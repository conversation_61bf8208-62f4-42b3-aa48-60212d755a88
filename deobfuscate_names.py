#!/usr/bin/env python3
"""
反混淆工具 - 分析混淆后的变量和函数命名，并尝试重命名为人类可理解的名字
"""

import re
import json
import os
from pathlib import Path
from collections import defaultdict, Counter

class NameDeobfuscator:
    def __init__(self):
        # 已知的API和函数映射
        self.known_mappings = {
            # Object 方法
            'defineProperty': 'defineProperty',
            'getOwnPropertyDescriptor': 'getOwnPropertyDescriptor', 
            'getOwnPropertyNames': 'getOwnPropertyNames',
            'hasOwnProperty': 'hasOwnProperty',
            'getPrototypeOf': 'getPrototypeOf',
            'create': 'objectCreate',
            
            # 常见模式
            'w': 'moduleWrapper',
            'J1': 'importHelper',
            'Nu': 'defineGetters',
            'Uz1': 'lazyEvaluator',
            'D1': 'requireFunction',
            
            # Sentry 相关
            'SentryError': 'SentryError',
            'GLOBAL_OBJ': 'globalObject',
            'getGlobalObject': 'getGlobalObject',
            'getGlobalSingleton': 'getGlobalSingleton',
            
            # DOM 相关
            'isDOMError': 'isDOMError',
            'isDOMException': 'isDOMException',
            'isElement': 'isElement',
            'isError': 'isError',
            'isErrorEvent': 'isErrorEvent',
            'isEvent': 'isEvent',
            
            # 工具函数
            'isInstanceOf': 'isInstanceOf',
            'isNaN': 'isNaN',
            'isString': 'isString',
            'isThenable': 'isThenable',
            'normalize': 'normalize',
            'truncate': 'truncate',
            
            # 文件路径相关
            'basename': 'basename',
            'dirname': 'dirname',
            'isAbsolute': 'isAbsolute',
            'join': 'pathJoin',
            'resolve': 'pathResolve',
            
            # Promise 相关
            'makePromiseBuffer': 'makePromiseBuffer',
            'SyncPromise': 'SyncPromise',
            
            # 时间相关
            'timestampInSeconds': 'timestampInSeconds',
            'dateTimestampInSeconds': 'dateTimestampInSeconds',
            
            # 网络相关
            'addFetchInstrumentationHandler': 'addFetchInstrumentationHandler',
            'addXhrInstrumentationHandler': 'addXhrInstrumentationHandler',
            
            # 控制台相关
            'CONSOLE_LEVELS': 'consoleLevels',
            'consoleSandbox': 'consoleSandbox',
            'logger': 'logger',
        }
        
        # 变量命名模式
        self.naming_patterns = {
            # 单字母变量通常的含义
            'A': 'target',
            'B': 'source', 
            'Q': 'options',
            'I': 'index',
            'G': 'key',
            'D': 'data',
            'Z': 'result',
            'Y': 'value',
            'W': 'wrapper',
            'F': 'function',
            'J': 'item',
            'C': 'context',
            'X': 'extra',
            'V': 'variable',
            'N': 'name',
            'E': 'element',
            'R': 'reference',
            'S': 'state',
            'T': 'type',
            'U': 'utility',
            'P': 'parameter',
            'L': 'length',
            'M': 'method',
            'K': 'constant',
            'H': 'handler',
        }
        
        # 模块名称模式
        self.module_patterns = {
            # 错误处理相关
            r'.*error.*': 'ErrorHandler',
            r'.*sentry.*': 'SentryModule',
            r'.*exception.*': 'ExceptionHandler',
            
            # 文件系统相关
            r'.*file.*': 'FileSystem',
            r'.*path.*': 'PathUtils',
            r'.*fs.*': 'FileSystem',
            
            # 网络相关
            r'.*fetch.*': 'FetchModule',
            r'.*xhr.*': 'XhrModule',
            r'.*http.*': 'HttpModule',
            
            # AI相关
            r'.*ai.*': 'AIModule',
            r'.*claude.*': 'ClaudeModule',
            r'.*model.*': 'ModelModule',
            
            # 工具相关
            r'.*util.*': 'UtilityModule',
            r'.*helper.*': 'HelperModule',
        }
        
        # 函数名称模式
        self.function_patterns = {
            r'^get.*': 'getter',
            r'^set.*': 'setter', 
            r'^is.*': 'predicate',
            r'^has.*': 'checker',
            r'^add.*': 'adder',
            r'^remove.*': 'remover',
            r'^create.*': 'creator',
            r'^make.*': 'factory',
            r'^parse.*': 'parser',
            r'^format.*': 'formatter',
            r'^normalize.*': 'normalizer',
            r'^validate.*': 'validator',
            r'^handle.*': 'handler',
            r'^process.*': 'processor',
        }

    def analyze_variable_usage(self, content):
        """分析变量使用模式"""
        # 提取所有变量声明
        var_declarations = re.findall(r'var\s+(\w+)\s*=', content)
        let_declarations = re.findall(r'let\s+(\w+)\s*=', content)
        const_declarations = re.findall(r'const\s+(\w+)\s*=', content)
        
        all_vars = var_declarations + let_declarations + const_declarations
        
        # 分析变量使用频率
        var_usage = Counter()
        for var in all_vars:
            var_usage[var] += content.count(var)
        
        return var_usage

    def identify_function_purpose(self, func_name, func_body):
        """根据函数体识别函数用途"""
        func_body_lower = func_body.lower()
        
        # 检查常见模式
        if 'error' in func_body_lower or 'exception' in func_body_lower:
            return 'errorHandler'
        elif 'fetch' in func_body_lower or 'xhr' in func_body_lower:
            return 'networkHandler'
        elif 'file' in func_body_lower or 'path' in func_body_lower:
            return 'fileHandler'
        elif 'dom' in func_body_lower or 'element' in func_body_lower:
            return 'domHandler'
        elif 'promise' in func_body_lower or 'async' in func_body_lower:
            return 'asyncHandler'
        elif 'console' in func_body_lower or 'log' in func_body_lower:
            return 'logHandler'
        elif 'validate' in func_body_lower or 'check' in func_body_lower:
            return 'validator'
        elif 'parse' in func_body_lower or 'format' in func_body_lower:
            return 'formatter'
        elif 'normalize' in func_body_lower:
            return 'normalizer'
        elif 'create' in func_body_lower or 'make' in func_body_lower:
            return 'factory'
        else:
            return 'utility'

    def suggest_variable_name(self, var_name, context=''):
        """为变量建议更好的名称"""
        # 检查已知映射
        if var_name in self.known_mappings:
            return self.known_mappings[var_name]
        
        # 检查单字母变量
        if len(var_name) == 1 and var_name in self.naming_patterns:
            return self.naming_patterns[var_name]
        
        # 分析变量名模式
        if re.match(r'^[A-Z]\d+$', var_name):  # 如 A1, B2
            base = var_name[0]
            if base in self.naming_patterns:
                return f"{self.naming_patterns[base]}{var_name[1:]}"
        
        if re.match(r'^[a-z]+\d+$', var_name):  # 如 abc1, def2
            return f"{var_name[:-1]}_{var_name[-1]}"
        
        # 根据上下文推断
        context_lower = context.lower()
        if 'error' in context_lower:
            return f"error_{var_name}"
        elif 'file' in context_lower:
            return f"file_{var_name}"
        elif 'network' in context_lower:
            return f"net_{var_name}"
        
        return var_name

    def deobfuscate_file(self, file_path):
        """反混淆单个文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析变量使用
        var_usage = self.analyze_variable_usage(content)
        
        # 创建重命名映射
        rename_map = {}
        
        # 处理模块包装器
        content = re.sub(r'var\s+(\w+)\s*=\s*w\(', 
                        lambda m: f"var {self.suggest_module_name(m.group(1))} = moduleWrapper(",
                        content)
        
        # 处理Object方法重命名
        object_methods = {
            'defineProperty': 'defineProperty',
            'getOwnPropertyDescriptor': 'getOwnPropertyDescriptor',
            'getOwnPropertyNames': 'getOwnPropertyNames',
            'getPrototypeOf': 'getPrototypeOf',
            'create': 'objectCreate'
        }
        
        for original, readable in object_methods.items():
            pattern = rf'(\w+):\s*{original}'
            content = re.sub(pattern, rf'{readable}: {original}', content)
        
        return content, rename_map

    def suggest_module_name(self, module_name):
        """为模块建议更好的名称"""
        # 检查已知映射
        if module_name in self.known_mappings:
            return self.known_mappings[module_name]
        
        # 根据模式匹配
        for pattern, suggestion in self.module_patterns.items():
            if re.match(pattern, module_name.lower()):
                return f"{suggestion}_{module_name}"
        
        # 默认处理
        if len(module_name) <= 3:
            return f"Module_{module_name}"
        
        return module_name

def main():
    deobfuscator = NameDeobfuscator()
    
    # 创建输出目录
    output_dir = Path('deobfuscated_modules')
    output_dir.mkdir(exist_ok=True)
    
    print("开始反混淆处理...")
    
    # 处理header文件
    header_path = 'extracted_modules/header.js'
    if os.path.exists(header_path):
        print("处理header文件...")
        deobfuscated_content, rename_map = deobfuscator.deobfuscate_file(header_path)
        
        with open(output_dir / 'header_deobfuscated.js', 'w', encoding='utf-8') as f:
            f.write(deobfuscated_content)
    
    # 处理一些示例模块
    sample_modules = ['QJ.js', 'tA.js', 'Mz1.js', 'CX.js', 'Tz1.js']
    
    for module_name in sample_modules:
        module_path = f'extracted_modules/{module_name}'
        if os.path.exists(module_path):
            print(f"处理模块: {module_name}")
            deobfuscated_content, rename_map = deobfuscator.deobfuscate_file(module_path)
            
            output_file = output_dir / f'{module_name.replace(".js", "_deobfuscated.js")}'
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(deobfuscated_content)
    
    # 创建命名映射文档
    create_naming_guide(output_dir, deobfuscator)
    
    print(f"反混淆完成！输出目录: {output_dir}")

def create_naming_guide(output_dir, deobfuscator):
    """创建命名指南文档"""
    guide_path = output_dir / 'NAMING_GUIDE.md'
    
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write("# 变量和函数命名指南\n\n")
        f.write("## 混淆模式分析\n\n")
        
        f.write("### 1. 单字母变量含义\n\n")
        f.write("| 混淆名 | 推测含义 | 说明 |\n")
        f.write("|--------|----------|------|\n")
        for var, meaning in deobfuscator.naming_patterns.items():
            f.write(f"| {var} | {meaning} | 常用于函数参数 |\n")
        
        f.write("\n### 2. 已知API映射\n\n")
        f.write("| 混淆名 | 实际含义 |\n")
        f.write("|--------|----------|\n")
        for obf, real in deobfuscator.known_mappings.items():
            f.write(f"| {obf} | {real} |\n")
        
        f.write("\n### 3. 模块命名模式\n\n")
        f.write("- **w**: 模块包装器函数\n")
        f.write("- **J1**: ES6导入辅助函数\n")
        f.write("- **Nu**: 属性getter定义函数\n")
        f.write("- **Uz1**: 延迟求值函数\n")
        f.write("- **D1**: require函数\n")
        
        f.write("\n### 4. 函数模式识别\n\n")
        f.write("- **get开头**: getter函数\n")
        f.write("- **is开头**: 布尔判断函数\n")
        f.write("- **add开头**: 添加操作函数\n")
        f.write("- **create/make开头**: 工厂函数\n")
        f.write("- **parse开头**: 解析函数\n")
        f.write("- **normalize开头**: 标准化函数\n")
        
        f.write("\n### 5. 上下文推断规则\n\n")
        f.write("- 包含'error'关键字 → 错误处理相关\n")
        f.write("- 包含'file'关键字 → 文件操作相关\n")
        f.write("- 包含'fetch/xhr'关键字 → 网络请求相关\n")
        f.write("- 包含'dom/element'关键字 → DOM操作相关\n")
        f.write("- 包含'promise/async'关键字 → 异步操作相关\n")

if __name__ == "__main__":
    main()
