#!/usr/bin/env python3
"""
Claude AI Integration Deobfuscation Script

This script deobfuscates the Claude AI integration code based on the analysis
in CLAUDE_AI_INTEGRATION_ANALYSIS.md. It focuses on the core AI functions
and makes them more readable.
"""

import re
import os
import json
from pathlib import Path

class ClaudeAIDeobfuscator:
    def __init__(self):
        # Function name mappings based on analysis
        self.function_mappings = {
            # Core API functions
            'KD': 'callClaudeAPI',
            'OW2': 'streamClaudeAPI', 
            'pX1': 'mainConversationLoop',
            'ht': 'coreConversationHandler',
            'S$2': 'nonInteractiveSessionHandler',
            'cX1': 'executeSingleTool',
            'XA5': 'executeToolWithValidation',
            'FA5': 'executeToolBlocks',
            'JA5': 'executeNonReadOnlyTools',
            'CA5': 'executeReadOnlyTools',
            
            # Message and context functions
            'ct6': 'convertMessagesToAPIFormat',
            'ut6': 'formatUserMessage',
            'pt6': 'formatAssistantMessage',
            'TW2': 'buildSystemPromptWithCaching',
            'M2': 'createMessage',
            'HC': 'processMessageHistory',
            'XF2': 'compressMessages',
            
            # Tool and permission functions
            'Ru0': 'validateToolPermissions',
            'ZY': 'getBetaFeatures',
            'x70': 'getCLISystemPrompt',
            'Ou0': 'validateSystemPrompt',
            
            # Model and configuration
            'it6': 'getModelTokenLimit',
            'yP': 'getCurrentModel',
            'cQ': 'getDefaultModel',
            'xP': 'parseUserSpecifiedModel',
            
            # Memory and context management
            'QJ2': 'saveMemory',
            'xR': 'getMemoryFilePath',
            'ut': 'readFileContent',
            'nX1': 'createLocalMemoryFile',
            'eF2': 'getMemoryUpdatePrompt',
            
            # Error handling and utilities
            'g1': 'logError',
            'j1': 'logMetric',
            'pr': 'createErrorResponse',
            'VA5': 'formatErrorMessage',
            'KA5': 'extractErrorDetails',
            'rF2': 'formatValidationError',
            
            # Stream and response handling
            'Su0': 'createStreamResponse',
            'Uu0': 'createProgressUpdate',
            'Nu0': 'createCancelledResponse',
            'UX1': 'createOffSwitchError',
            'PW2': 'handleRefusalResponse',
            
            # Authentication and configuration
            'gm9': 'API_ENDPOINTS',
            'N6': 'isDebugMode',
            'eH': 'getFeatureFlag',
            '_31': 'isModelOffSwitchEnabled',
            
            # Utility functions
            'Kp1': 'withRetry',
            'lt6': 'makeAPIRequest',
            'MX1': 'streamAPIRequest',
            'Ut': 'executeWithRetry',
            'ir': 'awaitResult',
            'Hp1': 'withErrorHandling'
        }
        
        # Parameter name mappings (common patterns)
        self.param_mappings = {
            'A': 'messages',
            'B': 'systemPrompt', 
            'Q': 'userPrompt',
            'I': 'tools',
            'G': 'signal',
            'D': 'options',
            'Z': 'temperature',
            'Y': 'response',
            'W': 'content',
            'F': 'enableCaching',
            'J': 'toolChoice',
            'C': 'processedMessages',
            'X': 'wasCompacted',
            'V': 'iterator',
            'K': 'result',
            'U': 'streamResponse',
            'N': 'skipPermissionCheck',
            'q': 'toolUses',
            'M': 'toolResults',
            'R': 'sortedResults',
            'T': 'queuedCommands',
            'O': 'error',
            'S': 'toolResult',
            'f': 'tool',
            'a': 'isReadOnly',
            'g': 'validationResult'
        }
        
        # Variable name mappings
        self.variable_mappings = {
            'l_': 'ENABLE_PROMPT_CACHING',
            'LW2': 'DEFAULT_TEMPERATURE',
            'wt': 'OFF_SWITCH_ERROR_MESSAGE',
            'gY': 'REFUSAL_PREFIX',
            '_Y': 'CANCELLED_MESSAGE',
            'ZR': 'CANCELLED_RESULT',
            'QG': 'CancellationError',
            'bP': 'ProcessError'
        }

    def deobfuscate_function_names(self, content):
        """Replace obfuscated function names with meaningful ones"""
        for obfuscated, readable in self.function_mappings.items():
            # Match function declarations
            pattern = rf'\bfunction\s+{re.escape(obfuscated)}\s*\('
            replacement = f'function {readable}('
            content = re.sub(pattern, replacement, content)
            
            # Match function calls
            pattern = rf'\b{re.escape(obfuscated)}\s*\('
            replacement = f'{readable}('
            content = re.sub(pattern, replacement, content)
            
            # Match async function declarations
            pattern = rf'\basync\s+function\s+{re.escape(obfuscated)}\s*\('
            replacement = f'async function {readable}('
            content = re.sub(pattern, replacement, content)
            
        return content

    def deobfuscate_parameters(self, content):
        """Replace single-letter parameters with meaningful names"""
        # More sophisticated parameter replacement that considers context

        def replace_function_params(match):
            func_signature = match.group(0)
            # Extract function name to provide context-specific parameter names
            func_name_match = re.search(r'function\s+(\w+)', func_signature)
            if func_name_match:
                func_name = func_name_match.group(1)
                # Use context-specific parameter names based on function
                if 'API' in func_name or 'claude' in func_name.lower():
                    context_params = {
                        'A': 'messages', 'B': 'systemPrompt', 'Q': 'userPrompt',
                        'I': 'tools', 'G': 'signal', 'D': 'options'
                    }
                elif 'tool' in func_name.lower() or 'execute' in func_name.lower():
                    context_params = {
                        'A': 'tool', 'B': 'toolUseId', 'Q': 'input',
                        'I': 'context', 'G': 'permissionHandler', 'D': 'message'
                    }
                elif 'conversation' in func_name.lower() or 'loop' in func_name.lower():
                    context_params = {
                        'A': 'messages', 'B': 'systemPrompt', 'Q': 'userPrompt',
                        'I': 'tools', 'G': 'permissionHandler', 'D': 'context'
                    }
                else:
                    context_params = self.param_mappings

                for short, long in context_params.items():
                    func_signature = re.sub(rf'\b{short}\b(?=\s*[,)])', long, func_signature)

            return func_signature

        # Replace in function declarations with context awareness
        content = re.sub(r'(async\s+)?function\s+\w+\s*\([^)]*\)', replace_function_params, content)

        return content

    def deobfuscate_variables(self, content):
        """Replace obfuscated variable names with meaningful ones"""
        for obfuscated, readable in self.variable_mappings.items():
            pattern = rf'\b{re.escape(obfuscated)}\b'
            content = re.sub(pattern, readable, content)
        
        return content

    def add_comments_and_structure(self, content):
        """Add comments and improve code structure"""
        # Add header comment
        header = '''/**
 * Claude AI Integration - Deobfuscated Code
 *
 * This file contains the core AI integration logic for Claude Code CLI.
 * Functions have been renamed for clarity and comments added for understanding.
 *
 * Key components:
 * - API calling functions (callClaudeAPI, streamClaudeAPI)
 * - Conversation loop management (mainConversationLoop, coreConversationHandler)
 * - Tool execution and validation
 * - Message formatting and context management
 * - Error handling and retry logic
 */

'''

        # Add detailed function comments based on analysis
        function_comments = {
            'callClaudeAPI': '''/**
 * Core Claude API calling function
 * Handles system prompts, user prompts, and assistant prompts
 * Supports prompt caching, temperature control, and signal interruption
 */''',
            'streamClaudeAPI': '''/**
 * Streaming Claude API function for real-time responses
 * Implements tool calling, error handling, and retry logic
 * Tracks cost and performance metrics
 */''',
            'mainConversationLoop': '''/**
 * Main conversation loop entry point
 * Filters out stream events and request start events
 * Delegates to core conversation handler
 */''',
            'coreConversationHandler': '''/**
 * Core conversation processing logic
 * Handles message compression, tool execution, and context management
 * Manages conversation state and error recovery
 */''',
            'executeSingleTool': '''/**
 * Executes a single tool with validation and error handling
 * Checks tool availability, validates input, and processes results
 * Handles cancellation and permission checking
 */''',
            'executeToolWithValidation': '''/**
 * Tool execution with comprehensive validation
 * Validates input schema, checks permissions, and handles errors
 * Tracks execution metrics and performance
 */''',
            'formatErrorMessage': '''/**
 * Formats error messages for display
 * Handles different error types and truncates long messages
 * Provides user-friendly error descriptions
 */''',
            'saveMemory': '''/**
 * Saves user memory to appropriate memory file
 * Supports different memory types (User, Local, Project, Managed)
 * Uses AI to intelligently update memory content
 */''',
        }

        # Add function-specific comments
        for func_name, comment in function_comments.items():
            pattern = rf'(async\s+function\*?\s+{re.escape(func_name)})'
            replacement = f'{comment}\n\\1'
            content = re.sub(pattern, replacement, content, count=1)

        # Add section comments before major function groups
        sections = [
            ('// === CORE API FUNCTIONS ===', 'async function callClaudeAPI'),
            ('// === CONVERSATION MANAGEMENT ===', 'async function* mainConversationLoop'),
            ('// === TOOL EXECUTION ===', 'async function* executeSingleTool'),
            ('// === MESSAGE FORMATTING ===', 'function convertMessagesToAPIFormat'),
            ('// === ERROR HANDLING ===', 'function formatErrorMessage'),
            ('// === MEMORY MANAGEMENT ===', 'async function saveMemory'),
        ]

        for comment, function_start in sections:
            pattern = rf'({re.escape(function_start)})'
            replacement = f'{comment}\n\n\\1'
            content = re.sub(pattern, replacement, content, count=1)

        return header + content

    def format_code(self, content):
        """Improve code formatting"""
        # Add proper spacing around operators
        content = re.sub(r'([^=!<>])=([^=])', r'\1 = \2', content)
        content = re.sub(r'([^=!<>])==([^=])', r'\1 == \2', content)
        content = re.sub(r'([^=!<>])!=([^=])', r'\1 != \2', content)
        
        # Add spacing around commas
        content = re.sub(r',([^\s])', r', \1', content)
        
        # Improve bracket spacing
        content = re.sub(r'\{([^\s])', r'{ \1', content)
        content = re.sub(r'([^\s])\}', r'\1 }', content)
        
        return content

    def deobfuscate_file(self, input_path, output_path):
        """Deobfuscate a single file"""
        print(f"Deobfuscating {input_path} -> {output_path}")
        
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply deobfuscation steps
        content = self.deobfuscate_function_names(content)
        content = self.deobfuscate_parameters(content)
        content = self.deobfuscate_variables(content)
        content = self.add_comments_and_structure(content)
        content = self.format_code(content)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Deobfuscated file saved to {output_path}")

    def create_deobfuscation_summary(self, output_dir):
        """Create a comprehensive summary of the deobfuscation process"""
        summary_content = f"""# Claude AI Integration - Deobfuscation Summary

## Overview

This document summarizes the deobfuscation process applied to the Claude Code CLI's AI integration modules. The original obfuscated code has been transformed to improve readability and understanding.

## Deobfuscation Process

### 1. Function Name Mapping
The following obfuscated function names were replaced with meaningful names:

| Obfuscated | Deobfuscated | Purpose |
|------------|--------------|---------|
| KD | callClaudeAPI | Core Claude API calling function |
| OW2 | streamClaudeAPI | Streaming API with tool integration |
| pX1 | mainConversationLoop | Main conversation loop entry |
| ht | coreConversationHandler | Core conversation processing |
| cX1 | executeSingleTool | Single tool execution |
| XA5 | executeToolWithValidation | Tool execution with validation |
| ct6 | convertMessagesToAPIFormat | Message format conversion |
| TW2 | buildSystemPromptWithCaching | System prompt with caching |
| QJ2 | saveMemory | Memory management function |

### 2. Variable Name Mapping
Key obfuscated variables were replaced:

| Obfuscated | Deobfuscated | Purpose |
|------------|--------------|---------|
| l_ | ENABLE_PROMPT_CACHING | Prompt caching flag |
| LW2 | DEFAULT_TEMPERATURE | Default API temperature |
| gY | REFUSAL_PREFIX | Refusal message prefix |
| _Y | CANCELLED_MESSAGE | Cancellation message |
| QG | CancellationError | Cancellation error class |

### 3. Parameter Improvements
Single-letter parameters were replaced with descriptive names based on context:
- API functions: messages, systemPrompt, userPrompt, tools, signal, options
- Tool functions: tool, toolUseId, input, context, permissionHandler, message
- Conversation functions: messages, systemPrompt, userPrompt, tools, permissionHandler, context

### 4. Code Structure Improvements
- Added comprehensive function documentation
- Organized code into logical sections
- Improved formatting and spacing
- Added inline comments for complex logic

## Key Findings

### Core Architecture
The Claude AI integration follows a sophisticated architecture:

1. **API Layer**: Direct communication with Claude API
   - `callClaudeAPI`: Simple API calls
   - `streamClaudeAPI`: Streaming responses with tool support

2. **Conversation Management**: Handles conversation flow
   - `mainConversationLoop`: Entry point for conversations
   - `coreConversationHandler`: Core processing logic
   - Message compression and context management

3. **Tool Execution**: Comprehensive tool calling system
   - Input validation and schema checking
   - Permission management and security
   - Error handling and retry logic

4. **Memory System**: Intelligent memory management
   - Multiple memory types (User, Local, Project, Managed)
   - AI-powered memory updates
   - File-based persistence

### Security Features
- Comprehensive permission checking
- Input validation and sanitization
- Sandboxed tool execution
- User confirmation workflows

### Performance Optimizations
- Message compression for long conversations
- Prompt caching for efficiency
- Streaming responses for real-time interaction
- Cost and performance tracking

## File Descriptions

### chunk_094.js - Core API Functions
Contains the fundamental API calling logic:
- Claude API communication
- Streaming response handling
- Error handling and retry logic
- Cost tracking and metrics

### chunk_097.js - Conversation Management
Manages the conversation flow:
- Main conversation loop
- Tool execution orchestration
- Message processing and formatting
- Memory management integration

### chunk_102.js - Session Management
Handles session-level functionality:
- Session configuration
- Non-interactive session handling
- User preferences and settings

### chunk_087.js - Authentication & Configuration
Manages authentication and configuration:
- OAuth and API key handling
- Model selection and configuration
- Environment variable processing

### chunk_093.js - MCP & External Tools
Handles external tool integration:
- Model Context Protocol (MCP) support
- External tool discovery and management
- Tool permission and security

## Usage Notes

The deobfuscated code provides insight into:
- How Claude Code CLI integrates with the Claude API
- The sophisticated tool calling and permission system
- Memory management and context handling
- Error handling and user experience considerations

This analysis can be valuable for:
- Understanding Claude integration patterns
- Learning about AI assistant architecture
- Studying tool calling and permission systems
- Exploring conversation management techniques

## Technical Insights

### Message Flow
1. User input → Message formatting
2. System prompt construction → API call
3. Streaming response processing → Tool detection
4. Tool execution → Permission checking
5. Result integration → Context update
6. Continue conversation loop

### Tool Execution Pipeline
1. Tool detection in AI response
2. Input validation and schema checking
3. Permission verification
4. Tool execution with monitoring
5. Result formatting and integration
6. Error handling and recovery

### Memory Management
1. Memory type determination (User/Local/Project/Managed)
2. File path resolution
3. AI-powered content updates
4. File persistence and backup

This deobfuscation reveals the sophisticated engineering behind Claude Code CLI's AI integration.
"""

        summary_path = os.path.join(output_dir, 'DEOBFUSCATION_SUMMARY.md')
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)

        print(f"Deobfuscation summary saved to {summary_path}")

    def deobfuscate_core_files(self):
        """Deobfuscate the core AI integration files"""
        core_files = [
            'cli_split/chunk_094.js',  # API calling and streaming
            'cli_split/chunk_097.js',  # Main conversation loop
            'cli_split/chunk_102.js',  # Session management
            'cli_split/chunk_087.js',  # Authentication and config
            'cli_split/chunk_093.js',  # MCP and external tools
        ]

        output_dir = 'deobfuscated_claude_ai'

        for file_path in core_files:
            if os.path.exists(file_path):
                filename = os.path.basename(file_path)
                output_path = os.path.join(output_dir, filename)
                self.deobfuscate_file(file_path, output_path)
            else:
                print(f"Warning: File {file_path} not found")

        # Create comprehensive summary
        self.create_deobfuscation_summary(output_dir)

def main():
    """Main function to run the deobfuscation"""
    print("Claude AI Integration Deobfuscation Tool")
    print("=" * 50)
    
    deobfuscator = ClaudeAIDeobfuscator()
    deobfuscator.deobfuscate_core_files()
    
    print("\nDeobfuscation complete!")
    print("Check the 'deobfuscated_claude_ai' directory for results.")

if __name__ == "__main__":
    main()
