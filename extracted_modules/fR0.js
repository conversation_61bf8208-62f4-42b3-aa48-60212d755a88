// Module: fR0
// Lines: 163535-163558
// Purpose: utility
// Dependencies: G, B, Object, MS, hasOwnProperty

var fR0 = w((MS) => {
  var UA6 = MS && MS.__createBinding || (Object.create ? function(A, B, Q, I) {
      if (I === void 0) I = Q;
      var G = Object.getOwnPropertyDescriptor(B, Q);
      if (!G || ("get" in G ? !B.__esModule : G.writable || G.configurable)) G = {
        enumerable: !0,
        get: function() {
          return B[Q]
        }
      };
      Object.defineProperty(A, I, G)
    } : function(A, B, Q, I) {
      if (I === void 0) I = Q;
      A[I] = B[Q]
    }),
    NA6 = MS && MS.__exportStar || function(A, B) {
      for (var Q in A)
        if (Q !== "default" && !Object.prototype.hasOwnProperty.call(B, Q)) UA6(B, A, Q)
    };
  Object.defineProperty(MS, "__esModule", {
    value: !0
  });
  NA6(xR0(), MS)
});