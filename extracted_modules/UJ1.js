// Module: uJ1
// Lines: 207460-207486
// Purpose: networking
// Dependencies: Object, pB2, Xm6, Cm6, LB2, Vm6, sl1, Lo

var uJ1 = w((Lo) => {
  Object.defineProperty(Lo, "__esModule", {
    value: !0
  });
  Lo.convertLegacyHttpOptions = Lo.getSharedConfigurationFromEnvironment = Lo.createOtlpHttpExportDelegate = void 0;
  var Cm6 = LB2();
  Object.defineProperty(Lo, "createOtlpHttpExportDelegate", {
    enumerable: !0,
    get: function() {
      return Cm6.createOtlpHttpExportDelegate
    }
  });
  var Xm6 = sl1();
  Object.defineProperty(Lo, "getSharedConfigurationFromEnvironment", {
    enumerable: !0,
    get: function() {
      return Xm6.getSharedConfigurationFromEnvironment
    }
  });
  var Vm6 = pB2();
  Object.defineProperty(Lo, "convertLegacyHttpOptions", {
    enumerable: !0,
    get: function() {
      return Vm6.convertLegacyHttpOptions
    }
  })
});