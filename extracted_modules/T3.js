// Module: T3
// Lines: 79359-80236
// Purpose: error_tracking, file_operations, networking, command_line, version_control
// Dependencies: console, BD4, G, J, Math, ZD4, constructor, I, el, Z, FS1, String, ZU, Object, jG4, C, YD4, Int8Array, eP1, A, Symbol, JSON, this, Date, B, TP, U, Array, Int16Array, K, Int32Array, Y, krA, ErA, Number, BS1, DD4, ID4, Q

var T3 = w((jp5, krA) => {
  var {
    defineProperty: uB1,
    getOwnPropertyDescriptor: SG4,
    getOwnPropertyNames: _G4
  } = Object, jG4 = Object.prototype.hasOwnProperty, a0 = (A, B) => uB1(A, "name", {
    value: B,
    configurable: !0
  }), yG4 = (A, B) => {
    for (var Q in B) uB1(A, Q, {
      get: B[Q],
      enumerable: !0
    })
  }, kG4 = (A, B, Q, I) => {
    if (B && typeof B === "object" || typeof B === "function") {
      for (let G of _G4(B))
        if (!jG4.call(A, G) && G !== Q) uB1(A, G, {
          get: () => B[G],
          enumerable: !(I = SG4(B, G)) || I.enumerable
        })
    }
    return A
  }, xG4 = (A) => kG4(uB1({}, "__esModule", {
    value: !0
  }), A), wrA = {};
  yG4(wrA, {
    Client: () => fG4,
    Command: () => UrA,
    LazyJsonString: () => TP,
    NoOpLogger: () => jD4,
    SENSITIVE_STRING: () => bG4,
    ServiceException: () => ED4,
    _json: () => DS1,
    collectBody: () => eP1.collectBody,
    convertMap: () => yD4,
    createAggregatedClient: () => gG4,
    dateToUtcString: () => RrA,
    decorateServiceException: () => OrA,
    emitWarningIfUnsupportedVersion: () => qD4,
    expectBoolean: () => mG4,
    expectByte: () => GS1,
    expectFloat32: () => mB1,
    expectInt: () => uG4,
    expectInt32: () => QS1,
    expectLong: () => tl,
    expectNonNull: () => cG4,
    expectNumber: () => ol,
    expectObject: () => NrA,
    expectShort: () => IS1,
    expectString: () => lG4,
    expectUnion: () => iG4,
    extendedEncodeURIComponent: () => eP1.extendedEncodeURIComponent,
    getArrayIfSingleItem: () => SD4,
    getDefaultClientConfiguration: () => TD4,
    getDefaultExtensionConfiguration: () => PrA,
    getValueFromTextNode: () => SrA,
    handleFloat: () => sG4,
    isSerializableHeaderValue: () => _D4,
    limitedParseDouble: () => WS1,
    limitedParseFloat: () => rG4,
    limitedParseFloat32: () => oG4,
    loadConfigsForDefaultMode: () => $D4,
    logger: () => el,
    map: () => JS1,
    parseBoolean: () => hG4,
    parseEpochTimestamp: () => FD4,
    parseRfc3339DateTime: () => QD4,
    parseRfc3339DateTimeWithOffset: () => GD4,
    parseRfc7231DateTime: () => WD4,
    quoteHeader: () => jrA,
    resolveDefaultRuntimeConfig: () => PD4,
    resolvedPath: () => eP1.resolvedPath,
    serializeDateTime: () => gD4,
    serializeFloat: () => bD4,
    splitEvery: () => yrA,
    splitHeader: () => hD4,
    strictParseByte: () => LrA,
    strictParseDouble: () => YS1,
    strictParseFloat: () => nG4,
    strictParseFloat32: () => $rA,
    strictParseInt: () => tG4,
    strictParseInt32: () => eG4,
    strictParseLong: () => MrA,
    strictParseShort: () => of,
    take: () => kD4,
    throwDefaultError: () => TrA,
    withBaseException: () => UD4
  });
  krA.exports = xG4(wrA);
  var ErA = ZU(),
    fG4 = class {
      constructor(A) {
        this.config = A, this.middlewareStack = ErA.constructStack()
      }
      static {
        a0(this, "Client")
      }
      send(A, B, Q) {
        let I = typeof B !== "function" ? B : void 0,
          G = typeof B === "function" ? B : Q,
          D = I === void 0 && this.config.cacheMiddleware === !0,
          Z;
        if (D) {
          if (!this.handlers) this.handlers = new WeakMap;
          let Y = this.handlers;
          if (Y.has(A.constructor)) Z = Y.get(A.constructor);
          else Z = A.resolveMiddleware(this.middlewareStack, this.config, I), Y.set(A.constructor, Z)
        } else delete this.handlers, Z = A.resolveMiddleware(this.middlewareStack, this.config, I);
        if (G) Z(A).then((Y) => G(null, Y.output), (Y) => G(Y)).catch(() => {});
        else return Z(A).then((Y) => Y.output)
      }
      destroy() {
        this.config?.requestHandler?.destroy?.(), delete this.handlers
      }
    },
    eP1 = bH(),
    BS1 = tP1(),
    UrA = class {
      constructor() {
        this.middlewareStack = ErA.constructStack()
      }
      static {
        a0(this, "Command")
      }
      static classBuilder() {
        return new vG4
      }
      resolveMiddlewareWithContext(A, B, Q, {
        middlewareFn: I,
        clientName: G,
        commandName: D,
        inputFilterSensitiveLog: Z,
        outputFilterSensitiveLog: Y,
        smithyContext: W,
        additionalContext: F,
        CommandCtor: J
      }) {
        for (let U of I.bind(this)(J, A, B, Q)) this.middlewareStack.use(U);
        let C = A.concat(this.middlewareStack),
          {
            logger: X
          } = B,
          V = {
            logger: X,
            clientName: G,
            commandName: D,
            inputFilterSensitiveLog: Z,
            outputFilterSensitiveLog: Y,
            [BS1.SMITHY_CONTEXT_KEY]: {
              commandInstance: this,
              ...W
            },
            ...F
          },
          {
            requestHandler: K
          } = B;
        return C.resolve((U) => K.handle(U.request, Q || {}), V)
      }
    },
    vG4 = class {
      constructor() {
        this._init = () => {}, this._ep = {}, this._middlewareFn = () => [], this._commandName = "", this
          ._clientName = "", this._additionalContext = {}, this._smithyContext = {}, this
          ._inputFilterSensitiveLog = (A) => A, this._outputFilterSensitiveLog = (A) => A, this._serializer =
          null, this._deserializer = null
      }
      static {
        a0(this, "ClassBuilder")
      }
      init(A) {
        this._init = A
      }
      ep(A) {
        return this._ep = A, this
      }
      m(A) {
        return this._middlewareFn = A, this
      }
      s(A, B, Q = {}) {
        return this._smithyContext = {
          service: A,
          operation: B,
          ...Q
        }, this
      }
      c(A = {}) {
        return this._additionalContext = A, this
      }
      n(A, B) {
        return this._clientName = A, this._commandName = B, this
      }
      f(A = (Q) => Q, B = (Q) => Q) {
        return this._inputFilterSensitiveLog = A, this._outputFilterSensitiveLog = B, this
      }
      ser(A) {
        return this._serializer = A, this
      }
      de(A) {
        return this._deserializer = A, this
      }
      build() {
        let A = this,
          B;
        return B = class extends UrA {
          constructor(...[Q]) {
            super();
            this.serialize = A._serializer, this.deserialize = A._deserializer, this.input = Q ?? {}, A._init(
              this)
          }
          static {
            a0(this, "CommandRef")
          }
          static getEndpointParameterInstructions() {
            return A._ep
          }
          resolveMiddleware(Q, I, G) {
            return this.resolveMiddlewareWithContext(Q, I, G, {
              CommandCtor: B,
              middlewareFn: A._middlewareFn,
              clientName: A._clientName,
              commandName: A._commandName,
              inputFilterSensitiveLog: A._inputFilterSensitiveLog,
              outputFilterSensitiveLog: A._outputFilterSensitiveLog,
              smithyContext: A._smithyContext,
              additionalContext: A._additionalContext
            })
          }
        }
      }
    },
    bG4 = "***SensitiveInformation***",
    gG4 = a0((A, B) => {
      for (let Q of Object.keys(A)) {
        let I = A[Q],
          G = a0(async function(Z, Y, W) {
            let F = new I(Z);
            if (typeof Y === "function") this.send(F, Y);
            else if (typeof W === "function") {
              if (typeof Y !== "object") throw new Error(`Expected http options but got ${typeof Y}`);
              this.send(F, Y || {}, W)
            } else return this.send(F, Y)
          }, "methodImpl"),
          D = (Q[0].toLowerCase() + Q.slice(1)).replace(/Command$/, "");
        B.prototype[D] = G
      }
    }, "createAggregatedClient"),
    hG4 = a0((A) => {
      switch (A) {
        case "true":
          return !0;
        case "false":
          return !1;
        default:
          throw new Error(`Unable to parse boolean value "${A}"`)
      }
    }, "parseBoolean"),
    mG4 = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "number") {
        if (A === 0 || A === 1) el.warn(dB1(`Expected boolean, got ${typeof A}: ${A}`));
        if (A === 0) return !1;
        if (A === 1) return !0
      }
      if (typeof A === "string") {
        let B = A.toLowerCase();
        if (B === "false" || B === "true") el.warn(dB1(`Expected boolean, got ${typeof A}: ${A}`));
        if (B === "false") return !1;
        if (B === "true") return !0
      }
      if (typeof A === "boolean") return A;
      throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)
    }, "expectBoolean"),
    ol = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "string") {
        let B = parseFloat(A);
        if (!Number.isNaN(B)) {
          if (String(B) !== String(A)) el.warn(dB1(`Expected number but observed string: ${A}`));
          return B
        }
      }
      if (typeof A === "number") return A;
      throw new TypeError(`Expected number, got ${typeof A}: ${A}`)
    }, "expectNumber"),
    dG4 = Math.ceil(340282346638528860000000000000000000000),
    mB1 = a0((A) => {
      let B = ol(A);
      if (B !== void 0 && !Number.isNaN(B) && B !== 1 / 0 && B !== -1 / 0) {
        if (Math.abs(B) > dG4) throw new TypeError(`Expected 32-bit float, got ${A}`)
      }
      return B
    }, "expectFloat32"),
    tl = a0((A) => {
      if (A === null || A === void 0) return;
      if (Number.isInteger(A) && !Number.isNaN(A)) return A;
      throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)
    }, "expectLong"),
    uG4 = tl,
    QS1 = a0((A) => ZS1(A, 32), "expectInt32"),
    IS1 = a0((A) => ZS1(A, 16), "expectShort"),
    GS1 = a0((A) => ZS1(A, 8), "expectByte"),
    ZS1 = a0((A, B) => {
      let Q = tl(A);
      if (Q !== void 0 && pG4(Q, B) !== Q) throw new TypeError(`Expected ${B}-bit integer, got ${A}`);
      return Q
    }, "expectSizedInt"),
    pG4 = a0((A, B) => {
      switch (B) {
        case 32:
          return Int32Array.of(A)[0];
        case 16:
          return Int16Array.of(A)[0];
        case 8:
          return Int8Array.of(A)[0]
      }
    }, "castInt"),
    cG4 = a0((A, B) => {
      if (A === null || A === void 0) {
        if (B) throw new TypeError(`Expected a non-null value for ${B}`);
        throw new TypeError("Expected a non-null value")
      }
      return A
    }, "expectNonNull"),
    NrA = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "object" && !Array.isArray(A)) return A;
      let B = Array.isArray(A) ? "array" : typeof A;
      throw new TypeError(`Expected object, got ${B}: ${A}`)
    }, "expectObject"),
    lG4 = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "string") return A;
      if (["boolean", "number", "bigint"].includes(typeof A)) return el.warn(dB1(
        `Expected string, got ${typeof A}: ${A}`)), String(A);
      throw new TypeError(`Expected string, got ${typeof A}: ${A}`)
    }, "expectString"),
    iG4 = a0((A) => {
      if (A === null || A === void 0) return;
      let B = NrA(A),
        Q = Object.entries(B).filter(([, I]) => I != null).map(([I]) => I);
      if (Q.length === 0) throw new TypeError("Unions must have exactly one non-null member. None were found.");
      if (Q.length > 1) throw new TypeError(
        `Unions must have exactly one non-null member. Keys ${Q} were not null.`);
      return B
    }, "expectUnion"),
    YS1 = a0((A) => {
      if (typeof A == "string") return ol(ef(A));
      return ol(A)
    }, "strictParseDouble"),
    nG4 = YS1,
    $rA = a0((A) => {
      if (typeof A == "string") return mB1(ef(A));
      return mB1(A)
    }, "strictParseFloat32"),
    aG4 = /(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,
    ef = a0((A) => {
      let B = A.match(aG4);
      if (B === null || B[0].length !== A.length) throw new TypeError("Expected real number, got implicit NaN");
      return parseFloat(A)
    }, "parseNumber"),
    WS1 = a0((A) => {
      if (typeof A == "string") return qrA(A);
      return ol(A)
    }, "limitedParseDouble"),
    sG4 = WS1,
    rG4 = WS1,
    oG4 = a0((A) => {
      if (typeof A == "string") return qrA(A);
      return mB1(A)
    }, "limitedParseFloat32"),
    qrA = a0((A) => {
      switch (A) {
        case "NaN":
          return NaN;
        case "Infinity":
          return 1 / 0;
        case "-Infinity":
          return -1 / 0;
        default:
          throw new Error(`Unable to parse float value: ${A}`)
      }
    }, "parseFloatString"),
    MrA = a0((A) => {
      if (typeof A === "string") return tl(ef(A));
      return tl(A)
    }, "strictParseLong"),
    tG4 = MrA,
    eG4 = a0((A) => {
      if (typeof A === "string") return QS1(ef(A));
      return QS1(A)
    }, "strictParseInt32"),
    of = a0((A) => {
      if (typeof A === "string") return IS1(ef(A));
      return IS1(A)
    }, "strictParseShort"),
    LrA = a0((A) => {
      if (typeof A === "string") return GS1(ef(A));
      return GS1(A)
    }, "strictParseByte"),
    dB1 = a0((A) => {
      return String(new TypeError(A).stack || A).split(`
`).slice(0, 5).filter((B) => !B.includes("stackTraceWarning")).join(`
`)
    }, "stackTraceWarning"),
    el = {
      warn: console.warn
    },
    AD4 = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
    FS1 = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

  function RrA(A) {
    let B = A.getUTCFullYear(),
      Q = A.getUTCMonth(),
      I = A.getUTCDay(),
      G = A.getUTCDate(),
      D = A.getUTCHours(),
      Z = A.getUTCMinutes(),
      Y = A.getUTCSeconds(),
      W = G < 10 ? `0${G}` : `${G}`,
      F = D < 10 ? `0${D}` : `${D}`,
      J = Z < 10 ? `0${Z}` : `${Z}`,
      C = Y < 10 ? `0${Y}` : `${Y}`;
    return `${AD4[I]}, ${W} ${FS1[Q]} ${B} ${F}:${J}:${C} GMT`
  }
  a0(RrA, "dateToUtcString");
  var BD4 = new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),
    QD4 = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A !== "string") throw new TypeError("RFC-3339 date-times must be expressed as strings");
      let B = BD4.exec(A);
      if (!B) throw new TypeError("Invalid RFC-3339 date-time value");
      let [Q, I, G, D, Z, Y, W, F] = B, J = of(tf(I)), C = iH(G, "month", 1, 12), X = iH(D, "day", 1, 31);
      return rl(J, C, X, {
        hours: Z,
        minutes: Y,
        seconds: W,
        fractionalMilliseconds: F
      })
    }, "parseRfc3339DateTime"),
    ID4 = new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),
    GD4 = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A !== "string") throw new TypeError("RFC-3339 date-times must be expressed as strings");
      let B = ID4.exec(A);
      if (!B) throw new TypeError("Invalid RFC-3339 date-time value");
      let [Q, I, G, D, Z, Y, W, F, J] = B, C = of(tf(I)), X = iH(G, "month", 1, 12), V = iH(D, "day", 1, 31), K =
        rl(C, X, V, {
          hours: Z,
          minutes: Y,
          seconds: W,
          fractionalMilliseconds: F
        });
      if (J.toUpperCase() != "Z") K.setTime(K.getTime() - wD4(J));
      return K
    }, "parseRfc3339DateTimeWithOffset"),
    DD4 = new RegExp(
      /^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/
      ),
    ZD4 = new RegExp(
      /^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/
      ),
    YD4 = new RegExp(
      /^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/
      ),
    WD4 = a0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A !== "string") throw new TypeError("RFC-7231 date-times must be expressed as strings");
      let B = DD4.exec(A);
      if (B) {
        let [Q, I, G, D, Z, Y, W, F] = B;
        return rl(of(tf(D)), AS1(G), iH(I, "day", 1, 31), {
          hours: Z,
          minutes: Y,
          seconds: W,
          fractionalMilliseconds: F
        })
      }
      if (B = ZD4.exec(A), B) {
        let [Q, I, G, D, Z, Y, W, F] = B;
        return XD4(rl(JD4(D), AS1(G), iH(I, "day", 1, 31), {
          hours: Z,
          minutes: Y,
          seconds: W,
          fractionalMilliseconds: F
        }))
      }
      if (B = YD4.exec(A), B) {
        let [Q, I, G, D, Z, Y, W, F] = B;
        return rl(of(tf(F)), AS1(I), iH(G.trimLeft(), "day", 1, 31), {
          hours: D,
          minutes: Z,
          seconds: Y,
          fractionalMilliseconds: W
        })
      }
      throw new TypeError("Invalid RFC-7231 date-time value")
    }, "parseRfc7231DateTime"),
    FD4 = a0((A) => {
      if (A === null || A === void 0) return;
      let B;
      if (typeof A === "number") B = A;
      else if (typeof A === "string") B = YS1(A);
      else if (typeof A === "object" && A.tag === 1) B = A.value;
      else throw new TypeError(
        "Epoch timestamps must be expressed as floating point numbers or their string representation");
      if (Number.isNaN(B) || B === 1 / 0 || B === -1 / 0) throw new TypeError(
        "Epoch timestamps must be valid, non-Infinite, non-NaN numerics");
      return new Date(Math.round(B * 1000))
    }, "parseEpochTimestamp"),
    rl = a0((A, B, Q, I) => {
      let G = B - 1;
      return KD4(A, G, Q), new Date(Date.UTC(A, G, Q, iH(I.hours, "hour", 0, 23), iH(I.minutes, "minute", 0, 59),
        iH(I.seconds, "seconds", 0, 60), zD4(I.fractionalMilliseconds)))
    }, "buildDate"),
    JD4 = a0((A) => {
      let B = new Date().getUTCFullYear(),
        Q = Math.floor(B / 100) * 100 + of(tf(A));
      if (Q < B) return Q + 100;
      return Q
    }, "parseTwoDigitYear"),
    CD4 = 1576800000000,
    XD4 = a0((A) => {
      if (A.getTime() - new Date().getTime() > CD4) return new Date(Date.UTC(A.getUTCFullYear() - 100, A
        .getUTCMonth(), A.getUTCDate(), A.getUTCHours(), A.getUTCMinutes(), A.getUTCSeconds(), A
        .getUTCMilliseconds()));
      return A
    }, "adjustRfc850Year"),
    AS1 = a0((A) => {
      let B = FS1.indexOf(A);
      if (B < 0) throw new TypeError(`Invalid month: ${A}`);
      return B + 1
    }, "parseMonthByShortName"),
    VD4 = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
    KD4 = a0((A, B, Q) => {
      let I = VD4[B];
      if (B === 1 && HD4(A)) I = 29;
      if (Q > I) throw new TypeError(`Invalid day for ${FS1[B]} in ${A}: ${Q}`)
    }, "validateDayOfMonth"),
    HD4 = a0((A) => {
      return A % 4 === 0 && (A % 100 !== 0 || A % 400 === 0)
    }, "isLeapYear"),
    iH = a0((A, B, Q, I) => {
      let G = LrA(tf(A));
      if (G < Q || G > I) throw new TypeError(`${B} must be between ${Q} and ${I}, inclusive`);
      return G
    }, "parseDateValue"),
    zD4 = a0((A) => {
      if (A === null || A === void 0) return 0;
      return $rA("0." + A) * 1000
    }, "parseMilliseconds"),
    wD4 = a0((A) => {
      let B = A[0],
        Q = 1;
      if (B == "+") Q = 1;
      else if (B == "-") Q = -1;
      else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);
      let I = Number(A.substring(1, 3)),
        G = Number(A.substring(4, 6));
      return Q * (I * 60 + G) * 60 * 1000
    }, "parseOffsetToMilliseconds"),
    tf = a0((A) => {
      let B = 0;
      while (B < A.length - 1 && A.charAt(B) === "0") B++;
      if (B === 0) return A;
      return A.slice(B)
    }, "stripLeadingZeroes"),
    ED4 = class A extends Error {
      static {
        a0(this, "ServiceException")
      }
      constructor(B) {
        super(B.message);
        Object.setPrototypeOf(this, Object.getPrototypeOf(this).constructor.prototype), this.name = B.name, this
          .$fault = B.$fault, this.$metadata = B.$metadata
      }
      static isInstance(B) {
        if (!B) return !1;
        let Q = B;
        return A.prototype.isPrototypeOf(Q) || Boolean(Q.$fault) && Boolean(Q.$metadata) && (Q.$fault ===
          "client" || Q.$fault === "server")
      }
      static[Symbol.hasInstance](B) {
        if (!B) return !1;
        let Q = B;
        if (this === A) return A.isInstance(B);
        if (A.isInstance(B)) {
          if (Q.name && this.name) return this.prototype.isPrototypeOf(B) || Q.name === this.name;
          return this.prototype.isPrototypeOf(B)
        }
        return !1
      }
    },
    OrA = a0((A, B = {}) => {
      Object.entries(B).filter(([, I]) => I !== void 0).forEach(([I, G]) => {
        if (A[I] == null || A[I] === "") A[I] = G
      });
      let Q = A.message || A.Message || "UnknownError";
      return A.message = Q, delete A.Message, A
    }, "decorateServiceException"),
    TrA = a0(({
      output: A,
      parsedBody: B,
      exceptionCtor: Q,
      errorCode: I
    }) => {
      let G = ND4(A),
        D = G.httpStatusCode ? G.httpStatusCode + "" : void 0,
        Z = new Q({
          name: B?.code || B?.Code || I || D || "UnknownError",
          $fault: "client",
          $metadata: G
        });
      throw OrA(Z, B)
    }, "throwDefaultError"),
    UD4 = a0((A) => {
      return ({
        output: B,
        parsedBody: Q,
        errorCode: I
      }) => {
        TrA({
          output: B,
          parsedBody: Q,
          exceptionCtor: A,
          errorCode: I
        })
      }
    }, "withBaseException"),
    ND4 = a0((A) => ({
      httpStatusCode: A.statusCode,
      requestId: A.headers["x-amzn-requestid"] ?? A.headers["x-amzn-request-id"] ?? A.headers[
        "x-amz-request-id"],
      extendedRequestId: A.headers["x-amz-id-2"],
      cfId: A.headers["x-amz-cf-id"]
    }), "deserializeMetadata"),
    $D4 = a0((A) => {
      switch (A) {
        case "standard":
          return {
            retryMode: "standard", connectionTimeout: 3100
          };
        case "in-region":
          return {
            retryMode: "standard", connectionTimeout: 1100
          };
        case "cross-region":
          return {
            retryMode: "standard", connectionTimeout: 3100
          };
        case "mobile":
          return {
            retryMode: "standard", connectionTimeout: 30000
          };
        default:
          return {}
      }
    }, "loadConfigsForDefaultMode"),
    zrA = !1,
    qD4 = a0((A) => {
      if (A && !zrA && parseInt(A.substring(1, A.indexOf("."))) < 16) zrA = !0
    }, "emitWarningIfUnsupportedVersion"),
    MD4 = a0((A) => {
      let B = [];
      for (let Q in BS1.AlgorithmId) {
        let I = BS1.AlgorithmId[Q];
        if (A[I] === void 0) continue;
        B.push({
          algorithmId: () => I,
          checksumConstructor: () => A[I]
        })
      }
      return {
        addChecksumAlgorithm(Q) {
          B.push(Q)
        },
        checksumAlgorithms() {
          return B
        }
      }
    }, "getChecksumConfiguration"),
    LD4 = a0((A) => {
      let B = {};
      return A.checksumAlgorithms().forEach((Q) => {
        B[Q.algorithmId()] = Q.checksumConstructor()
      }), B
    }, "resolveChecksumRuntimeConfig"),
    RD4 = a0((A) => {
      return {
        setRetryStrategy(B) {
          A.retryStrategy = B
        },
        retryStrategy() {
          return A.retryStrategy
        }
      }
    }, "getRetryConfiguration"),
    OD4 = a0((A) => {
      let B = {};
      return B.retryStrategy = A.retryStrategy(), B
    }, "resolveRetryRuntimeConfig"),
    PrA = a0((A) => {
      return Object.assign(MD4(A), RD4(A))
    }, "getDefaultExtensionConfiguration"),
    TD4 = PrA,
    PD4 = a0((A) => {
      return Object.assign(LD4(A), OD4(A))
    }, "resolveDefaultRuntimeConfig"),
    SD4 = a0((A) => Array.isArray(A) ? A : [A], "getArrayIfSingleItem"),
    SrA = a0((A) => {
      for (let Q in A)
        if (A.hasOwnProperty(Q) && A[Q]["#text"] !== void 0) A[Q] = A[Q]["#text"];
        else if (typeof A[Q] === "object" && A[Q] !== null) A[Q] = SrA(A[Q]);
      return A
    }, "getValueFromTextNode"),
    _D4 = a0((A) => {
      return A != null
    }, "isSerializableHeaderValue"),
    TP = a0(function A(B) {
      return Object.assign(new String(B), {
        deserializeJSON() {
          return JSON.parse(String(B))
        },
        toString() {
          return String(B)
        },
        toJSON() {
          return String(B)
        }
      })
    }, "LazyJsonString");
  TP.from = (A) => {
    if (A && typeof A === "object" && (A instanceof TP || ("deserializeJSON" in A))) return A;
    else if (typeof A === "string" || Object.getPrototypeOf(A) === String.prototype) return TP(String(A));
    return TP(JSON.stringify(A))
  };
  TP.fromObject = TP.from;
  var jD4 = class {
    static {
      a0(this, "NoOpLogger")
    }
    trace() {}
    debug() {}
    info() {}
    warn() {}
    error() {}
  };

  function JS1(A, B, Q) {
    let I, G, D;
    if (typeof B === "undefined" && typeof Q === "undefined") I = {}, D = A;
    else if (I = A, typeof B === "function") return G = B, D = Q, xD4(I, G, D);
    else D = B;
    for (let Z of Object.keys(D)) {
      if (!Array.isArray(D[Z])) {
        I[Z] = D[Z];
        continue
      }
      _rA(I, null, D, Z)
    }
    return I
  }
  a0(JS1, "map");
  var yD4 = a0((A) => {
      let B = {};
      for (let [Q, I] of Object.entries(A || {})) B[Q] = [, I];
      return B
    }, "convertMap"),
    kD4 = a0((A, B) => {
      let Q = {};
      for (let I in B) _rA(Q, A, B, I);
      return Q
    }, "take"),
    xD4 = a0((A, B, Q) => {
      return JS1(A, Object.entries(Q).reduce((I, [G, D]) => {
        if (Array.isArray(D)) I[G] = D;
        else if (typeof D === "function") I[G] = [B, D()];
        else I[G] = [B, D];
        return I
      }, {}))
    }, "mapWithFilter"),
    _rA = a0((A, B, Q, I) => {
      if (B !== null) {
        let Z = Q[I];
        if (typeof Z === "function") Z = [, Z];
        let [Y = fD4, W = vD4, F = I] = Z;
        if (typeof Y === "function" && Y(B[F]) || typeof Y !== "function" && !!Y) A[I] = W(B[F]);
        return
      }
      let [G, D] = Q[I];
      if (typeof D === "function") {
        let Z, Y = G === void 0 && (Z = D()) != null,
          W = typeof G === "function" && !!G(void 0) || typeof G !== "function" && !!G;
        if (Y) A[I] = Z;
        else if (W) A[I] = D()
      } else {
        let Z = G === void 0 && D != null,
          Y = typeof G === "function" && !!G(D) || typeof G !== "function" && !!G;
        if (Z || Y) A[I] = D
      }
    }, "applyInstruction"),
    fD4 = a0((A) => A != null, "nonNullish"),
    vD4 = a0((A) => A, "pass");

  function jrA(A) {
    if (A.includes(",") || A.includes('"')) A = `"${A.replace(/"/g,"\\\"")}"`;
    return A
  }
  a0(jrA, "quoteHeader");
  var bD4 = a0((A) => {
      if (A !== A) return "NaN";
      switch (A) {
        case 1 / 0:
          return "Infinity";
        case -1 / 0:
          return "-Infinity";
        default:
          return A
      }
    }, "serializeFloat"),
    gD4 = a0((A) => A.toISOString().replace(".000Z", "Z"), "serializeDateTime"),
    DS1 = a0((A) => {
      if (A == null) return {};
      if (Array.isArray(A)) return A.filter((B) => B != null).map(DS1);
      if (typeof A === "object") {
        let B = {};
        for (let Q of Object.keys(A)) {
          if (A[Q] == null) continue;
          B[Q] = DS1(A[Q])
        }
        return B
      }
      return A
    }, "_json");

  function yrA(A, B, Q) {
    if (Q <= 0 || !Number.isInteger(Q)) throw new Error("Invalid number of delimiters (" + Q + ") for splitEvery.");
    let I = A.split(B);
    if (Q === 1) return I;
    let G = [],
      D = "";
    for (let Z = 0; Z < I.length; Z++) {
      if (D === "") D = I[Z];
      else D += B + I[Z];
      if ((Z + 1) % Q === 0) G.push(D), D = ""
    }
    if (D !== "") G.push(D);
    return G
  }
  a0(yrA, "splitEvery");
  var hD4 = a0((A) => {
    let B = A.length,
      Q = [],
      I = !1,
      G = void 0,
      D = 0;
    for (let Z = 0; Z < B; ++Z) {
      let Y = A[Z];
      switch (Y) {
        case '"':
          if (G !== "\\") I = !I;
          break;
        case ",":
          if (!I) Q.push(A.slice(D, Z)), D = Z + 1;
          break;
        default:
      }
      G = Y
    }
    return Q.push(A.slice(D)), Q.map((Z) => {
      Z = Z.trim();
      let Y = Z.length;
      if (Y < 2) return Z;
      if (Z[0] === '"' && Z[Y - 1] === '"') Z = Z.slice(1, Y - 1);
      return Z.replace(/\\"/g, '"')
    })
  }, "splitHeader")
});