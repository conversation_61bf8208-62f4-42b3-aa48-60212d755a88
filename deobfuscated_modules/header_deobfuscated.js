#!/usr/bin/env -S node --no-warnings --enable-source-maps

/**
 * Claude Code CLI - A Beta product by Anthropic
 *
 * By using Claude Code, you agree that all code acceptance or rejection decisions you make,
 * and the associated conversations in context, constitute Feedback under Anthropic's Commercial Terms,
 * and may be used to improve Anthropic's products, including training models.
 * You are responsible for reviewing any code suggestions before use.
 *
 * (c) Anthropic PBC. All rights reserved.
 * Use is subject to Anthropic's Commercial Terms of Service
 * (https://www.anthropic.com/legal/commercial-terms).
 *
 * Version: 1.0.3
 */

import {
  createRequire as Zq2
} from "node:module";
var Qq2 = Object.create;
var {
  getPrototypeOf: Iq2,
  defineProperty: Ez1,
  getOwnPropertyNames: Gq2
} = Object;
var Dq2 = Object.prototype.hasOwnProperty;
var J1 = (A, B, Q) => {
  Q = A != null ? Qq2(Iq2(A)) : {};
  let I = B || !A || !A.__esModule ? Ez1(Q, "default", {
    value: A,
    enumerable: !0
  }) : Q;
  for (let G of Gq2(A))
    if (!Dq2.call(I, G)) Ez1(I, G, {
      get: () => A[G],
      enumerable: !0
    });
  return I
};
var w = (A, B) => () => (B || A((B = {
  exports: {}
}).exports, B), B.exports);
var Nu = (A, B) => {
  for (var Q in B) Ez1(A, Q, {
    get: B[Q],
    enumerable: !0,
    configurable: !0,
    set: (I) => B[Q] = () => I
  })
};
var Uz1 = (A, B) => () => (A && (B = A(A = 0)), B);
var D1 = Zq2(import.meta.url);