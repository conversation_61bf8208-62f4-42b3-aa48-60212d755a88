# 变量和函数命名指南

## 混淆模式分析

### 1. 单字母变量含义

| 混淆名 | 推测含义 | 说明 |
|--------|----------|------|
| A | target | 常用于函数参数 |
| B | source | 常用于函数参数 |
| Q | options | 常用于函数参数 |
| I | index | 常用于函数参数 |
| G | key | 常用于函数参数 |
| D | data | 常用于函数参数 |
| Z | result | 常用于函数参数 |
| Y | value | 常用于函数参数 |
| W | wrapper | 常用于函数参数 |
| F | function | 常用于函数参数 |
| J | item | 常用于函数参数 |
| C | context | 常用于函数参数 |
| X | extra | 常用于函数参数 |
| V | variable | 常用于函数参数 |
| N | name | 常用于函数参数 |
| E | element | 常用于函数参数 |
| R | reference | 常用于函数参数 |
| S | state | 常用于函数参数 |
| T | type | 常用于函数参数 |
| U | utility | 常用于函数参数 |
| P | parameter | 常用于函数参数 |
| L | length | 常用于函数参数 |
| M | method | 常用于函数参数 |
| K | constant | 常用于函数参数 |
| H | handler | 常用于函数参数 |

### 2. 已知API映射

| 混淆名 | 实际含义 |
|--------|----------|
| defineProperty | defineProperty |
| getOwnPropertyDescriptor | getOwnPropertyDescriptor |
| getOwnPropertyNames | getOwnPropertyNames |
| hasOwnProperty | hasOwnProperty |
| getPrototypeOf | getPrototypeOf |
| create | objectCreate |
| w | moduleWrapper |
| J1 | importHelper |
| Nu | defineGetters |
| Uz1 | lazyEvaluator |
| D1 | requireFunction |
| SentryError | SentryError |
| GLOBAL_OBJ | globalObject |
| getGlobalObject | getGlobalObject |
| getGlobalSingleton | getGlobalSingleton |
| isDOMError | isDOMError |
| isDOMException | isDOMException |
| isElement | isElement |
| isError | isError |
| isErrorEvent | isErrorEvent |
| isEvent | isEvent |
| isInstanceOf | isInstanceOf |
| isNaN | isNaN |
| isString | isString |
| isThenable | isThenable |
| normalize | normalize |
| truncate | truncate |
| basename | basename |
| dirname | dirname |
| isAbsolute | isAbsolute |
| join | pathJoin |
| resolve | pathResolve |
| makePromiseBuffer | makePromiseBuffer |
| SyncPromise | SyncPromise |
| timestampInSeconds | timestampInSeconds |
| dateTimestampInSeconds | dateTimestampInSeconds |
| addFetchInstrumentationHandler | addFetchInstrumentationHandler |
| addXhrInstrumentationHandler | addXhrInstrumentationHandler |
| CONSOLE_LEVELS | consoleLevels |
| consoleSandbox | consoleSandbox |
| logger | logger |

### 3. 模块命名模式

- **w**: 模块包装器函数
- **J1**: ES6导入辅助函数
- **Nu**: 属性getter定义函数
- **Uz1**: 延迟求值函数
- **D1**: require函数

### 4. 函数模式识别

- **get开头**: getter函数
- **is开头**: 布尔判断函数
- **add开头**: 添加操作函数
- **create/make开头**: 工厂函数
- **parse开头**: 解析函数
- **normalize开头**: 标准化函数

### 5. 上下文推断规则

- 包含'error'关键字 → 错误处理相关
- 包含'file'关键字 → 文件操作相关
- 包含'fetch/xhr'关键字 → 网络请求相关
- 包含'dom/element'关键字 → DOM操作相关
- 包含'promise/async'关键字 → 异步操作相关
