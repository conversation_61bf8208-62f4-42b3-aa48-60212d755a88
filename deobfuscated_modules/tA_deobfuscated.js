// Module: tA
// Lines: 3067-3289
// Purpose: error_tracking, file_operations, networking, command_line, ai_integration
// Dependencies: jS2, qw1, B0A, Rw1, nO, uS2, cS2, S2, yu, TS2, mS2, Object, hS2, Mw1, Oe1, rK, RS2, zA1, wA1, _u, Su, w1, EA1, Uy, dS2, pS2, lS2, Q0A, SS2, LS2, _S2, Lw1, Nw1, vG, OS2, NA1, UE, fS2, iO, xS2, NS2, I0A, ku, qS2, ju, UA1, vS2, gS2, MS2, kS2, bS2, yS2, PS2

var Module_tA = moduleWrapper((Rw1) => {
  Object.defineProperty(Rw1, "__esModule", {
    value: !0
  });
  var NS2 = Oe1(),
    zA1 = Mz1(),
    Nw1 = Tz1(),
    $S2 = Pz1(),
    $w1 = fG(),
    qS2 = T1A(),
    vG = QJ(),
    MS2 = y1A(),
    wA1 = CX(),
    LS2 = Qw1(),
    x$ = Mu(),
    qw1 = Bw1(),
    EA1 = Tu(),
    UE = IJ(),
    iO = p1A(),
    RS2 = i1A(),
    nO = BAA(),
    Mw1 = DAA(),
    Su = AA1(),
    _u = $u(),
    f$ = dz1(),
    Lw1 = Dw1(),
    ju = Fw1(),
    yu = EAA(),
    B0A = Aw1(),
    rK = Kw1(),
    OS2 = MAA(),
    ku = PAA(),
    Uy = Cw1(),
    UA1 = Yw1(),
    TS2 = yAA(),
    PS2 = xAA(),
    NA1 = gAA(),
    Q0A = mAA(),
    SS2 = pAA(),
    _S2 = iAA(),
    jS2 = Ew1(),
    yS2 = sAA(),
    kS2 = ww1(),
    xS2 = Uw1(),
    fS2 = tAA(),
    vS2 = kz1(),
    bS2 = gz1(),
    I0A = tz1(),
    gS2 = pz1(),
    hS2 = oz1(),
    mS2 = iz1(),
    dS2 = sz1(),
    uS2 = wE(),
    pS2 = e11(),
    cS2 = A0A(),
    lS2 = rz1();
  Rw1.applyAggregateErrorsToEvent = NS2.applyAggregateErrorsToEvent;
  Rw1.getComponentName = zA1.getComponentName;
  Rw1.getDomElement = zA1.getDomElement;
  Rw1.getLocationHref = zA1.getLocationHref;
  Rw1.htmlTreeAsString = zA1.htmlTreeAsString;
  Rw1.dsnFromString = Nw1.dsnFromString;
  Rw1.dsnToString = Nw1.dsnToString;
  Rw1.makeDsn = Nw1.makeDsn;
  Rw1.SentryError = $S2.SentryError;
  Rw1.GLOBAL_OBJ = $w1.GLOBAL_OBJ;
  Rw1.getGlobalObject = $w1.getGlobalObject;
  Rw1.getGlobalSingleton = $w1.getGlobalSingleton;
  Rw1.addInstrumentationHandler = qS2.addInstrumentationHandler;
  Rw1.isDOMError = vG.isDOMError;
  Rw1.isDOMException = vG.isDOMException;
  Rw1.isElement = vG.isElement;
  Rw1.isError = vG.isError;
  Rw1.isErrorEvent = vG.isErrorEvent;
  Rw1.isEvent = vG.isEvent;
  Rw1.isInstanceOf = vG.isInstanceOf;
  Rw1.isNaN = vG.isNaN;
  Rw1.isParameterizedString = vG.isParameterizedString;
  Rw1.isPlainObject = vG.isPlainObject;
  Rw1.isPrimitive = vG.isPrimitive;
  Rw1.isRegExp = vG.isRegExp;
  Rw1.isString = vG.isString;
  Rw1.isSyntheticEvent = vG.isSyntheticEvent;
  Rw1.isThenable = vG.isThenable;
  Rw1.isVueViewModel = vG.isVueViewModel;
  Rw1.isBrowser = MS2.isBrowser;
  Rw1.CONSOLE_LEVELS = wA1.CONSOLE_LEVELS;
  Rw1.consoleSandbox = wA1.consoleSandbox;
  Rw1.logger = wA1.logger;
  Rw1.originalConsoleMethods = wA1.originalConsoleMethods;
  Rw1.memoBuilder = LS2.memoBuilder;
  Rw1.addContextToFrame = x$.addContextToFrame;
  Rw1.addExceptionMechanism = x$.addExceptionMechanism;
  Rw1.addExceptionTypeValue = x$.addExceptionTypeValue;
  Rw1.arrayify = x$.arrayify;
  Rw1.checkOrSetAlreadyCaught = x$.checkOrSetAlreadyCaught;
  Rw1.getEventDescription = x$.getEventDescription;
  Rw1.parseSemver = x$.parseSemver;
  Rw1.uuid4 = x$.uuid4;
  Rw1.dynamicRequire = qw1.dynamicRequire;
  Rw1.isNodeEnv = qw1.isNodeEnv;
  Rw1.loadModule = qw1.loadModule;
  Rw1.normalize = EA1.normalize;
  Rw1.normalizeToSize = EA1.normalizeToSize;
  Rw1.normalizeUrlToBase = EA1.normalizeUrlToBase;
  Rw1.walk = EA1.walk;
  Rw1.addNonEnumerableProperty = UE.addNonEnumerableProperty;
  Rw1.convertToPlainObject = UE.convertToPlainObject;
  Rw1.dropUndefinedKeys = UE.dropUndefinedKeys;
  Rw1.extractExceptionKeysForMessage = UE.extractExceptionKeysForMessage;
  Rw1.fill = UE.fill;
  Rw1.getOriginalFunction = UE.getOriginalFunction;
  Rw1.markFunctionWrapped = UE.markFunctionWrapped;
  Rw1.objectify = UE.objectify;
  Rw1.urlEncode = UE.urlEncode;
  Rw1.basename = iO.basename;
  Rw1.dirname = iO.dirname;
  Rw1.isAbsolute = iO.isAbsolute;
  Rw1.join = iO.join;
  Rw1.normalizePath = iO.normalizePath;
  Rw1.relative = iO.relative;
  Rw1.resolve = iO.resolve;
  Rw1.makePromiseBuffer = RS2.makePromiseBuffer;
  Rw1.DEFAULT_USER_INCLUDES = nO.DEFAULT_USER_INCLUDES;
  Rw1.addRequestDataToEvent = nO.addRequestDataToEvent;
  Rw1.addRequestDataToTransaction = nO.addRequestDataToTransaction;
  Rw1.extractPathForTransaction = nO.extractPathForTransaction;
  Rw1.extractRequestData = nO.extractRequestData;
  Rw1.winterCGHeadersToDict = nO.winterCGHeadersToDict;
  Rw1.winterCGRequestToRequestData = nO.winterCGRequestToRequestData;
  Rw1.severityFromString = Mw1.severityFromString;
  Rw1.severityLevelFromString = Mw1.severityLevelFromString;
  Rw1.validSeverityLevels = Mw1.validSeverityLevels;
  Rw1.createStackParser = Su.createStackParser;
  Rw1.getFunctionName = Su.getFunctionName;
  Rw1.nodeStackLineParser = Su.nodeStackLineParser;
  Rw1.stackParserFromStackParserOptions = Su.stackParserFromStackParserOptions;
  Rw1.stripSentryFramesAndReverse = Su.stripSentryFramesAndReverse;
  Rw1.isMatchingPattern = _u.isMatchingPattern;
  Rw1.safeJoin = _u.safeJoin;
  Rw1.snipLine = _u.snipLine;
  Rw1.stringMatchesSomePattern = _u.stringMatchesSomePattern;
  Rw1.truncate = _u.truncate;
  Rw1.isNativeFetch = f$.isNativeFetch;
  Rw1.supportsDOMError = f$.supportsDOMError;
  Rw1.supportsDOMException = f$.supportsDOMException;
  Rw1.supportsErrorEvent = f$.supportsErrorEvent;
  Rw1.supportsFetch = f$.supportsFetch;
  Rw1.supportsNativeFetch = f$.supportsNativeFetch;
  Rw1.supportsReferrerPolicy = f$.supportsReferrerPolicy;
  Rw1.supportsReportingObserver = f$.supportsReportingObserver;
  Rw1.SyncPromise = Lw1.SyncPromise;
  Rw1.rejectedSyncPromise = Lw1.rejectedSyncPromise;
  Rw1.resolvedSyncPromise = Lw1.resolvedSyncPromise;
  Object.defineProperty(Rw1, "_browserPerformanceTimeOriginMode", {
    enumerable: !0,
    get: () => ju._browserPerformanceTimeOriginMode
  });
  Rw1.browserPerformanceTimeOrigin = ju.browserPerformanceTimeOrigin;
  Rw1.dateTimestampInSeconds = ju.dateTimestampInSeconds;
  Rw1.timestampInSeconds = ju.timestampInSeconds;
  Rw1.timestampWithMs = ju.timestampWithMs;
  Rw1.TRACEPARENT_REGEXP = yu.TRACEPARENT_REGEXP;
  Rw1.extractTraceparentData = yu.extractTraceparentData;
  Rw1.generateSentryTraceHeader = yu.generateSentryTraceHeader;
  Rw1.propagationContextFromHeaders = yu.propagationContextFromHeaders;
  Rw1.tracingContextFromHeaders = yu.tracingContextFromHeaders;
  Rw1.getSDKSource = B0A.getSDKSource;
  Rw1.isBrowserBundle = B0A.isBrowserBundle;
  Rw1.addItemToEnvelope = rK.addItemToEnvelope;
  Rw1.createAttachmentEnvelopeItem = rK.createAttachmentEnvelopeItem;
  Rw1.createEnvelope = rK.createEnvelope;
  Rw1.createEventEnvelopeHeaders = rK.createEventEnvelopeHeaders;
  Rw1.envelopeContainsItemType = rK.envelopeContainsItemType;
  Rw1.envelopeItemTypeToDataCategory = rK.envelopeItemTypeToDataCategory;
  Rw1.forEachEnvelopeItem = rK.forEachEnvelopeItem;
  Rw1.getSdkMetadataForEnvelopeHeader = rK.getSdkMetadataForEnvelopeHeader;
  Rw1.parseEnvelope = rK.parseEnvelope;
  Rw1.serializeEnvelope = rK.serializeEnvelope;
  Rw1.createClientReportEnvelope = OS2.createClientReportEnvelope;
  Rw1.DEFAULT_RETRY_AFTER = ku.DEFAULT_RETRY_AFTER;
  Rw1.disabledUntil = ku.disabledUntil;
  Rw1.isRateLimited = ku.isRateLimited;
  Rw1.parseRetryAfterHeader = ku.parseRetryAfterHeader;
  Rw1.updateRateLimits = ku.updateRateLimits;
  Rw1.BAGGAGE_HEADER_NAME = Uy.BAGGAGE_HEADER_NAME;
  Rw1.MAX_BAGGAGE_STRING_LENGTH = Uy.MAX_BAGGAGE_STRING_LENGTH;
  Rw1.SENTRY_BAGGAGE_KEY_PREFIX = Uy.SENTRY_BAGGAGE_KEY_PREFIX;
  Rw1.SENTRY_BAGGAGE_KEY_PREFIX_REGEX = Uy.SENTRY_BAGGAGE_KEY_PREFIX_REGEX;
  Rw1.baggageHeaderToDynamicSamplingContext = Uy.baggageHeaderToDynamicSamplingContext;
  Rw1.dynamicSamplingContextToSentryBaggageHeader = Uy.dynamicSamplingContextToSentryBaggageHeader;
  Rw1.getNumberOfUrlSegments = UA1.getNumberOfUrlSegments;
  Rw1.getSanitizedUrlString = UA1.getSanitizedUrlString;
  Rw1.parseUrl = UA1.parseUrl;
  Rw1.stripUrlQueryAndFragment = UA1.stripUrlQueryAndFragment;
  Rw1.addOrUpdateIntegration = TS2.addOrUpdateIntegration;
  Rw1.makeFifoCache = PS2.makeFifoCache;
  Rw1.eventFromMessage = NA1.eventFromMessage;
  Rw1.eventFromUnknownInput = NA1.eventFromUnknownInput;
  Rw1.exceptionFromError = NA1.exceptionFromError;
  Rw1.parseStackFrames = NA1.parseStackFrames;
  Rw1.callFrameToStackFrame = Q0A.callFrameToStackFrame;
  Rw1.watchdogTimer = Q0A.watchdogTimer;
  Rw1.LRUMap = SS2.LRUMap;
  Rw1._asyncNullishCoalesce = _S2._asyncNullishCoalesce;
  Rw1._asyncOptionalChain = jS2._asyncOptionalChain;
  Rw1._asyncOptionalChainDelete = yS2._asyncOptionalChainDelete;
  Rw1._nullishCoalesce = kS2._nullishCoalesce;
  Rw1._optionalChain = xS2._optionalChain;
  Rw1._optionalChainDelete = fS2._optionalChainDelete;
  Rw1.addConsoleInstrumentationHandler = vS2.addConsoleInstrumentationHandler;
  Rw1.addClickKeypressInstrumentationHandler = bS2.addClickKeypressInstrumentationHandler;
  Rw1.SENTRY_XHR_DATA_KEY = I0A.SENTRY_XHR_DATA_KEY;
  Rw1.addXhrInstrumentationHandler = I0A.addXhrInstrumentationHandler;
  Rw1.addFetchInstrumentationHandler = gS2.addFetchInstrumentationHandler;
  Rw1.addHistoryInstrumentationHandler = hS2.addHistoryInstrumentationHandler;
  Rw1.addGlobalErrorInstrumentationHandler = mS2.addGlobalErrorInstrumentationHandler;
  Rw1.addGlobalUnhandledRejectionInstrumentationHandler = dS2.addGlobalUnhandledRejectionInstrumentationHandler;
  Rw1.resetInstrumentationHandlers = uS2.resetInstrumentationHandlers;
  Rw1.filenameIsInApp = pS2.filenameIsInApp;
  Rw1.escapeStringForRegex = cS2.escapeStringForRegex;
  Rw1.supportsHistory = lS2.supportsHistory
});