// Chunk 70
// Lines 210001-213000
// Size: 96698 bytes

    }
    getPeer() {
      var A, B;
      return (B = (A = this.call) === null || A === void 0 ? void 0 : A.getPeer()) !== null && B !== void 0 ?
        B : "unknown"
    }
  }
  LQ2.ClientUnaryCallImpl = NQ2;
  class $Q2 extends Ni1.Readable {
    constructor(A) {
      super({
        objectMode: !0
      });
      this.deserialize = A
    }
    cancel() {
      var A;
      (A = this.call) === null || A === void 0 || A.cancelWithStatus(ko.Status.CANCELLED, "Cancelled on client")
    }
    getPeer() {
      var A, B;
      return (B = (A = this.call) === null || A === void 0 ? void 0 : A.getPeer()) !== null && B !== void 0 ?
        B : "unknown"
    }
    _read(A) {
      var B;
      (B = this.call) === null || B === void 0 || B.startRead()
    }
  }
  LQ2.ClientReadableStreamImpl = $Q2;
  class qQ2 extends Ni1.Writable {
    constructor(A) {
      super({
        objectMode: !0
      });
      this.serialize = A
    }
    cancel() {
      var A;
      (A = this.call) === null || A === void 0 || A.cancelWithStatus(ko.Status.CANCELLED, "Cancelled on client")
    }
    getPeer() {
      var A, B;
      return (B = (A = this.call) === null || A === void 0 ? void 0 : A.getPeer()) !== null && B !== void 0 ?
        B : "unknown"
    }
    _write(A, B, Q) {
      var I;
      let G = {
          callback: Q
        },
        D = Number(B);
      if (!Number.isNaN(D)) G.flags = D;
      (I = this.call) === null || I === void 0 || I.sendMessageWithContext(G, A)
    }
    _final(A) {
      var B;
      (B = this.call) === null || B === void 0 || B.halfClose(), A()
    }
  }
  LQ2.ClientWritableStreamImpl = qQ2;
  class MQ2 extends Ni1.Duplex {
    constructor(A, B) {
      super({
        objectMode: !0
      });
      this.serialize = A, this.deserialize = B
    }
    cancel() {
      var A;
      (A = this.call) === null || A === void 0 || A.cancelWithStatus(ko.Status.CANCELLED, "Cancelled on client")
    }
    getPeer() {
      var A, B;
      return (B = (A = this.call) === null || A === void 0 ? void 0 : A.getPeer()) !== null && B !== void 0 ?
        B : "unknown"
    }
    _read(A) {
      var B;
      (B = this.call) === null || B === void 0 || B.startRead()
    }
    _write(A, B, Q) {
      var I;
      let G = {
          callback: Q
        },
        D = Number(B);
      if (!Number.isNaN(D)) G.flags = D;
      (I = this.call) === null || I === void 0 || I.sendMessageWithContext(G, A)
    }
    _final(A) {
      var B;
      (B = this.call) === null || B === void 0 || B.halfClose(), A()
    }
  }
  LQ2.ClientDuplexStreamImpl = MQ2
});
var _Q2 = w((PQ2) => {
  Object.defineProperty(PQ2, "__esModule", {
    value: !0
  });
  PQ2.InterceptingListenerImpl = void 0;
  PQ2.isInterceptingListener = Vp6;

  function Vp6(A) {
    return A.onReceiveMetadata !== void 0 && A.onReceiveMetadata.length === 1
  }
  class TQ2 {
    constructor(A, B) {
      this.listener = A, this.nextListener = B, this.processingMetadata = !1, this.hasPendingMessage = !1, this
        .processingMessage = !1, this.pendingStatus = null
    }
    processPendingMessage() {
      if (this.hasPendingMessage) this.nextListener.onReceiveMessage(this.pendingMessage), this.pendingMessage =
        null, this.hasPendingMessage = !1
    }
    processPendingStatus() {
      if (this.pendingStatus) this.nextListener.onReceiveStatus(this.pendingStatus)
    }
    onReceiveMetadata(A) {
      this.processingMetadata = !0, this.listener.onReceiveMetadata(A, (B) => {
        this.processingMetadata = !1, this.nextListener.onReceiveMetadata(B), this.processPendingMessage(),
          this.processPendingStatus()
      })
    }
    onReceiveMessage(A) {
      this.processingMessage = !0, this.listener.onReceiveMessage(A, (B) => {
        if (this.processingMessage = !1, this.processingMetadata) this.pendingMessage = B, this
          .hasPendingMessage = !0;
        else this.nextListener.onReceiveMessage(B), this.processPendingStatus()
      })
    }
    onReceiveStatus(A) {
      this.listener.onReceiveStatus(A, (B) => {
        if (this.processingMetadata || this.processingMessage) this.pendingStatus = B;
        else this.nextListener.onReceiveStatus(B)
      })
    }
  }
  PQ2.InterceptingListenerImpl = TQ2
});
var Mi1 = w((hQ2) => {
  Object.defineProperty(hQ2, "__esModule", {
    value: !0
  });
  hQ2.InterceptingCall = hQ2.RequesterBuilder = hQ2.ListenerBuilder = hQ2.InterceptorConfigurationError = void 0;
  hQ2.getInterceptingCall = Ep6;
  var Hp6 = XD(),
    jQ2 = _Q2(),
    yQ2 = O6(),
    kQ2 = lJ1();
  class fo extends Error {
    constructor(A) {
      super(A);
      this.name = "InterceptorConfigurationError", Error.captureStackTrace(this, fo)
    }
  }
  hQ2.InterceptorConfigurationError = fo;
  class xQ2 {
    constructor() {
      this.metadata = void 0, this.message = void 0, this.status = void 0
    }
    withOnReceiveMetadata(A) {
      return this.metadata = A, this
    }
    withOnReceiveMessage(A) {
      return this.message = A, this
    }
    withOnReceiveStatus(A) {
      return this.status = A, this
    }
    build() {
      return {
        onReceiveMetadata: this.metadata,
        onReceiveMessage: this.message,
        onReceiveStatus: this.status
      }
    }
  }
  hQ2.ListenerBuilder = xQ2;
  class fQ2 {
    constructor() {
      this.start = void 0, this.message = void 0, this.halfClose = void 0, this.cancel = void 0
    }
    withStart(A) {
      return this.start = A, this
    }
    withSendMessage(A) {
      return this.message = A, this
    }
    withHalfClose(A) {
      return this.halfClose = A, this
    }
    withCancel(A) {
      return this.cancel = A, this
    }
    build() {
      return {
        start: this.start,
        sendMessage: this.message,
        halfClose: this.halfClose,
        cancel: this.cancel
      }
    }
  }
  hQ2.RequesterBuilder = fQ2;
  var $i1 = {
      onReceiveMetadata: (A, B) => {
        B(A)
      },
      onReceiveMessage: (A, B) => {
        B(A)
      },
      onReceiveStatus: (A, B) => {
        B(A)
      }
    },
    xo = {
      start: (A, B, Q) => {
        Q(A, B)
      },
      sendMessage: (A, B) => {
        B(A)
      },
      halfClose: (A) => {
        A()
      },
      cancel: (A) => {
        A()
      }
    };
  class vQ2 {
    constructor(A, B) {
      var Q, I, G, D;
      if (this.nextCall = A, this.processingMetadata = !1, this.pendingMessageContext = null, this
        .processingMessage = !1, this.pendingHalfClose = !1, B) this.requester = {
        start: (Q = B.start) !== null && Q !== void 0 ? Q : xo.start,
        sendMessage: (I = B.sendMessage) !== null && I !== void 0 ? I : xo.sendMessage,
        halfClose: (G = B.halfClose) !== null && G !== void 0 ? G : xo.halfClose,
        cancel: (D = B.cancel) !== null && D !== void 0 ? D : xo.cancel
      };
      else this.requester = xo
    }
    cancelWithStatus(A, B) {
      this.requester.cancel(() => {
        this.nextCall.cancelWithStatus(A, B)
      })
    }
    getPeer() {
      return this.nextCall.getPeer()
    }
    processPendingMessage() {
      if (this.pendingMessageContext) this.nextCall.sendMessageWithContext(this.pendingMessageContext, this
        .pendingMessage), this.pendingMessageContext = null, this.pendingMessage = null
    }
    processPendingHalfClose() {
      if (this.pendingHalfClose) this.nextCall.halfClose()
    }
    start(A, B) {
      var Q, I, G, D, Z, Y;
      let W = {
        onReceiveMetadata: (I = (Q = B === null || B === void 0 ? void 0 : B.onReceiveMetadata) === null ||
          Q === void 0 ? void 0 : Q.bind(B)) !== null && I !== void 0 ? I : (F) => {},
        onReceiveMessage: (D = (G = B === null || B === void 0 ? void 0 : B.onReceiveMessage) === null || G ===
          void 0 ? void 0 : G.bind(B)) !== null && D !== void 0 ? D : (F) => {},
        onReceiveStatus: (Y = (Z = B === null || B === void 0 ? void 0 : B.onReceiveStatus) === null || Z ===
          void 0 ? void 0 : Z.bind(B)) !== null && Y !== void 0 ? Y : (F) => {}
      };
      this.processingMetadata = !0, this.requester.start(A, W, (F, J) => {
        var C, X, V;
        this.processingMetadata = !1;
        let K;
        if (jQ2.isInterceptingListener(J)) K = J;
        else {
          let U = {
            onReceiveMetadata: (C = J.onReceiveMetadata) !== null && C !== void 0 ? C : $i1
              .onReceiveMetadata,
            onReceiveMessage: (X = J.onReceiveMessage) !== null && X !== void 0 ? X : $i1.onReceiveMessage,
            onReceiveStatus: (V = J.onReceiveStatus) !== null && V !== void 0 ? V : $i1.onReceiveStatus
          };
          K = new jQ2.InterceptingListenerImpl(U, W)
        }
        this.nextCall.start(F, K), this.processPendingMessage(), this.processPendingHalfClose()
      })
    }
    sendMessageWithContext(A, B) {
      this.processingMessage = !0, this.requester.sendMessage(B, (Q) => {
        if (this.processingMessage = !1, this.processingMetadata) this.pendingMessageContext = A, this
          .pendingMessage = B;
        else this.nextCall.sendMessageWithContext(A, Q), this.processPendingHalfClose()
      })
    }
    sendMessage(A) {
      this.sendMessageWithContext({}, A)
    }
    startRead() {
      this.nextCall.startRead()
    }
    halfClose() {
      this.requester.halfClose(() => {
        if (this.processingMetadata || this.processingMessage) this.pendingHalfClose = !0;
        else this.nextCall.halfClose()
      })
    }
  }
  hQ2.InterceptingCall = vQ2;

  function zp6(A, B, Q) {
    var I, G;
    let D = (I = Q.deadline) !== null && I !== void 0 ? I : 1 / 0,
      Z = Q.host,
      Y = (G = Q.parent) !== null && G !== void 0 ? G : null,
      W = Q.propagate_flags,
      F = Q.credentials,
      J = A.createCall(B, D, Z, Y, W);
    if (F) J.setCredentials(F);
    return J
  }
  class qi1 {
    constructor(A, B) {
      this.call = A, this.methodDefinition = B
    }
    cancelWithStatus(A, B) {
      this.call.cancelWithStatus(A, B)
    }
    getPeer() {
      return this.call.getPeer()
    }
    sendMessageWithContext(A, B) {
      let Q;
      try {
        Q = this.methodDefinition.requestSerialize(B)
      } catch (I) {
        this.call.cancelWithStatus(yQ2.Status.INTERNAL,
          `Request message serialization failure: ${kQ2.getErrorMessage(I)}`);
        return
      }
      this.call.sendMessageWithContext(A, Q)
    }
    sendMessage(A) {
      this.sendMessageWithContext({}, A)
    }
    start(A, B) {
      let Q = null;
      this.call.start(A, {
        onReceiveMetadata: (I) => {
          var G;
          (G = B === null || B === void 0 ? void 0 : B.onReceiveMetadata) === null || G === void 0 || G
            .call(B, I)
        },
        onReceiveMessage: (I) => {
          var G;
          let D;
          try {
            D = this.methodDefinition.responseDeserialize(I)
          } catch (Z) {
            Q = {
              code: yQ2.Status.INTERNAL,
              details: `Response message parsing error: ${kQ2.getErrorMessage(Z)}`,
              metadata: new Hp6.Metadata
            }, this.call.cancelWithStatus(Q.code, Q.details);
            return
          }(G = B === null || B === void 0 ? void 0 : B.onReceiveMessage) === null || G === void 0 || G
            .call(B, D)
        },
        onReceiveStatus: (I) => {
          var G, D;
          if (Q)(G = B === null || B === void 0 ? void 0 : B.onReceiveStatus) === null || G === void 0 || G
            .call(B, Q);
          else(D = B === null || B === void 0 ? void 0 : B.onReceiveStatus) === null || D === void 0 || D
            .call(B, I)
        }
      })
    }
    startRead() {
      this.call.startRead()
    }
    halfClose() {
      this.call.halfClose()
    }
  }
  class bQ2 extends qi1 {
    constructor(A, B) {
      super(A, B)
    }
    start(A, B) {
      var Q, I;
      let G = !1,
        D = {
          onReceiveMetadata: (I = (Q = B === null || B === void 0 ? void 0 : B.onReceiveMetadata) === null ||
            Q === void 0 ? void 0 : Q.bind(B)) !== null && I !== void 0 ? I : (Z) => {},
          onReceiveMessage: (Z) => {
            var Y;
            G = !0, (Y = B === null || B === void 0 ? void 0 : B.onReceiveMessage) === null || Y === void 0 || Y
              .call(B, Z)
          },
          onReceiveStatus: (Z) => {
            var Y, W;
            if (!G)(Y = B === null || B === void 0 ? void 0 : B.onReceiveMessage) === null || Y === void 0 || Y
              .call(B, null);
            (W = B === null || B === void 0 ? void 0 : B.onReceiveStatus) === null || W === void 0 || W.call(B,
              Z)
          }
        };
      super.start(A, D), this.call.startRead()
    }
  }
  class gQ2 extends qi1 {}

  function wp6(A, B, Q) {
    let I = zp6(A, Q.path, B);
    if (Q.responseStream) return new gQ2(I, Q);
    else return new bQ2(I, Q)
  }

  function Ep6(A, B, Q, I) {
    if (A.clientInterceptors.length > 0 && A.clientInterceptorProviders.length > 0) throw new fo(
      "Both interceptors and interceptor_providers were passed as options to the client constructor. Only one of these is allowed."
      );
    if (A.callInterceptors.length > 0 && A.callInterceptorProviders.length > 0) throw new fo(
      "Both interceptors and interceptor_providers were passed as call options. Only one of these is allowed.");
    let G = [];
    if (A.callInterceptors.length > 0 || A.callInterceptorProviders.length > 0) G = [].concat(A.callInterceptors, A
      .callInterceptorProviders.map((Y) => Y(B))).filter((Y) => Y);
    else G = [].concat(A.clientInterceptors, A.clientInterceptorProviders.map((Y) => Y(B))).filter((Y) => Y);
    let D = Object.assign({}, Q, {
      method_definition: B
    });
    return G.reduceRight((Y, W) => {
      return (F) => W(F, Y)
    }, (Y) => wp6(I, Y, B))(D)
  }
});
var Ri1 = w((uQ2) => {
  Object.defineProperty(uQ2, "__esModule", {
    value: !0
  });
  uQ2.Client = void 0;
  var Gw = OQ2(),
    Mp6 = Oi1(),
    Lp6 = $C(),
    UR = O6(),
    Vm = XD(),
    YC1 = Mi1(),
    cV = Symbol(),
    Km = Symbol(),
    Hm = Symbol(),
    NN = Symbol();

  function Li1(A) {
    return typeof A === "function"
  }

  function zm(A) {
    var B;
    return ((B = A.stack) === null || B === void 0 ? void 0 : B.split(`
`).slice(1).join(`
`)) || "no stack trace available"
  }
  class dQ2 {
    constructor(A, B, Q = {}) {
      var I, G;
      if (Q = Object.assign({}, Q), this[Km] = (I = Q.interceptors) !== null && I !== void 0 ? I : [], delete Q
        .interceptors, this[Hm] = (G = Q.interceptor_providers) !== null && G !== void 0 ? G : [], delete Q
        .interceptor_providers, this[Km].length > 0 && this[Hm].length > 0) throw new Error(
        "Both interceptors and interceptor_providers were passed as options to the client constructor. Only one of these is allowed."
        );
      if (this[NN] = Q.callInvocationTransformer, delete Q.callInvocationTransformer, Q.channelOverride) this[
        cV] = Q.channelOverride;
      else if (Q.channelFactoryOverride) {
        let D = Q.channelFactoryOverride;
        delete Q.channelFactoryOverride, this[cV] = D(A, B, Q)
      } else this[cV] = new Mp6.ChannelImplementation(A, B, Q)
    }
    close() {
      this[cV].close()
    }
    getChannel() {
      return this[cV]
    }
    waitForReady(A, B) {
      let Q = (I) => {
        if (I) {
          B(new Error("Failed to connect before the deadline"));
          return
        }
        let G;
        try {
          G = this[cV].getConnectivityState(!0)
        } catch (D) {
          B(new Error("The channel has been closed"));
          return
        }
        if (G === Lp6.ConnectivityState.READY) B();
        else try {
          this[cV].watchConnectivityState(G, A, Q)
        } catch (D) {
          B(new Error("The channel has been closed"))
        }
      };
      setImmediate(Q)
    }
    checkOptionalUnaryResponseArguments(A, B, Q) {
      if (Li1(A)) return {
        metadata: new Vm.Metadata,
        options: {},
        callback: A
      };
      else if (Li1(B))
        if (A instanceof Vm.Metadata) return {
          metadata: A,
          options: {},
          callback: B
        };
        else return {
          metadata: new Vm.Metadata,
          options: A,
          callback: B
        };
      else {
        if (!(A instanceof Vm.Metadata && B instanceof Object && Li1(Q))) throw new Error(
          "Incorrect arguments passed");
        return {
          metadata: A,
          options: B,
          callback: Q
        }
      }
    }
    makeUnaryRequest(A, B, Q, I, G, D, Z) {
      var Y, W;
      let F = this.checkOptionalUnaryResponseArguments(G, D, Z),
        J = {
          path: A,
          requestStream: !1,
          responseStream: !1,
          requestSerialize: B,
          responseDeserialize: Q
        },
        C = {
          argument: I,
          metadata: F.metadata,
          call: new Gw.ClientUnaryCallImpl,
          channel: this[cV],
          methodDefinition: J,
          callOptions: F.options,
          callback: F.callback
        };
      if (this[NN]) C = this[NN](C);
      let X = C.call,
        V = {
          clientInterceptors: this[Km],
          clientInterceptorProviders: this[Hm],
          callInterceptors: (Y = C.callOptions.interceptors) !== null && Y !== void 0 ? Y : [],
          callInterceptorProviders: (W = C.callOptions.interceptor_providers) !== null && W !== void 0 ? W : []
        },
        K = YC1.getInterceptingCall(V, C.methodDefinition, C.callOptions, C.channel);
      X.call = K;
      let U = null,
        N = !1,
        q = new Error;
      return K.start(C.metadata, {
        onReceiveMetadata: (M) => {
          X.emit("metadata", M)
        },
        onReceiveMessage(M) {
          if (U !== null) K.cancelWithStatus(UR.Status.UNIMPLEMENTED, "Too many responses received");
          U = M
        },
        onReceiveStatus(M) {
          if (N) return;
          if (N = !0, M.code === UR.Status.OK)
            if (U === null) {
              let R = zm(q);
              C.callback(Gw.callErrorFromStatus({
                code: UR.Status.UNIMPLEMENTED,
                details: "No message received",
                metadata: M.metadata
              }, R))
            } else C.callback(null, U);
          else {
            let R = zm(q);
            C.callback(Gw.callErrorFromStatus(M, R))
          }
          q = null, X.emit("status", M)
        }
      }), K.sendMessage(I), K.halfClose(), X
    }
    makeClientStreamRequest(A, B, Q, I, G, D) {
      var Z, Y;
      let W = this.checkOptionalUnaryResponseArguments(I, G, D),
        F = {
          path: A,
          requestStream: !0,
          responseStream: !1,
          requestSerialize: B,
          responseDeserialize: Q
        },
        J = {
          metadata: W.metadata,
          call: new Gw.ClientWritableStreamImpl(B),
          channel: this[cV],
          methodDefinition: F,
          callOptions: W.options,
          callback: W.callback
        };
      if (this[NN]) J = this[NN](J);
      let C = J.call,
        X = {
          clientInterceptors: this[Km],
          clientInterceptorProviders: this[Hm],
          callInterceptors: (Z = J.callOptions.interceptors) !== null && Z !== void 0 ? Z : [],
          callInterceptorProviders: (Y = J.callOptions.interceptor_providers) !== null && Y !== void 0 ? Y : []
        },
        V = YC1.getInterceptingCall(X, J.methodDefinition, J.callOptions, J.channel);
      C.call = V;
      let K = null,
        U = !1,
        N = new Error;
      return V.start(J.metadata, {
        onReceiveMetadata: (q) => {
          C.emit("metadata", q)
        },
        onReceiveMessage(q) {
          if (K !== null) V.cancelWithStatus(UR.Status.UNIMPLEMENTED, "Too many responses received");
          K = q, V.startRead()
        },
        onReceiveStatus(q) {
          if (U) return;
          if (U = !0, q.code === UR.Status.OK)
            if (K === null) {
              let M = zm(N);
              J.callback(Gw.callErrorFromStatus({
                code: UR.Status.UNIMPLEMENTED,
                details: "No message received",
                metadata: q.metadata
              }, M))
            } else J.callback(null, K);
          else {
            let M = zm(N);
            J.callback(Gw.callErrorFromStatus(q, M))
          }
          N = null, C.emit("status", q)
        }
      }), C
    }
    checkMetadataAndOptions(A, B) {
      let Q, I;
      if (A instanceof Vm.Metadata)
        if (Q = A, B) I = B;
        else I = {};
      else {
        if (A) I = A;
        else I = {};
        Q = new Vm.Metadata
      }
      return {
        metadata: Q,
        options: I
      }
    }
    makeServerStreamRequest(A, B, Q, I, G, D) {
      var Z, Y;
      let W = this.checkMetadataAndOptions(G, D),
        F = {
          path: A,
          requestStream: !1,
          responseStream: !0,
          requestSerialize: B,
          responseDeserialize: Q
        },
        J = {
          argument: I,
          metadata: W.metadata,
          call: new Gw.ClientReadableStreamImpl(Q),
          channel: this[cV],
          methodDefinition: F,
          callOptions: W.options
        };
      if (this[NN]) J = this[NN](J);
      let C = J.call,
        X = {
          clientInterceptors: this[Km],
          clientInterceptorProviders: this[Hm],
          callInterceptors: (Z = J.callOptions.interceptors) !== null && Z !== void 0 ? Z : [],
          callInterceptorProviders: (Y = J.callOptions.interceptor_providers) !== null && Y !== void 0 ? Y : []
        },
        V = YC1.getInterceptingCall(X, J.methodDefinition, J.callOptions, J.channel);
      C.call = V;
      let K = !1,
        U = new Error;
      return V.start(J.metadata, {
        onReceiveMetadata(N) {
          C.emit("metadata", N)
        },
        onReceiveMessage(N) {
          C.push(N)
        },
        onReceiveStatus(N) {
          if (K) return;
          if (K = !0, C.push(null), N.code !== UR.Status.OK) {
            let q = zm(U);
            C.emit("error", Gw.callErrorFromStatus(N, q))
          }
          U = null, C.emit("status", N)
        }
      }), V.sendMessage(I), V.halfClose(), C
    }
    makeBidiStreamRequest(A, B, Q, I, G) {
      var D, Z;
      let Y = this.checkMetadataAndOptions(I, G),
        W = {
          path: A,
          requestStream: !0,
          responseStream: !0,
          requestSerialize: B,
          responseDeserialize: Q
        },
        F = {
          metadata: Y.metadata,
          call: new Gw.ClientDuplexStreamImpl(B, Q),
          channel: this[cV],
          methodDefinition: W,
          callOptions: Y.options
        };
      if (this[NN]) F = this[NN](F);
      let J = F.call,
        C = {
          clientInterceptors: this[Km],
          clientInterceptorProviders: this[Hm],
          callInterceptors: (D = F.callOptions.interceptors) !== null && D !== void 0 ? D : [],
          callInterceptorProviders: (Z = F.callOptions.interceptor_providers) !== null && Z !== void 0 ? Z : []
        },
        X = YC1.getInterceptingCall(C, F.methodDefinition, F.callOptions, F.channel);
      J.call = X;
      let V = !1,
        K = new Error;
      return X.start(F.metadata, {
        onReceiveMetadata(U) {
          J.emit("metadata", U)
        },
        onReceiveMessage(U) {
          J.push(U)
        },
        onReceiveStatus(U) {
          if (V) return;
          if (V = !0, J.push(null), U.code !== UR.Status.OK) {
            let N = zm(K);
            J.emit("error", Gw.callErrorFromStatus(U, N))
          }
          K = null, J.emit("status", U)
        }
      }), J
    }
  }
  uQ2.Client = dQ2
});
var Pi1 = w((lQ2) => {
  Object.defineProperty(lQ2, "__esModule", {
    value: !0
  });
  lQ2.makeClientConstructor = cQ2;
  lQ2.loadPackageDefinition = Pp6;
  var vo = Ri1(),
    Rp6 = {
      unary: vo.Client.prototype.makeUnaryRequest,
      server_stream: vo.Client.prototype.makeServerStreamRequest,
      client_stream: vo.Client.prototype.makeClientStreamRequest,
      bidi: vo.Client.prototype.makeBidiStreamRequest
    };

  function Ti1(A) {
    return ["__proto__", "prototype", "constructor"].includes(A)
  }

  function cQ2(A, B, Q) {
    if (!Q) Q = {};
    class I extends vo.Client {}
    return Object.keys(A).forEach((G) => {
      if (Ti1(G)) return;
      let D = A[G],
        Z;
      if (typeof G === "string" && G.charAt(0) === "$") throw new Error("Method names cannot start with $");
      if (D.requestStream)
        if (D.responseStream) Z = "bidi";
        else Z = "client_stream";
      else if (D.responseStream) Z = "server_stream";
      else Z = "unary";
      let {
        requestSerialize: Y,
        responseDeserialize: W
      } = D, F = Op6(Rp6[Z], D.path, Y, W);
      if (I.prototype[G] = F, Object.assign(I.prototype[G], D), D.originalName && !Ti1(D.originalName)) I
        .prototype[D.originalName] = I.prototype[G]
    }), I.service = A, I.serviceName = B, I
  }

  function Op6(A, B, Q, I) {
    return function(...G) {
      return A.call(this, B, Q, I, ...G)
    }
  }

  function Tp6(A) {
    return "format" in A
  }

  function Pp6(A) {
    let B = {};
    for (let Q in A)
      if (Object.prototype.hasOwnProperty.call(A, Q)) {
        let I = A[Q],
          G = Q.split(".");
        if (G.some((Y) => Ti1(Y))) continue;
        let D = G[G.length - 1],
          Z = B;
        for (let Y of G.slice(0, -1)) {
          if (!Z[Y]) Z[Y] = {};
          Z = Z[Y]
        }
        if (Tp6(I)) Z[D] = I;
        else Z[D] = cQ2(I, D, {})
      } return B
  }
});
var z72 = w((dO8, H72) => {
  var jp6 = 1 / 0,
    yp6 = "[object Symbol]",
    kp6 = /[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,
    xp6 = /[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,
    FC1 = "\\ud800-\\udfff",
    eQ2 = "\\u0300-\\u036f\\ufe20-\\ufe23",
    A72 = "\\u20d0-\\u20f0",
    B72 = "\\u2700-\\u27bf",
    Q72 = "a-z\\xdf-\\xf6\\xf8-\\xff",
    fp6 = "\\xac\\xb1\\xd7\\xf7",
    vp6 = "\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",
    bp6 = "\\u2000-\\u206f",
    gp6 =
    " \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",
    I72 = "A-Z\\xc0-\\xd6\\xd8-\\xde",
    G72 = "\\ufe0e\\ufe0f",
    D72 = fp6 + vp6 + bp6 + gp6,
    _i1 = "['’]",
    hp6 = "[" + FC1 + "]",
    iQ2 = "[" + D72 + "]",
    WC1 = "[" + eQ2 + A72 + "]",
    Z72 = "\\d+",
    mp6 = "[" + B72 + "]",
    Y72 = "[" + Q72 + "]",
    W72 = "[^" + FC1 + D72 + Z72 + B72 + Q72 + I72 + "]",
    Si1 = "\\ud83c[\\udffb-\\udfff]",
    dp6 = "(?:" + WC1 + "|" + Si1 + ")",
    F72 = "[^" + FC1 + "]",
    ji1 = "(?:\\ud83c[\\udde6-\\uddff]){2}",
    yi1 = "[\\ud800-\\udbff][\\udc00-\\udfff]",
    wm = "[" + I72 + "]",
    J72 = "\\u200d",
    nQ2 = "(?:" + Y72 + "|" + W72 + ")",
    up6 = "(?:" + wm + "|" + W72 + ")",
    aQ2 = "(?:" + _i1 + "(?:d|ll|m|re|s|t|ve))?",
    sQ2 = "(?:" + _i1 + "(?:D|LL|M|RE|S|T|VE))?",
    C72 = dp6 + "?",
    X72 = "[" + G72 + "]?",
    pp6 = "(?:" + J72 + "(?:" + [F72, ji1, yi1].join("|") + ")" + X72 + C72 + ")*",
    V72 = X72 + C72 + pp6,
    cp6 = "(?:" + [mp6, ji1, yi1].join("|") + ")" + V72,
    lp6 = "(?:" + [F72 + WC1 + "?", WC1, ji1, yi1, hp6].join("|") + ")",
    ip6 = RegExp(_i1, "g"),
    np6 = RegExp(WC1, "g"),
    ap6 = RegExp(Si1 + "(?=" + Si1 + ")|" + lp6 + V72, "g"),
    sp6 = RegExp([wm + "?" + Y72 + "+" + aQ2 + "(?=" + [iQ2, wm, "$"].join("|") + ")", up6 + "+" + sQ2 + "(?=" + [
      iQ2, wm + nQ2, "$"
    ].join("|") + ")", wm + "?" + nQ2 + "+" + aQ2, wm + "+" + sQ2, Z72, cp6].join("|"), "g"),
    rp6 = RegExp("[" + J72 + FC1 + eQ2 + A72 + G72 + "]"),
    op6 = /[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,
    tp6 = {
      "À": "A",
      "Á": "A",
      "Â": "A",
      "Ã": "A",
      "Ä": "A",
      "Å": "A",
      "à": "a",
      "á": "a",
      "â": "a",
      "ã": "a",
      "ä": "a",
      "å": "a",
      "Ç": "C",
      "ç": "c",
      "Ð": "D",
      "ð": "d",
      "È": "E",
      "É": "E",
      "Ê": "E",
      "Ë": "E",
      "è": "e",
      "é": "e",
      "ê": "e",
      "ë": "e",
      "Ì": "I",
      "Í": "I",
      "Î": "I",
      "Ï": "I",
      "ì": "i",
      "í": "i",
      "î": "i",
      "ï": "i",
      "Ñ": "N",
      "ñ": "n",
      "Ò": "O",
      "Ó": "O",
      "Ô": "O",
      "Õ": "O",
      "Ö": "O",
      "Ø": "O",
      "ò": "o",
      "ó": "o",
      "ô": "o",
      "õ": "o",
      "ö": "o",
      "ø": "o",
      "Ù": "U",
      "Ú": "U",
      "Û": "U",
      "Ü": "U",
      "ù": "u",
      "ú": "u",
      "û": "u",
      "ü": "u",
      "Ý": "Y",
      "ý": "y",
      "ÿ": "y",
      "Æ": "Ae",
      "æ": "ae",
      "Þ": "Th",
      "þ": "th",
      "ß": "ss",
      "Ā": "A",
      "Ă": "A",
      "Ą": "A",
      "ā": "a",
      "ă": "a",
      "ą": "a",
      "Ć": "C",
      "Ĉ": "C",
      "Ċ": "C",
      "Č": "C",
      "ć": "c",
      "ĉ": "c",
      "ċ": "c",
      "č": "c",
      "Ď": "D",
      "Đ": "D",
      "ď": "d",
      "đ": "d",
      "Ē": "E",
      "Ĕ": "E",
      "Ė": "E",
      "Ę": "E",
      "Ě": "E",
      "ē": "e",
      "ĕ": "e",
      "ė": "e",
      "ę": "e",
      "ě": "e",
      "Ĝ": "G",
      "Ğ": "G",
      "Ġ": "G",
      "Ģ": "G",
      "ĝ": "g",
      "ğ": "g",
      "ġ": "g",
      "ģ": "g",
      "Ĥ": "H",
      "Ħ": "H",
      "ĥ": "h",
      "ħ": "h",
      "Ĩ": "I",
      "Ī": "I",
      "Ĭ": "I",
      "Į": "I",
      "İ": "I",
      "ĩ": "i",
      "ī": "i",
      "ĭ": "i",
      "į": "i",
      "ı": "i",
      "Ĵ": "J",
      "ĵ": "j",
      "Ķ": "K",
      "ķ": "k",
      "ĸ": "k",
      "Ĺ": "L",
      "Ļ": "L",
      "Ľ": "L",
      "Ŀ": "L",
      "Ł": "L",
      "ĺ": "l",
      "ļ": "l",
      "ľ": "l",
      "ŀ": "l",
      "ł": "l",
      "Ń": "N",
      "Ņ": "N",
      "Ň": "N",
      "Ŋ": "N",
      "ń": "n",
      "ņ": "n",
      "ň": "n",
      "ŋ": "n",
      "Ō": "O",
      "Ŏ": "O",
      "Ő": "O",
      "ō": "o",
      "ŏ": "o",
      "ő": "o",
      "Ŕ": "R",
      "Ŗ": "R",
      "Ř": "R",
      "ŕ": "r",
      "ŗ": "r",
      "ř": "r",
      "Ś": "S",
      "Ŝ": "S",
      "Ş": "S",
      "Š": "S",
      "ś": "s",
      "ŝ": "s",
      "ş": "s",
      "š": "s",
      "Ţ": "T",
      "Ť": "T",
      "Ŧ": "T",
      "ţ": "t",
      "ť": "t",
      "ŧ": "t",
      "Ũ": "U",
      "Ū": "U",
      "Ŭ": "U",
      "Ů": "U",
      "Ű": "U",
      "Ų": "U",
      "ũ": "u",
      "ū": "u",
      "ŭ": "u",
      "ů": "u",
      "ű": "u",
      "ų": "u",
      "Ŵ": "W",
      "ŵ": "w",
      "Ŷ": "Y",
      "ŷ": "y",
      "Ÿ": "Y",
      "Ź": "Z",
      "Ż": "Z",
      "Ž": "Z",
      "ź": "z",
      "ż": "z",
      "ž": "z",
      "Ĳ": "IJ",
      "ĳ": "ij",
      "Œ": "Oe",
      "œ": "oe",
      "ŉ": "'n",
      "ſ": "ss"
    },
    ep6 = typeof global == "object" && global && global.Object === Object && global,
    Ac6 = typeof self == "object" && self && self.Object === Object && self,
    Bc6 = ep6 || Ac6 || Function("return this")();

  function Qc6(A, B, Q, I) {
    var G = -1,
      D = A ? A.length : 0;
    if (I && D) Q = A[++G];
    while (++G < D) Q = B(Q, A[G], G, A);
    return Q
  }

  function Ic6(A) {
    return A.split("")
  }

  function Gc6(A) {
    return A.match(kp6) || []
  }

  function Dc6(A) {
    return function(B) {
      return A == null ? void 0 : A[B]
    }
  }
  var Zc6 = Dc6(tp6);

  function K72(A) {
    return rp6.test(A)
  }

  function Yc6(A) {
    return op6.test(A)
  }

  function Wc6(A) {
    return K72(A) ? Fc6(A) : Ic6(A)
  }

  function Fc6(A) {
    return A.match(ap6) || []
  }

  function Jc6(A) {
    return A.match(sp6) || []
  }
  var Cc6 = Object.prototype,
    Xc6 = Cc6.toString,
    rQ2 = Bc6.Symbol,
    oQ2 = rQ2 ? rQ2.prototype : void 0,
    tQ2 = oQ2 ? oQ2.toString : void 0;

  function Vc6(A, B, Q) {
    var I = -1,
      G = A.length;
    if (B < 0) B = -B > G ? 0 : G + B;
    if (Q = Q > G ? G : Q, Q < 0) Q += G;
    G = B > Q ? 0 : Q - B >>> 0, B >>>= 0;
    var D = Array(G);
    while (++I < G) D[I] = A[I + B];
    return D
  }

  function Kc6(A) {
    if (typeof A == "string") return A;
    if (Uc6(A)) return tQ2 ? tQ2.call(A) : "";
    var B = A + "";
    return B == "0" && 1 / A == -jp6 ? "-0" : B
  }

  function Hc6(A, B, Q) {
    var I = A.length;
    return Q = Q === void 0 ? I : Q, !B && Q >= I ? A : Vc6(A, B, Q)
  }

  function zc6(A) {
    return function(B) {
      B = JC1(B);
      var Q = K72(B) ? Wc6(B) : void 0,
        I = Q ? Q[0] : B.charAt(0),
        G = Q ? Hc6(Q, 1).join("") : B.slice(1);
      return I[A]() + G
    }
  }

  function wc6(A) {
    return function(B) {
      return Qc6(Lc6(qc6(B).replace(ip6, "")), A, "")
    }
  }

  function Ec6(A) {
    return !!A && typeof A == "object"
  }

  function Uc6(A) {
    return typeof A == "symbol" || Ec6(A) && Xc6.call(A) == yp6
  }

  function JC1(A) {
    return A == null ? "" : Kc6(A)
  }
  var Nc6 = wc6(function(A, B, Q) {
    return B = B.toLowerCase(), A + (Q ? $c6(B) : B)
  });

  function $c6(A) {
    return Mc6(JC1(A).toLowerCase())
  }

  function qc6(A) {
    return A = JC1(A), A && A.replace(xp6, Zc6).replace(np6, "")
  }
  var Mc6 = zc6("toUpperCase");

  function Lc6(A, B, Q) {
    if (A = JC1(A), B = Q ? void 0 : B, B === void 0) return Yc6(A) ? Jc6(A) : Gc6(A);
    return A.match(B) || []
  }
  H72.exports = Nc6
});
var E72 = w((uO8, w72) => {
  w72.exports = ki1;

  function ki1(A, B) {
    if (typeof A === "string") B = A, A = void 0;
    var Q = [];

    function I(D) {
      if (typeof D !== "string") {
        var Z = G();
        if (ki1.verbose) console.log("codegen: " + Z);
        if (Z = "return " + Z, D) {
          var Y = Object.keys(D),
            W = new Array(Y.length + 1),
            F = new Array(Y.length),
            J = 0;
          while (J < Y.length) W[J] = Y[J], F[J] = D[Y[J++]];
          return W[J] = Z, Function.apply(null, W).apply(null, F)
        }
        return Function(Z)()
      }
      var C = new Array(arguments.length - 1),
        X = 0;
      while (X < C.length) C[X] = arguments[++X];
      if (X = 0, D = D.replace(/%([%dfijs])/g, function V(K, U) {
          var N = C[X++];
          switch (U) {
            case "d":
            case "f":
              return String(Number(N));
            case "i":
              return String(Math.floor(N));
            case "j":
              return JSON.stringify(N);
            case "s":
              return String(N)
          }
          return "%"
        }), X !== C.length) throw Error("parameter count mismatch");
      return Q.push(D), I
    }

    function G(D) {
      return "function " + (D || B || "") + "(" + (A && A.join(",") || "") + `){
  ` + Q.join(`
  `) + `
}`
    }
    return I.toString = G, I
  }
  ki1.verbose = !1
});
var N72 = w((pO8, U72) => {
  U72.exports = bo;
  var Rc6 = El1(),
    Oc6 = Nl1(),
    xi1 = Oc6("fs");

  function bo(A, B, Q) {
    if (typeof B === "function") Q = B, B = {};
    else if (!B) B = {};
    if (!Q) return Rc6(bo, this, A, B);
    if (!B.xhr && xi1 && xi1.readFile) return xi1.readFile(A, function I(G, D) {
      return G && typeof XMLHttpRequest !== "undefined" ? bo.xhr(A, B, Q) : G ? Q(G) : Q(null, B.binary ? D :
        D.toString("utf8"))
    });
    return bo.xhr(A, B, Q)
  }
  bo.xhr = function A(B, Q, I) {
    var G = new XMLHttpRequest;
    if (G.onreadystatechange = function D() {
        if (G.readyState !== 4) return;
        if (G.status !== 0 && G.status !== 200) return I(Error("status " + G.status));
        if (Q.binary) {
          var Z = G.response;
          if (!Z) {
            Z = [];
            for (var Y = 0; Y < G.responseText.length; ++Y) Z.push(G.responseText.charCodeAt(Y) & 255)
          }
          return I(null, typeof Uint8Array !== "undefined" ? new Uint8Array(Z) : Z)
        }
        return I(null, G.responseText)
      }, Q.binary) {
      if ("overrideMimeType" in G) G.overrideMimeType("text/plain; charset=x-user-defined");
      G.responseType = "arraybuffer"
    }
    G.open("GET", B), G.send()
  }
});
var M72 = w((q72) => {
  var vi1 = q72,
    $72 = vi1.isAbsolute = function A(B) {
      return /^(?:\/|\w+:)/.test(B)
    },
    fi1 = vi1.normalize = function A(B) {
      B = B.replace(/\\/g, "/").replace(/\/{2,}/g, "/");
      var Q = B.split("/"),
        I = $72(B),
        G = "";
      if (I) G = Q.shift() + "/";
      for (var D = 0; D < Q.length;)
        if (Q[D] === "..")
          if (D > 0 && Q[D - 1] !== "..") Q.splice(--D, 2);
          else if (I) Q.splice(D, 1);
      else ++D;
      else if (Q[D] === ".") Q.splice(D, 1);
      else ++D;
      return G + Q.join("/")
    };
  vi1.resolve = function A(B, Q, I) {
    if (!I) Q = fi1(Q);
    if ($72(Q)) return Q;
    if (!I) B = fi1(B);
    return (B = B.replace(/(?:\/|^)[^/]+$/, "")).length ? fi1(B + "/" + Q) : Q
  }
});
var k_ = w((L72) => {
  var go = L72,
    Tc6 = VI(),
    Pc6 = ["double", "float", "int32", "uint32", "sint32", "fixed32", "sfixed32", "int64", "uint64", "sint64",
      "fixed64", "sfixed64", "bool", "string", "bytes"
    ];

  function ho(A, B) {
    var Q = 0,
      I = {};
    B |= 0;
    while (Q < A.length) I[Pc6[Q + B]] = A[Q++];
    return I
  }
  go.basic = ho([1, 5, 0, 0, 0, 5, 5, 0, 0, 0, 1, 1, 0, 2, 2]);
  go.defaults = ho([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, !1, "", Tc6.emptyArray, null]);
  go.long = ho([0, 0, 0, 1, 1], 7);
  go.mapKey = ho([0, 0, 0, 5, 5, 0, 0, 0, 1, 1, 0, 2], 2);
  go.packed = ho([1, 5, 0, 0, 0, 5, 5, 0, 0, 0, 1, 1, 0])
});
var NR = w((iO8, T72) => {
  T72.exports = MC;
  var CC1 = x_();
  ((MC.prototype = Object.create(CC1.prototype)).constructor = MC).className = "Field";
  var R72 = lV(),
    O72 = k_(),
    A7 = VI(),
    bi1, Sc6 = /^required|optional|repeated$/;
  MC.fromJSON = function A(B, Q) {
    return new MC(B, Q.id, Q.type, Q.rule, Q.extend, Q.options, Q.comment)
  };

  function MC(A, B, Q, I, G, D, Z) {
    if (A7.isObject(I)) Z = G, D = I, I = G = void 0;
    else if (A7.isObject(G)) Z = D, D = G, G = void 0;
    if (CC1.call(this, A, D), !A7.isInteger(B) || B < 0) throw TypeError("id must be a non-negative integer");
    if (!A7.isString(Q)) throw TypeError("type must be a string");
    if (I !== void 0 && !Sc6.test(I = I.toString().toLowerCase())) throw TypeError("rule must be a string rule");
    if (G !== void 0 && !A7.isString(G)) throw TypeError("extend must be a string");
    if (I === "proto3_optional") I = "optional";
    this.rule = I && I !== "optional" ? I : void 0, this.type = Q, this.id = B, this.extend = G || void 0, this
      .required = I === "required", this.optional = !this.required, this.repeated = I === "repeated", this.map = !1,
      this.message = null, this.partOf = null, this.typeDefault = null, this.defaultValue = null, this.long = A7
      .Long ? O72.long[Q] !== void 0 : !1, this.bytes = Q === "bytes", this.resolvedType = null, this
      .extensionField = null, this.declaringField = null, this._packed = null, this.comment = Z
  }
  Object.defineProperty(MC.prototype, "packed", {
    get: function() {
      if (this._packed === null) this._packed = this.getOption("packed") !== !1;
      return this._packed
    }
  });
  MC.prototype.setOption = function A(B, Q, I) {
    if (B === "packed") this._packed = null;
    return CC1.prototype.setOption.call(this, B, Q, I)
  };
  MC.prototype.toJSON = function A(B) {
    var Q = B ? Boolean(B.keepComments) : !1;
    return A7.toObject(["rule", this.rule !== "optional" && this.rule || void 0, "type", this.type, "id", this.id,
      "extend", this.extend, "options", this.options, "comment", Q ? this.comment : void 0
    ])
  };
  MC.prototype.resolve = function A() {
    if (this.resolved) return this;
    if ((this.typeDefault = O72.defaults[this.type]) === void 0)
      if (this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(
          this.type), this.resolvedType instanceof bi1) this.typeDefault = null;
      else this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]];
    else if (this.options && this.options.proto3_optional) this.typeDefault = null;
    if (this.options && this.options.default != null) {
      if (this.typeDefault = this.options.default, this.resolvedType instanceof R72 && typeof this.typeDefault ===
        "string") this.typeDefault = this.resolvedType.values[this.typeDefault]
    }
    if (this.options) {
      if (this.options.packed === !0 || this.options.packed !== void 0 && this.resolvedType && !(this
          .resolvedType instanceof R72)) delete this.options.packed;
      if (!Object.keys(this.options).length) this.options = void 0
    }
    if (this.long) {
      if (this.typeDefault = A7.Long.fromNumber(this.typeDefault, this.type.charAt(0) === "u"), Object.freeze)
        Object.freeze(this.typeDefault)
    } else if (this.bytes && typeof this.typeDefault === "string") {
      var B;
      if (A7.base64.test(this.typeDefault)) A7.base64.decode(this.typeDefault, B = A7.newBuffer(A7.base64.length(
        this.typeDefault)), 0);
      else A7.utf8.write(this.typeDefault, B = A7.newBuffer(A7.utf8.length(this.typeDefault)), 0);
      this.typeDefault = B
    }
    if (this.map) this.defaultValue = A7.emptyObject;
    else if (this.repeated) this.defaultValue = A7.emptyArray;
    else this.defaultValue = this.typeDefault;
    if (this.parent instanceof bi1) this.parent.ctor.prototype[this.name] = this.defaultValue;
    return CC1.prototype.resolve.call(this)
  };
  MC.d = function A(B, Q, I, G) {
    if (typeof Q === "function") Q = A7.decorateType(Q).name;
    else if (Q && typeof Q === "object") Q = A7.decorateEnum(Q).name;
    return function D(Z, Y) {
      A7.decorateType(Z.constructor).add(new MC(Y, B, Q, I, {
        default: G
      }))
    }
  };
  MC._configure = function A(B) {
    bi1 = B
  }
});
var Em = w((nO8, _72) => {
  _72.exports = LC;
  var VC1 = x_();
  ((LC.prototype = Object.create(VC1.prototype)).constructor = LC).className = "OneOf";
  var P72 = NR(),
    XC1 = VI();

  function LC(A, B, Q, I) {
    if (!Array.isArray(B)) Q = B, B = void 0;
    if (VC1.call(this, A, Q), !(B === void 0 || Array.isArray(B))) throw TypeError("fieldNames must be an Array");
    this.oneof = B || [], this.fieldsArray = [], this.comment = I
  }
  LC.fromJSON = function A(B, Q) {
    return new LC(B, Q.oneof, Q.options, Q.comment)
  };
  LC.prototype.toJSON = function A(B) {
    var Q = B ? Boolean(B.keepComments) : !1;
    return XC1.toObject(["options", this.options, "oneof", this.oneof, "comment", Q ? this.comment : void 0])
  };

  function S72(A) {
    if (A.parent) {
      for (var B = 0; B < A.fieldsArray.length; ++B)
        if (!A.fieldsArray[B].parent) A.parent.add(A.fieldsArray[B])
    }
  }
  LC.prototype.add = function A(B) {
    if (!(B instanceof P72)) throw TypeError("field must be a Field");
    if (B.parent && B.parent !== this.parent) B.parent.remove(B);
    return this.oneof.push(B.name), this.fieldsArray.push(B), B.partOf = this, S72(this), this
  };
  LC.prototype.remove = function A(B) {
    if (!(B instanceof P72)) throw TypeError("field must be a Field");
    var Q = this.fieldsArray.indexOf(B);
    if (Q < 0) throw Error(B + " is not a member of " + this);
    if (this.fieldsArray.splice(Q, 1), Q = this.oneof.indexOf(B.name), Q > -1) this.oneof.splice(Q, 1);
    return B.partOf = null, this
  };
  LC.prototype.onAdd = function A(B) {
    VC1.prototype.onAdd.call(this, B);
    var Q = this;
    for (var I = 0; I < this.oneof.length; ++I) {
      var G = B.get(this.oneof[I]);
      if (G && !G.partOf) G.partOf = Q, Q.fieldsArray.push(G)
    }
    S72(this)
  };
  LC.prototype.onRemove = function A(B) {
    for (var Q = 0, I; Q < this.fieldsArray.length; ++Q)
      if ((I = this.fieldsArray[Q]).parent) I.parent.remove(I);
    VC1.prototype.onRemove.call(this, B)
  };
  LC.d = function A() {
    var B = new Array(arguments.length),
      Q = 0;
    while (Q < arguments.length) B[Q] = arguments[Q++];
    return function I(G, D) {
      XC1.decorateType(G.constructor).add(new LC(D, B)), Object.defineProperty(G, D, {
        get: XC1.oneOfGetter(B),
        set: XC1.oneOfSetter(B)
      })
    }
  }
});
var $m = w((aO8, x72) => {
  x72.exports = w5;
  var gi1 = x_();
  ((w5.prototype = Object.create(gi1.prototype)).constructor = w5).className = "Namespace";
  var j72 = NR(),
    KC1 = VI(),
    _c6 = Em(),
    Um, mo, Nm;
  w5.fromJSON = function A(B, Q) {
    return new w5(B, Q.options).addJSON(Q.nested)
  };

  function y72(A, B) {
    if (!(A && A.length)) return;
    var Q = {};
    for (var I = 0; I < A.length; ++I) Q[A[I].name] = A[I].toJSON(B);
    return Q
  }
  w5.arrayToJSON = y72;
  w5.isReservedId = function A(B, Q) {
    if (B) {
      for (var I = 0; I < B.length; ++I)
        if (typeof B[I] !== "string" && B[I][0] <= Q && B[I][1] > Q) return !0
    }
    return !1
  };
  w5.isReservedName = function A(B, Q) {
    if (B) {
      for (var I = 0; I < B.length; ++I)
        if (B[I] === Q) return !0
    }
    return !1
  };

  function w5(A, B) {
    gi1.call(this, A, B), this.nested = void 0, this._nestedArray = null
  }

  function k72(A) {
    return A._nestedArray = null, A
  }
  Object.defineProperty(w5.prototype, "nestedArray", {
    get: function() {
      return this._nestedArray || (this._nestedArray = KC1.toArray(this.nested))
    }
  });
  w5.prototype.toJSON = function A(B) {
    return KC1.toObject(["options", this.options, "nested", y72(this.nestedArray, B)])
  };
  w5.prototype.addJSON = function A(B) {
    var Q = this;
    if (B)
      for (var I = Object.keys(B), G = 0, D; G < I.length; ++G) D = B[I[G]], Q.add((D.fields !== void 0 ? Um
        .fromJSON : D.values !== void 0 ? Nm.fromJSON : D.methods !== void 0 ? mo.fromJSON : D.id !== void 0 ?
        j72.fromJSON : w5.fromJSON)(I[G], D));
    return this
  };
  w5.prototype.get = function A(B) {
    return this.nested && this.nested[B] || null
  };
  w5.prototype.getEnum = function A(B) {
    if (this.nested && this.nested[B] instanceof Nm) return this.nested[B].values;
    throw Error("no such enum: " + B)
  };
  w5.prototype.add = function A(B) {
    if (!(B instanceof j72 && B.extend !== void 0 || B instanceof Um || B instanceof _c6 || B instanceof Nm ||
        B instanceof mo || B instanceof w5)) throw TypeError("object must be a valid nested object");
    if (!this.nested) this.nested = {};
    else {
      var Q = this.get(B.name);
      if (Q)
        if (Q instanceof w5 && B instanceof w5 && !(Q instanceof Um || Q instanceof mo)) {
          var I = Q.nestedArray;
          for (var G = 0; G < I.length; ++G) B.add(I[G]);
          if (this.remove(Q), !this.nested) this.nested = {};
          B.setOptions(Q.options, !0)
        } else throw Error("duplicate name '" + B.name + "' in " + this)
    }
    return this.nested[B.name] = B, B.onAdd(this), k72(this)
  };
  w5.prototype.remove = function A(B) {
    if (!(B instanceof gi1)) throw TypeError("object must be a ReflectionObject");
    if (B.parent !== this) throw Error(B + " is not a member of " + this);
    if (delete this.nested[B.name], !Object.keys(this.nested).length) this.nested = void 0;
    return B.onRemove(this), k72(this)
  };
  w5.prototype.define = function A(B, Q) {
    if (KC1.isString(B)) B = B.split(".");
    else if (!Array.isArray(B)) throw TypeError("illegal path");
    if (B && B.length && B[0] === "") throw Error("path must be relative");
    var I = this;
    while (B.length > 0) {
      var G = B.shift();
      if (I.nested && I.nested[G]) {
        if (I = I.nested[G], !(I instanceof w5)) throw Error("path conflicts with non-namespace objects")
      } else I.add(I = new w5(G))
    }
    if (Q) I.addJSON(Q);
    return I
  };
  w5.prototype.resolveAll = function A() {
    var B = this.nestedArray,
      Q = 0;
    while (Q < B.length)
      if (B[Q] instanceof w5) B[Q++].resolveAll();
      else B[Q++].resolve();
    return this.resolve()
  };
  w5.prototype.lookup = function A(B, Q, I) {
    if (typeof Q === "boolean") I = Q, Q = void 0;
    else if (Q && !Array.isArray(Q)) Q = [Q];
    if (KC1.isString(B) && B.length) {
      if (B === ".") return this.root;
      B = B.split(".")
    } else if (!B.length) return this;
    if (B[0] === "") return this.root.lookup(B.slice(1), Q);
    var G = this.get(B[0]);
    if (G) {
      if (B.length === 1) {
        if (!Q || Q.indexOf(G.constructor) > -1) return G
      } else if (G instanceof w5 && (G = G.lookup(B.slice(1), Q, !0))) return G
    } else
      for (var D = 0; D < this.nestedArray.length; ++D)
        if (this._nestedArray[D] instanceof w5 && (G = this._nestedArray[D].lookup(B, Q, !0))) return G;
    if (this.parent === null || I) return null;
    return this.parent.lookup(B, Q)
  };
  w5.prototype.lookupType = function A(B) {
    var Q = this.lookup(B, [Um]);
    if (!Q) throw Error("no such type: " + B);
    return Q
  };
  w5.prototype.lookupEnum = function A(B) {
    var Q = this.lookup(B, [Nm]);
    if (!Q) throw Error("no such Enum '" + B + "' in " + this);
    return Q
  };
  w5.prototype.lookupTypeOrEnum = function A(B) {
    var Q = this.lookup(B, [Um, Nm]);
    if (!Q) throw Error("no such Type or Enum '" + B + "' in " + this);
    return Q
  };
  w5.prototype.lookupService = function A(B) {
    var Q = this.lookup(B, [mo]);
    if (!Q) throw Error("no such Service '" + B + "' in " + this);
    return Q
  };
  w5._configure = function(A, B, Q) {
    Um = A, mo = B, Nm = Q
  }
});
var HC1 = w((sO8, f72) => {
  f72.exports = $N;
  var hi1 = NR();
  (($N.prototype = Object.create(hi1.prototype)).constructor = $N).className = "MapField";
  var jc6 = k_(),
    uo = VI();

  function $N(A, B, Q, I, G, D) {
    if (hi1.call(this, A, B, I, void 0, void 0, G, D), !uo.isString(Q)) throw TypeError("keyType must be a string");
    this.keyType = Q, this.resolvedKeyType = null, this.map = !0
  }
  $N.fromJSON = function A(B, Q) {
    return new $N(B, Q.id, Q.keyType, Q.type, Q.options, Q.comment)
  };
  $N.prototype.toJSON = function A(B) {
    var Q = B ? Boolean(B.keepComments) : !1;
    return uo.toObject(["keyType", this.keyType, "type", this.type, "id", this.id, "extend", this.extend,
      "options", this.options, "comment", Q ? this.comment : void 0
    ])
  };
  $N.prototype.resolve = function A() {
    if (this.resolved) return this;
    if (jc6.mapKey[this.keyType] === void 0) throw Error("invalid key type: " + this.keyType);
    return hi1.prototype.resolve.call(this)
  };
  $N.d = function A(B, Q, I) {
    if (typeof I === "function") I = uo.decorateType(I).name;
    else if (I && typeof I === "object") I = uo.decorateEnum(I).name;
    return function G(D, Z) {
      uo.decorateType(D.constructor).add(new $N(Z, B, Q, I))
    }
  }
});
var zC1 = w((rO8, v72) => {
  v72.exports = f_;
  var mi1 = x_();
  ((f_.prototype = Object.create(mi1.prototype)).constructor = f_).className = "Method";
  var qm = VI();

  function f_(A, B, Q, I, G, D, Z, Y, W) {
    if (qm.isObject(G)) Z = G, G = D = void 0;
    else if (qm.isObject(D)) Z = D, D = void 0;
    if (!(B === void 0 || qm.isString(B))) throw TypeError("type must be a string");
    if (!qm.isString(Q)) throw TypeError("requestType must be a string");
    if (!qm.isString(I)) throw TypeError("responseType must be a string");
    mi1.call(this, A, Z), this.type = B || "rpc", this.requestType = Q, this.requestStream = G ? !0 : void 0, this
      .responseType = I, this.responseStream = D ? !0 : void 0, this.resolvedRequestType = null, this
      .resolvedResponseType = null, this.comment = Y, this.parsedOptions = W
  }
  f_.fromJSON = function A(B, Q) {
    return new f_(B, Q.type, Q.requestType, Q.responseType, Q.requestStream, Q.responseStream, Q.options, Q
      .comment, Q.parsedOptions)
  };
  f_.prototype.toJSON = function A(B) {
    var Q = B ? Boolean(B.keepComments) : !1;
    return qm.toObject(["type", this.type !== "rpc" && this.type || void 0, "requestType", this.requestType,
      "requestStream", this.requestStream, "responseType", this.responseType, "responseStream", this
      .responseStream, "options", this.options, "comment", Q ? this.comment : void 0, "parsedOptions", this
      .parsedOptions
    ])
  };
  f_.prototype.resolve = function A() {
    if (this.resolved) return this;
    return this.resolvedRequestType = this.parent.lookupType(this.requestType), this.resolvedResponseType = this
      .parent.lookupType(this.responseType), mi1.prototype.resolve.call(this)
  }
});
var wC1 = w((oO8, g72) => {
  g72.exports = RC;
  var $R = $m();
  ((RC.prototype = Object.create($R.prototype)).constructor = RC).className = "Service";
  var di1 = zC1(),
    po = VI(),
    yc6 = yl1();

  function RC(A, B) {
    $R.call(this, A, B), this.methods = {}, this._methodsArray = null
  }
  RC.fromJSON = function A(B, Q) {
    var I = new RC(B, Q.options);
    if (Q.methods)
      for (var G = Object.keys(Q.methods), D = 0; D < G.length; ++D) I.add(di1.fromJSON(G[D], Q.methods[G[D]]));
    if (Q.nested) I.addJSON(Q.nested);
    return I.comment = Q.comment, I
  };
  RC.prototype.toJSON = function A(B) {
    var Q = $R.prototype.toJSON.call(this, B),
      I = B ? Boolean(B.keepComments) : !1;
    return po.toObject(["options", Q && Q.options || void 0, "methods", $R.arrayToJSON(this.methodsArray, B) ||
      {}, "nested", Q && Q.nested || void 0, "comment", I ? this.comment : void 0
    ])
  };
  Object.defineProperty(RC.prototype, "methodsArray", {
    get: function() {
      return this._methodsArray || (this._methodsArray = po.toArray(this.methods))
    }
  });

  function b72(A) {
    return A._methodsArray = null, A
  }
  RC.prototype.get = function A(B) {
    return this.methods[B] || $R.prototype.get.call(this, B)
  };
  RC.prototype.resolveAll = function A() {
    var B = this.methodsArray;
    for (var Q = 0; Q < B.length; ++Q) B[Q].resolve();
    return $R.prototype.resolve.call(this)
  };
  RC.prototype.add = function A(B) {
    if (this.get(B.name)) throw Error("duplicate name '" + B.name + "' in " + this);
    if (B instanceof di1) return this.methods[B.name] = B, B.parent = this, b72(this);
    return $R.prototype.add.call(this, B)
  };
  RC.prototype.remove = function A(B) {
    if (B instanceof di1) {
      if (this.methods[B.name] !== B) throw Error(B + " is not a member of " + this);
      return delete this.methods[B.name], B.parent = null, b72(this)
    }
    return $R.prototype.remove.call(this, B)
  };
  RC.prototype.create = function A(B, Q, I) {
    var G = new yc6.Service(B, Q, I);
    for (var D = 0, Z; D < this.methodsArray.length; ++D) {
      var Y = po.lcFirst((Z = this._methodsArray[D]).resolve().name).replace(/[^$\w_]/g, "");
      G[Y] = po.codegen(["r", "c"], po.isReserved(Y) ? Y + "_" : Y)("return this.rpcCall(m,q,s,r,c)")({
        m: Z,
        q: Z.resolvedRequestType.ctor,
        s: Z.resolvedResponseType.ctor
      })
    }
    return G
  }
});
var EC1 = w((tO8, h72) => {
  h72.exports = Dw;
  var kc6 = Bw();

  function Dw(A) {
    if (A)
      for (var B = Object.keys(A), Q = 0; Q < B.length; ++Q) this[B[Q]] = A[B[Q]]
  }
  Dw.create = function A(B) {
    return this.$type.create(B)
  };
  Dw.encode = function A(B, Q) {
    return this.$type.encode(B, Q)
  };
  Dw.encodeDelimited = function A(B, Q) {
    return this.$type.encodeDelimited(B, Q)
  };
  Dw.decode = function A(B) {
    return this.$type.decode(B)
  };
  Dw.decodeDelimited = function A(B) {
    return this.$type.decodeDelimited(B)
  };
  Dw.verify = function A(B) {
    return this.$type.verify(B)
  };
  Dw.fromObject = function A(B) {
    return this.$type.fromObject(B)
  };
  Dw.toObject = function A(B, Q) {
    return this.$type.toObject(B, Q)
  };
  Dw.prototype.toJSON = function A() {
    return this.$type.toObject(this, kc6.toJSONOptions)
  }
});
var ui1 = w((eO8, d72) => {
  d72.exports = vc6;
  var xc6 = lV(),
    qN = k_(),
    m72 = VI();

  function fc6(A) {
    return "missing required '" + A.name + "'"
  }

  function vc6(A) {
    var B = m72.codegen(["r", "l"], A.name + "$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")(
      "var c=l===undefined?r.len:r.pos+l,m=new this.ctor" + (A.fieldsArray.filter(function(Y) {
        return Y.map
      }).length ? ",k,value" : ""))("while(r.pos<c){")("var t=r.uint32()");
    if (A.group) B("if((t&7)===4)")("break");
    B("switch(t>>>3){");
    var Q = 0;
    for (; Q < A.fieldsArray.length; ++Q) {
      var I = A._fieldsArray[Q].resolve(),
        G = I.resolvedType instanceof xc6 ? "int32" : I.type,
        D = "m" + m72.safeProp(I.name);
      if (B("case %i: {", I.id), I.map) {
        if (B("if(%s===util.emptyObject)", D)("%s={}", D)("var c2 = r.uint32()+r.pos"), qN.defaults[I.keyType] !==
          void 0) B("k=%j", qN.defaults[I.keyType]);
        else B("k=null");
        if (qN.defaults[G] !== void 0) B("value=%j", qN.defaults[G]);
        else B("value=null");
        if (B("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break", I.keyType)(
            "case 2:"), qN.basic[G] === void 0) B("value=types[%i].decode(r,r.uint32())", Q);
        else B("value=r.%s()", G);
        if (B("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"), qN.long[I.keyType] !== void 0) B(
          '%s[typeof k==="object"?util.longToHash(k):k]=value', D);
        else B("%s[k]=value", D)
      } else if (I.repeated) {
        if (B("if(!(%s&&%s.length))", D, D)("%s=[]", D), qN.packed[G] !== void 0) B("if((t&7)===2){")(
          "var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())", D, G)("}else");
        if (qN.basic[G] === void 0) B(I.resolvedType.group ? "%s.push(types[%i].decode(r))" :
          "%s.push(types[%i].decode(r,r.uint32()))", D, Q);
        else B("%s.push(r.%s())", D, G)
      } else if (qN.basic[G] === void 0) B(I.resolvedType.group ? "%s=types[%i].decode(r)" :
        "%s=types[%i].decode(r,r.uint32())", D, Q);
      else B("%s=r.%s()", D, G);
      B("break")("}")
    }
    B("default:")("r.skipType(t&7)")("break")("}")("}");
    for (Q = 0; Q < A._fieldsArray.length; ++Q) {
      var Z = A._fieldsArray[Q];
      if (Z.required) B("if(!m.hasOwnProperty(%j))", Z.name)("throw util.ProtocolError(%j,{instance:m})", fc6(Z))
    }
    return B("return m")
  }
});
var li1 = w((AT8, u72) => {
  u72.exports = hc6;
  var bc6 = lV(),
    pi1 = VI();

  function OC(A, B) {
    return A.name + ": " + B + (A.repeated && B !== "array" ? "[]" : A.map && B !== "object" ? "{k:" + A.keyType +
      "}" : "") + " expected"
  }

  function ci1(A, B, Q, I) {
    if (B.resolvedType)
      if (B.resolvedType instanceof bc6) {
        A("switch(%s){", I)("default:")("return%j", OC(B, "enum value"));
        for (var G = Object.keys(B.resolvedType.values), D = 0; D < G.length; ++D) A("case %i:", B.resolvedType
          .values[G[D]]);
        A("break")("}")
      } else A("{")("var e=types[%i].verify(%s);", Q, I)("if(e)")("return%j+e", B.name + ".")("}");
    else switch (B.type) {
      case "int32":
      case "uint32":
      case "sint32":
      case "fixed32":
      case "sfixed32":
        A("if(!util.isInteger(%s))", I)("return%j", OC(B, "integer"));
        break;
      case "int64":
      case "uint64":
      case "sint64":
      case "fixed64":
      case "sfixed64":
        A("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))", I, I, I, I)(
          "return%j", OC(B, "integer|Long"));
        break;
      case "float":
      case "double":
        A('if(typeof %s!=="number")', I)("return%j", OC(B, "number"));
        break;
      case "bool":
        A('if(typeof %s!=="boolean")', I)("return%j", OC(B, "boolean"));
        break;
      case "string":
        A("if(!util.isString(%s))", I)("return%j", OC(B, "string"));
        break;
      case "bytes":
        A('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))', I, I, I)("return%j", OC(B, "buffer"));
        break
    }
    return A
  }

  function gc6(A, B, Q) {
    switch (B.keyType) {
      case "int32":
      case "uint32":
      case "sint32":
      case "fixed32":
      case "sfixed32":
        A("if(!util.key32Re.test(%s))", Q)("return%j", OC(B, "integer key"));
        break;
      case "int64":
      case "uint64":
      case "sint64":
      case "fixed64":
      case "sfixed64":
        A("if(!util.key64Re.test(%s))", Q)("return%j", OC(B, "integer|Long key"));
        break;
      case "bool":
        A("if(!util.key2Re.test(%s))", Q)("return%j", OC(B, "boolean key"));
        break
    }
    return A
  }

  function hc6(A) {
    var B = pi1.codegen(["m"], A.name + "$verify")('if(typeof m!=="object"||m===null)')("return%j",
        "object expected"),
      Q = A.oneofsArray,
      I = {};
    if (Q.length) B("var p={}");
    for (var G = 0; G < A.fieldsArray.length; ++G) {
      var D = A._fieldsArray[G].resolve(),
        Z = "m" + pi1.safeProp(D.name);
      if (D.optional) B("if(%s!=null&&m.hasOwnProperty(%j)){", Z, D.name);
      if (D.map) B("if(!util.isObject(%s))", Z)("return%j", OC(D, "object"))("var k=Object.keys(%s)", Z)(
        "for(var i=0;i<k.length;++i){"), gc6(B, D, "k[i]"), ci1(B, D, G, Z + "[k[i]]")("}");
      else if (D.repeated) B("if(!Array.isArray(%s))", Z)("return%j", OC(D, "array"))(
        "for(var i=0;i<%s.length;++i){", Z), ci1(B, D, G, Z + "[i]")("}");
      else {
        if (D.partOf) {
          var Y = pi1.safeProp(D.partOf.name);
          if (I[D.partOf.name] === 1) B("if(p%s===1)", Y)("return%j", D.partOf.name + ": multiple values");
          I[D.partOf.name] = 1, B("p%s=1", Y)
        }
        ci1(B, D, G, Z)
      }
      if (D.optional) B("}")
    }
    return B("return null")
  }
});
var ai1 = w((c72) => {
  var p72 = c72,
    co = lV(),
    Zw = VI();

  function ii1(A, B, Q, I) {
    var G = !1;
    if (B.resolvedType)
      if (B.resolvedType instanceof co) {
        A("switch(d%s){", I);
        for (var D = B.resolvedType.values, Z = Object.keys(D), Y = 0; Y < Z.length; ++Y) {
          if (D[Z[Y]] === B.typeDefault && !G) {
            if (A("default:")('if(typeof(d%s)==="number"){m%s=d%s;break}', I, I, I), !B.repeated) A("break");
            G = !0
          }
          A("case%j:", Z[Y])("case %i:", D[Z[Y]])("m%s=%j", I, D[Z[Y]])("break")
        }
        A("}")
      } else A('if(typeof d%s!=="object")', I)("throw TypeError(%j)", B.fullName + ": object expected")(
        "m%s=types[%i].fromObject(d%s)", I, Q, I);
    else {
      var W = !1;
      switch (B.type) {
        case "double":
        case "float":
          A("m%s=Number(d%s)", I, I);
          break;
        case "uint32":
        case "fixed32":
          A("m%s=d%s>>>0", I, I);
          break;
        case "int32":
        case "sint32":
        case "sfixed32":
          A("m%s=d%s|0", I, I);
          break;
        case "uint64":
          W = !0;
        case "int64":
        case "sint64":
        case "fixed64":
        case "sfixed64":
          A("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j", I, I, W)(
            'else if(typeof d%s==="string")', I)("m%s=parseInt(d%s,10)", I, I)('else if(typeof d%s==="number")',
            I)("m%s=d%s", I, I)('else if(typeof d%s==="object")', I)(
            "m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)", I, I, I, W ? "true" : "");
          break;
        case "bytes":
          A('if(typeof d%s==="string")', I)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",
            I, I, I)("else if(d%s.length >= 0)", I)("m%s=d%s", I, I);
          break;
        case "string":
          A("m%s=String(d%s)", I, I);
          break;
        case "bool":
          A("m%s=Boolean(d%s)", I, I);
          break
      }
    }
    return A
  }
  p72.fromObject = function A(B) {
    var Q = B.fieldsArray,
      I = Zw.codegen(["d"], B.name + "$fromObject")("if(d instanceof this.ctor)")("return d");
    if (!Q.length) return I("return new this.ctor");
    I("var m=new this.ctor");
    for (var G = 0; G < Q.length; ++G) {
      var D = Q[G].resolve(),
        Z = Zw.safeProp(D.name);
      if (D.map) I("if(d%s){", Z)('if(typeof d%s!=="object")', Z)("throw TypeError(%j)", D.fullName +
        ": object expected")("m%s={}", Z)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){", Z), ii1(I, D, G,
        Z + "[ks[i]]")("}")("}");
      else if (D.repeated) I("if(d%s){", Z)("if(!Array.isArray(d%s))", Z)("throw TypeError(%j)", D.fullName +
        ": array expected")("m%s=[]", Z)("for(var i=0;i<d%s.length;++i){", Z), ii1(I, D, G, Z + "[i]")("}")("}");
      else {
        if (!(D.resolvedType instanceof co)) I("if(d%s!=null){", Z);
        if (ii1(I, D, G, Z), !(D.resolvedType instanceof co)) I("}")
      }
    }
    return I("return m")
  };

  function ni1(A, B, Q, I) {
    if (B.resolvedType)
      if (B.resolvedType instanceof co) A(
        "d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s", I, Q, I, I, Q,
        I, I);
      else A("d%s=types[%i].toObject(m%s,o)", I, Q, I);
    else {
      var G = !1;
      switch (B.type) {
        case "double":
        case "float":
          A("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s", I, I, I, I);
          break;
        case "uint64":
          G = !0;
        case "int64":
        case "sint64":
        case "fixed64":
        case "sfixed64":
          A('if(typeof m%s==="number")', I)("d%s=o.longs===String?String(m%s):m%s", I, I, I)("else")(
            "d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",
            I, I, I, I, G ? "true" : "", I);
          break;
        case "bytes":
          A("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",
            I, I, I, I, I);
          break;
        default:
          A("d%s=m%s", I, I);
          break
      }
    }
    return A
  }
  p72.toObject = function A(B) {
    var Q = B.fieldsArray.slice().sort(Zw.compareFieldsById);
    if (!Q.length) return Zw.codegen()("return {}");
    var I = Zw.codegen(["m", "o"], B.name + "$toObject")("if(!o)")("o={}")("var d={}"),
      G = [],
      D = [],
      Z = [],
      Y = 0;
    for (; Y < Q.length; ++Y)
      if (!Q[Y].partOf)(Q[Y].resolve().repeated ? G : Q[Y].map ? D : Z).push(Q[Y]);
    if (G.length) {
      I("if(o.arrays||o.defaults){");
      for (Y = 0; Y < G.length; ++Y) I("d%s=[]", Zw.safeProp(G[Y].name));
      I("}")
    }
    if (D.length) {
      I("if(o.objects||o.defaults){");
      for (Y = 0; Y < D.length; ++Y) I("d%s={}", Zw.safeProp(D[Y].name));
      I("}")
    }
    if (Z.length) {
      I("if(o.defaults){");
      for (Y = 0; Y < Z.length; ++Y) {
        var W = Z[Y],
          F = Zw.safeProp(W.name);
        if (W.resolvedType instanceof co) I("d%s=o.enums===String?%j:%j", F, W.resolvedType.valuesById[W
          .typeDefault], W.typeDefault);
        else if (W.long) I("if(util.Long){")("var n=new util.Long(%i,%i,%j)", W.typeDefault.low, W.typeDefault
            .high, W.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n", F)
          ("}else")("d%s=o.longs===String?%j:%i", F, W.typeDefault.toString(), W.typeDefault.toNumber());
        else if (W.bytes) {
          var J = "[" + Array.prototype.slice.call(W.typeDefault).join(",") + "]";
          I("if(o.bytes===String)d%s=%j", F, String.fromCharCode.apply(String, W.typeDefault))("else{")("d%s=%s",
            F, J)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)", F, F)("}")
        } else I("d%s=%j", F, W.typeDefault)
      }
      I("}")
    }
    var C = !1;
    for (Y = 0; Y < Q.length; ++Y) {
      var W = Q[Y],
        X = B._fieldsArray.indexOf(W),
        F = Zw.safeProp(W.name);
      if (W.map) {
        if (!C) C = !0, I("var ks2");
        I("if(m%s&&(ks2=Object.keys(m%s)).length){", F, F)("d%s={}", F)("for(var j=0;j<ks2.length;++j){"), ni1(I,
          W, X, F + "[ks2[j]]")("}")
      } else if (W.repeated) I("if(m%s&&m%s.length){", F, F)("d%s=[]", F)("for(var j=0;j<m%s.length;++j){", F),
        ni1(I, W, X, F + "[j]")("}");
      else if (I("if(m%s!=null&&m.hasOwnProperty(%j)){", F, W.name), ni1(I, W, X, F), W.partOf) I("if(o.oneofs)")(
        "d%s=%j", Zw.safeProp(W.partOf.name), W.name);
      I("}")
    }
    return I("return d")
  }
});
var si1 = w((l72) => {
  var mc6 = l72,
    dc6 = EC1();
  mc6[".google.protobuf.Any"] = {
    fromObject: function(A) {
      if (A && A["@type"]) {
        var B = A["@type"].substring(A["@type"].lastIndexOf("/") + 1),
          Q = this.lookup(B);
        if (Q) {
          var I = A["@type"].charAt(0) === "." ? A["@type"].slice(1) : A["@type"];
          if (I.indexOf("/") === -1) I = "/" + I;
          return this.create({
            type_url: I,
            value: Q.encode(Q.fromObject(A)).finish()
          })
        }
      }
      return this.fromObject(A)
    },
    toObject: function(A, B) {
      var Q = "type.googleapis.com/",
        I = "",
        G = "";
      if (B && B.json && A.type_url && A.value) {
        G = A.type_url.substring(A.type_url.lastIndexOf("/") + 1), I = A.type_url.substring(0, A.type_url
          .lastIndexOf("/") + 1);
        var D = this.lookup(G);
        if (D) A = D.decode(A.value)
      }
      if (!(A instanceof this.ctor) && A instanceof dc6) {
        var Z = A.$type.toObject(A, B),
          Y = A.$type.fullName[0] === "." ? A.$type.fullName.slice(1) : A.$type.fullName;
        if (I === "") I = Q;
        return G = I + Y, Z["@type"] = G, Z
      }
      return this.toObject(A, B)
    }
  }
});
var $C1 = w((IT8, n72) => {
  n72.exports = o8;
  var iV = $m();
  ((o8.prototype = Object.create(iV.prototype)).constructor = o8).className = "Type";
  var uc6 = lV(),
    ti1 = Em(),
    UC1 = NR(),
    pc6 = HC1(),
    cc6 = wC1(),
    ri1 = EC1(),
    oi1 = fJ1(),
    lc6 = kJ1(),
    VD = VI(),
    ic6 = ei1(),
    nc6 = ui1(),
    ac6 = li1(),
    i72 = ai1(),
    sc6 = si1();

  function o8(A, B) {
    iV.call(this, A, B), this.fields = {}, this.oneofs = void 0, this.extensions = void 0, this.reserved = void 0,
      this.group = void 0, this._fieldsById = null, this._fieldsArray = null, this._oneofsArray = null, this._ctor =
      null
  }
  Object.defineProperties(o8.prototype, {
    fieldsById: {
      get: function() {
        if (this._fieldsById) return this._fieldsById;
        this._fieldsById = {};
        for (var A = Object.keys(this.fields), B = 0; B < A.length; ++B) {
          var Q = this.fields[A[B]],
            I = Q.id;
          if (this._fieldsById[I]) throw Error("duplicate id " + I + " in " + this);
          this._fieldsById[I] = Q
        }
        return this._fieldsById
      }
    },
    fieldsArray: {
      get: function() {
        return this._fieldsArray || (this._fieldsArray = VD.toArray(this.fields))
      }
    },
    oneofsArray: {
      get: function() {
        return this._oneofsArray || (this._oneofsArray = VD.toArray(this.oneofs))
      }
    },
    ctor: {
      get: function() {
        return this._ctor || (this.ctor = o8.generateConstructor(this)())
      },
      set: function(A) {
        var B = A.prototype;
        if (!(B instanceof ri1))(A.prototype = new ri1).constructor = A, VD.merge(A.prototype, B);
        A.$type = A.prototype.$type = this, VD.merge(A, ri1, !0), this._ctor = A;
        var Q = 0;
        for (; Q < this.fieldsArray.length; ++Q) this._fieldsArray[Q].resolve();
        var I = {};
        for (Q = 0; Q < this.oneofsArray.length; ++Q) I[this._oneofsArray[Q].resolve().name] = {
          get: VD.oneOfGetter(this._oneofsArray[Q].oneof),
          set: VD.oneOfSetter(this._oneofsArray[Q].oneof)
        };
        if (Q) Object.defineProperties(A.prototype, I)
      }
    }
  });
  o8.generateConstructor = function A(B) {
    var Q = VD.codegen(["p"], B.name);
    for (var I = 0, G; I < B.fieldsArray.length; ++I)
      if ((G = B._fieldsArray[I]).map) Q("this%s={}", VD.safeProp(G.name));
      else if (G.repeated) Q("this%s=[]", VD.safeProp(G.name));
    return Q("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")
  };

  function NC1(A) {
    return A._fieldsById = A._fieldsArray = A._oneofsArray = null, delete A.encode, delete A.decode, delete A
      .verify, A
  }
  o8.fromJSON = function A(B, Q) {
    var I = new o8(B, Q.options);
    I.extensions = Q.extensions, I.reserved = Q.reserved;
    var G = Object.keys(Q.fields),
      D = 0;
    for (; D < G.length; ++D) I.add((typeof Q.fields[G[D]].keyType !== "undefined" ? pc6.fromJSON : UC1.fromJSON)(
      G[D], Q.fields[G[D]]));
    if (Q.oneofs)
      for (G = Object.keys(Q.oneofs), D = 0; D < G.length; ++D) I.add(ti1.fromJSON(G[D], Q.oneofs[G[D]]));
    if (Q.nested)
      for (G = Object.keys(Q.nested), D = 0; D < G.length; ++D) {
        var Z = Q.nested[G[D]];
        I.add((Z.id !== void 0 ? UC1.fromJSON : Z.fields !== void 0 ? o8.fromJSON : Z.values !== void 0 ? uc6
          .fromJSON : Z.methods !== void 0 ? cc6.fromJSON : iV.fromJSON)(G[D], Z))
      }
    if (Q.extensions && Q.extensions.length) I.extensions = Q.extensions;
    if (Q.reserved && Q.reserved.length) I.reserved = Q.reserved;
    if (Q.group) I.group = !0;
    if (Q.comment) I.comment = Q.comment;
    return I
  };
  o8.prototype.toJSON = function A(B) {
    var Q = iV.prototype.toJSON.call(this, B),
      I = B ? Boolean(B.keepComments) : !1;
    return VD.toObject(["options", Q && Q.options || void 0, "oneofs", iV.arrayToJSON(this.oneofsArray, B),
      "fields", iV.arrayToJSON(this.fieldsArray.filter(function(G) {
        return !G.declaringField
      }), B) || {}, "extensions", this.extensions && this.extensions.length ? this.extensions : void 0,
      "reserved", this.reserved && this.reserved.length ? this.reserved : void 0, "group", this.group ||
      void 0, "nested", Q && Q.nested || void 0, "comment", I ? this.comment : void 0
    ])
  };
  o8.prototype.resolveAll = function A() {
    var B = this.fieldsArray,
      Q = 0;
    while (Q < B.length) B[Q++].resolve();
    var I = this.oneofsArray;
    Q = 0;
    while (Q < I.length) I[Q++].resolve();
    return iV.prototype.resolveAll.call(this)
  };
  o8.prototype.get = function A(B) {
    return this.fields[B] || this.oneofs && this.oneofs[B] || this.nested && this.nested[B] || null
  };
  o8.prototype.add = function A(B) {
    if (this.get(B.name)) throw Error("duplicate name '" + B.name + "' in " + this);
    if (B instanceof UC1 && B.extend === void 0) {
      if (this._fieldsById ? this._fieldsById[B.id] : this.fieldsById[B.id]) throw Error("duplicate id " + B.id +
        " in " + this);
      if (this.isReservedId(B.id)) throw Error("id " + B.id + " is reserved in " + this);
      if (this.isReservedName(B.name)) throw Error("name '" + B.name + "' is reserved in " + this);
      if (B.parent) B.parent.remove(B);
      return this.fields[B.name] = B, B.message = this, B.onAdd(this), NC1(this)
    }
    if (B instanceof ti1) {
      if (!this.oneofs) this.oneofs = {};
      return this.oneofs[B.name] = B, B.onAdd(this), NC1(this)
    }
    return iV.prototype.add.call(this, B)
  };
  o8.prototype.remove = function A(B) {
    if (B instanceof UC1 && B.extend === void 0) {
      if (!this.fields || this.fields[B.name] !== B) throw Error(B + " is not a member of " + this);
      return delete this.fields[B.name], B.parent = null, B.onRemove(this), NC1(this)
    }
    if (B instanceof ti1) {
      if (!this.oneofs || this.oneofs[B.name] !== B) throw Error(B + " is not a member of " + this);
      return delete this.oneofs[B.name], B.parent = null, B.onRemove(this), NC1(this)
    }
    return iV.prototype.remove.call(this, B)
  };
  o8.prototype.isReservedId = function A(B) {
    return iV.isReservedId(this.reserved, B)
  };
  o8.prototype.isReservedName = function A(B) {
    return iV.isReservedName(this.reserved, B)
  };
  o8.prototype.create = function A(B) {
    return new this.ctor(B)
  };
  o8.prototype.setup = function A() {
    var B = this.fullName,
      Q = [];
    for (var I = 0; I < this.fieldsArray.length; ++I) Q.push(this._fieldsArray[I].resolve().resolvedType);
    this.encode = ic6(this)({
      Writer: lc6,
      types: Q,
      util: VD
    }), this.decode = nc6(this)({
      Reader: oi1,
      types: Q,
      util: VD
    }), this.verify = ac6(this)({
      types: Q,
      util: VD
    }), this.fromObject = i72.fromObject(this)({
      types: Q,
      util: VD
    }), this.toObject = i72.toObject(this)({
      types: Q,
      util: VD
    });
    var G = sc6[B];
    if (G) {
      var D = Object.create(this);
      D.fromObject = this.fromObject, this.fromObject = G.fromObject.bind(D), D.toObject = this.toObject, this
        .toObject = G.toObject.bind(D)
    }
    return this
  };
  o8.prototype.encode = function A(B, Q) {
    return this.setup().encode(B, Q)
  };
  o8.prototype.encodeDelimited = function A(B, Q) {
    return this.encode(B, Q && Q.len ? Q.fork() : Q).ldelim()
  };
  o8.prototype.decode = function A(B, Q) {
    return this.setup().decode(B, Q)
  };
  o8.prototype.decodeDelimited = function A(B) {
    if (!(B instanceof oi1)) B = oi1.create(B);
    return this.decode(B, B.uint32())
  };
  o8.prototype.verify = function A(B) {
    return this.setup().verify(B)
  };
  o8.prototype.fromObject = function A(B) {
    return this.setup().fromObject(B)
  };
  o8.prototype.toObject = function A(B, Q) {
    return this.setup().toObject(B, Q)
  };
  o8.d = function A(B) {
    return function Q(I) {
      VD.decorateType(I, B)
    }
  }
});
var LC1 = w((GT8, t72) => {
  t72.exports = HF;
  var MC1 = $m();
  ((HF.prototype = Object.create(MC1.prototype)).constructor = HF).className = "Root";
  var Bn1 = NR(),
    s72 = lV(),
    rc6 = Em(),
    qR = VI(),
    r72, An1, lo;

  function HF(A) {
    MC1.call(this, "", A), this.deferred = [], this.files = []
  }
  HF.fromJSON = function A(B, Q) {
    if (!Q) Q = new HF;
    if (B.options) Q.setOptions(B.options);
    return Q.addJSON(B.nested)
  };
  HF.prototype.resolvePath = qR.path.resolve;
  HF.prototype.fetch = qR.fetch;

  function o72() {}
  HF.prototype.load = function A(B, Q, I) {
    if (typeof Q === "function") I = Q, Q = void 0;
    var G = this;
    if (!I) return qR.asPromise(A, G, B, Q);
    var D = I === o72;

    function Z(V, K) {
      if (!I) return;
      if (D) throw V;
      var U = I;
      I = null, U(V, K)
    }

    function Y(V) {
      var K = V.lastIndexOf("google/protobuf/");
      if (K > -1) {
        var U = V.substring(K);
        if (U in lo) return U
      }
      return null
    }

    function W(V, K) {
      try {
        if (qR.isString(K) && K.charAt(0) === "{") K = JSON.parse(K);
        if (!qR.isString(K)) G.setOptions(K.options).addJSON(K.nested);
        else {
          An1.filename = V;
          var U = An1(K, G, Q),
            N, q = 0;
          if (U.imports) {
            for (; q < U.imports.length; ++q)
              if (N = Y(U.imports[q]) || G.resolvePath(V, U.imports[q])) F(N)
          }
          if (U.weakImports) {
            for (q = 0; q < U.weakImports.length; ++q)
              if (N = Y(U.weakImports[q]) || G.resolvePath(V, U.weakImports[q])) F(N, !0)
          }
        }
      } catch (M) {
        Z(M)
      }
      if (!D && !J) Z(null, G)
    }

    function F(V, K) {
      if (V = Y(V) || V, G.files.indexOf(V) > -1) return;
      if (G.files.push(V), V in lo) {
        if (D) W(V, lo[V]);
        else ++J, setTimeout(function() {
          --J, W(V, lo[V])
        });
        return
      }
      if (D) {
        var U;
        try {
          U = qR.fs.readFileSync(V).toString("utf8")
        } catch (N) {
          if (!K) Z(N);
          return
        }
        W(V, U)
      } else ++J, G.fetch(V, function(N, q) {
        if (--J, !I) return;
        if (N) {
          if (!K) Z(N);
          else if (!J) Z(null, G);
          return
        }
        W(V, q)
      })
    }
    var J = 0;
    if (qR.isString(B)) B = [B];
    for (var C = 0, X; C < B.length; ++C)
      if (X = G.resolvePath("", B[C])) F(X);
    if (D) return G;
    if (!J) Z(null, G);
    return
  };
  HF.prototype.loadSync = function A(B, Q) {
    if (!qR.isNode) throw Error("not supported");
    return this.load(B, Q, o72)
  };
  HF.prototype.resolveAll = function A() {
    if (this.deferred.length) throw Error("unresolvable extensions: " + this.deferred.map(function(B) {
      return "'extend " + B.extend + "' in " + B.parent.fullName
    }).join(", "));
    return MC1.prototype.resolveAll.call(this)
  };
  var qC1 = /^[A-Z]/;

  function a72(A, B) {
    var Q = B.parent.lookup(B.extend);
    if (Q) {
      var I = new Bn1(B.fullName, B.id, B.type, B.rule, void 0, B.options);
      if (Q.get(I.name)) return !0;
      return I.declaringField = B, B.extensionField = I, Q.add(I), !0
    }
    return !1
  }
  HF.prototype._handleAdd = function A(B) {
    if (B instanceof Bn1) {
      if (B.extend !== void 0 && !B.extensionField) {
        if (!a72(this, B)) this.deferred.push(B)
      }
    } else if (B instanceof s72) {
      if (qC1.test(B.name)) B.parent[B.name] = B.values
    } else if (!(B instanceof rc6)) {
      if (B instanceof r72)
        for (var Q = 0; Q < this.deferred.length;)
          if (a72(this, this.deferred[Q])) this.deferred.splice(Q, 1);
          else ++Q;
      for (var I = 0; I < B.nestedArray.length; ++I) this._handleAdd(B._nestedArray[I]);
      if (qC1.test(B.name)) B.parent[B.name] = B
    }
  };
  HF.prototype._handleRemove = function A(B) {
    if (B instanceof Bn1) {
      if (B.extend !== void 0)
        if (B.extensionField) B.extensionField.parent.remove(B.extensionField), B.extensionField = null;
        else {
          var Q = this.deferred.indexOf(B);
          if (Q > -1) this.deferred.splice(Q, 1)
        }
    } else if (B instanceof s72) {
      if (qC1.test(B.name)) delete B.parent[B.name]
    } else if (B instanceof MC1) {
      for (var I = 0; I < B.nestedArray.length; ++I) this._handleRemove(B._nestedArray[I]);
      if (qC1.test(B.name)) delete B.parent[B.name]
    }
  };
  HF._configure = function(A, B, Q) {
    r72 = A, An1 = B, lo = Q
  }
});
var VI = w((DT8, AI2) => {
  var EQ = AI2.exports = Bw(),
    e72 = kl1(),
    Qn1, In1;
  EQ.codegen = E72();
  EQ.fetch = N72();
  EQ.path = M72();
  EQ.fs = EQ.inquire("fs");
  EQ.toArray = function A(B) {
    if (B) {
      var Q = Object.keys(B),
        I = new Array(Q.length),
        G = 0;
      while (G < Q.length) I[G] = B[Q[G++]];
      return I
    }
    return []
  };
  EQ.toObject = function A(B) {
    var Q = {},
      I = 0;
    while (I < B.length) {
      var G = B[I++],
        D = B[I++];
      if (D !== void 0) Q[G] = D
    }
    return Q
  };
  var oc6 = /\\/g,
    tc6 = /"/g;
  EQ.isReserved = function A(B) {
    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/
      .test(B)
  };
  EQ.safeProp = function A(B) {
    if (!/^[$\w_]+$/.test(B) || EQ.isReserved(B)) return '["' + B.replace(oc6, "\\\\").replace(tc6, "\\\"") +
    '"]';
    return "." + B
  };
  EQ.ucFirst = function A(B) {
    return B.charAt(0).toUpperCase() + B.substring(1)
  };
  var ec6 = /_([a-z])/g;
  EQ.camelCase = function A(B) {
    return B.substring(0, 1) + B.substring(1).replace(ec6, function(Q, I) {
      return I.toUpperCase()
    })
  };
  EQ.compareFieldsById = function A(B, Q) {
    return B.id - Q.id
  };
  EQ.decorateType = function A(B, Q) {
    if (B.$type) {
      if (Q && B.$type.name !== Q) EQ.decorateRoot.remove(B.$type), B.$type.name = Q, EQ.decorateRoot.add(B
      .$type);
      return B.$type
    }
    if (!Qn1) Qn1 = $C1();
    var I = new Qn1(Q || B.name);
    return EQ.decorateRoot.add(I), I.ctor = B, Object.defineProperty(B, "$type", {
      value: I,
      enumerable: !1
    }), Object.defineProperty(B.prototype, "$type", {
      value: I,
      enumerable: !1
    }), I
  };
  var Al6 = 0;
  EQ.decorateEnum = function A(B) {
    if (B.$type) return B.$type;
    if (!In1) In1 = lV();
    var Q = new In1("Enum" + Al6++, B);
    return EQ.decorateRoot.add(Q), Object.defineProperty(B, "$type", {
      value: Q,
      enumerable: !1
    }), Q
  };
  EQ.setProperty = function A(B, Q, I) {
    function G(D, Z, Y) {
      var W = Z.shift();
      if (W === "__proto__" || W === "prototype") return D;
      if (Z.length > 0) D[W] = G(D[W] || {}, Z, Y);
      else {
        var F = D[W];
        if (F) Y = [].concat(F).concat(Y);
        D[W] = Y
      }
      return D
    }
    if (typeof B !== "object") throw TypeError("dst must be an object");
    if (!Q) throw TypeError("path must be specified");
    return Q = Q.split("."), G(B, Q, I)
  };
  Object.defineProperty(EQ, "decorateRoot", {
    get: function() {
      return e72.decorated || (e72.decorated = new(LC1()))
    }
  })
});
var x_ = w((ZT8, BI2) => {
  BI2.exports = zF;
  zF.className = "ReflectionObject";
  var RC1 = VI(),
    OC1;

  function zF(A, B) {
    if (!RC1.isString(A)) throw TypeError("name must be a string");
    if (B && !RC1.isObject(B)) throw TypeError("options must be an object");
    this.options = B, this.parsedOptions = null, this.name = A, this.parent = null, this.resolved = !1, this
      .comment = null, this.filename = null
  }
  Object.defineProperties(zF.prototype, {
    root: {
      get: function() {
        var A = this;
        while (A.parent !== null) A = A.parent;
        return A
      }
    },
    fullName: {
      get: function() {
        var A = [this.name],
          B = this.parent;
        while (B) A.unshift(B.name), B = B.parent;
        return A.join(".")
      }
    }
  });
  zF.prototype.toJSON = function A() {
    throw Error()
  };
  zF.prototype.onAdd = function A(B) {
    if (this.parent && this.parent !== B) this.parent.remove(this);
    this.parent = B, this.resolved = !1;
    var Q = B.root;
    if (Q instanceof OC1) Q._handleAdd(this)
  };
  zF.prototype.onRemove = function A(B) {
    var Q = B.root;
    if (Q instanceof OC1) Q._handleRemove(this);
    this.parent = null, this.resolved = !1
  };
  zF.prototype.resolve = function A() {
    if (this.resolved) return this;
    if (this.root instanceof OC1) this.resolved = !0;
    return this
  };
  zF.prototype.getOption = function A(B) {
    if (this.options) return this.options[B];
    return
  };
  zF.prototype.setOption = function A(B, Q, I) {
    if (!I || !this.options || this.options[B] === void 0)(this.options || (this.options = {}))[B] = Q;
    return this
  };
  zF.prototype.setParsedOption = function A(B, Q, I) {
    if (!this.parsedOptions) this.parsedOptions = [];
    var G = this.parsedOptions;
    if (I) {
      var D = G.find(function(W) {
        return Object.prototype.hasOwnProperty.call(W, B)
      });
      if (D) {
        var Z = D[B];
        RC1.setProperty(Z, I, Q)
      } else D = {}, D[B] = RC1.setProperty({}, I, Q), G.push(D)
    } else {
      var Y = {};
      Y[B] = Q, G.push(Y)
    }
    return this
  };
  zF.prototype.setOptions = function A(B, Q) {
    if (B)
      for (var I = Object.keys(B), G = 0; G < I.length; ++G) this.setOption(I[G], B[I[G]], Q);
    return this
  };
  zF.prototype.toString = function A() {
    var B = this.constructor.className,
      Q = this.fullName;
    if (Q.length) return B + " " + Q;
    return B
  };
  zF._configure = function(A) {
    OC1 = A
  }
});
var lV = w((YT8, GI2) => {
  GI2.exports = Yw;
  var QI2 = x_();
  ((Yw.prototype = Object.create(QI2.prototype)).constructor = Yw).className = "Enum";
  var II2 = $m(),
    TC1 = VI();

  function Yw(A, B, Q, I, G, D) {
    if (QI2.call(this, A, Q), B && typeof B !== "object") throw TypeError("values must be an object");
    if (this.valuesById = {}, this.values = Object.create(this.valuesById), this.comment = I, this.comments = G ||
      {}, this.valuesOptions = D, this.reserved = void 0, B) {
      for (var Z = Object.keys(B), Y = 0; Y < Z.length; ++Y)
        if (typeof B[Z[Y]] === "number") this.valuesById[this.values[Z[Y]] = B[Z[Y]]] = Z[Y]
    }
  }
  Yw.fromJSON = function A(B, Q) {
    var I = new Yw(B, Q.values, Q.options, Q.comment, Q.comments);
    return I.reserved = Q.reserved, I
  };
  Yw.prototype.toJSON = function A(B) {
    var Q = B ? Boolean(B.keepComments) : !1;
    return TC1.toObject(["options", this.options, "valuesOptions", this.valuesOptions, "values", this.values,
      "reserved", this.reserved && this.reserved.length ? this.reserved : void 0, "comment", Q ? this
      .comment : void 0, "comments", Q ? this.comments : void 0
    ])
  };
  Yw.prototype.add = function A(B, Q, I, G) {
    if (!TC1.isString(B)) throw TypeError("name must be a string");
    if (!TC1.isInteger(Q)) throw TypeError("id must be an integer");
    if (this.values[B] !== void 0) throw Error("duplicate name '" + B + "' in " + this);
    if (this.isReservedId(Q)) throw Error("id " + Q + " is reserved in " + this);
    if (this.isReservedName(B)) throw Error("name '" + B + "' is reserved in " + this);
    if (this.valuesById[Q] !== void 0) {
      if (!(this.options && this.options.allow_alias)) throw Error("duplicate id " + Q + " in " + this);
      this.values[B] = Q
    } else this.valuesById[this.values[B] = Q] = B;
    if (G) {
      if (this.valuesOptions === void 0) this.valuesOptions = {};
      this.valuesOptions[B] = G || null
    }
    return this.comments[B] = I || null, this
  };
  Yw.prototype.remove = function A(B) {
    if (!TC1.isString(B)) throw TypeError("name must be a string");
    var Q = this.values[B];
    if (Q == null) throw Error("name '" + B + "' does not exist in " + this);
    if (delete this.valuesById[Q], delete this.values[B], delete this.comments[B], this.valuesOptions) delete this
      .valuesOptions[B];
    return this
  };
  Yw.prototype.isReservedId = function A(B) {
    return II2.isReservedId(this.reserved, B)
  };
  Yw.prototype.isReservedName = function A(B) {
    return II2.isReservedName(this.reserved, B)
  }
});
var ei1 = w((WT8, ZI2) => {
  ZI2.exports = Ql6;
  var Bl6 = lV(),
    Gn1 = k_(),
    Dn1 = VI();

  function DI2(A, B, Q, I) {
    return B.resolvedType.group ? A("types[%i].encode(%s,w.uint32(%i)).uint32(%i)", Q, I, (B.id << 3 | 3) >>> 0, (B
      .id << 3 | 4) >>> 0) : A("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()", Q, I, (B.id << 3 | 2) >>> 0)
  }

  function Ql6(A) {
    var B = Dn1.codegen(["m", "w"], A.name + "$encode")("if(!w)")("w=Writer.create()"),
      Q, I, G = A.fieldsArray.slice().sort(Dn1.compareFieldsById);
    for (var Q = 0; Q < G.length; ++Q) {
      var D = G[Q].resolve(),
        Z = A._fieldsArray.indexOf(D),
        Y = D.resolvedType instanceof Bl6 ? "int32" : D.type,
        W = Gn1.basic[Y];
      if (I = "m" + Dn1.safeProp(D.name), D.map) {
        if (B("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){", I, D.name)(
            "for(var ks=Object.keys(%s),i=0;i<ks.length;++i){", I)("w.uint32(%i).fork().uint32(%i).%s(ks[i])", (D
            .id << 3 | 2) >>> 0, 8 | Gn1.mapKey[D.keyType], D.keyType), W === void 0) B(
          "types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()", Z, I);
        else B(".uint32(%i).%s(%s[ks[i]]).ldelim()", 16 | W, Y, I);
        B("}")("}")
      } else if (D.repeated) {
        if (B("if(%s!=null&&%s.length){", I, I), D.packed && Gn1.packed[Y] !== void 0) B("w.uint32(%i).fork()", (D
          .id << 3 | 2) >>> 0)("for(var i=0;i<%s.length;++i)", I)("w.%s(%s[i])", Y, I)("w.ldelim()");
        else if (B("for(var i=0;i<%s.length;++i)", I), W === void 0) DI2(B, D, Z, I + "[i]");
        else B("w.uint32(%i).%s(%s[i])", (D.id << 3 | W) >>> 0, Y, I);
        B("}")
      } else {
        if (D.optional) B("if(%s!=null&&Object.hasOwnProperty.call(m,%j))", I, D.name);
        if (W === void 0) DI2(B, D, Z, I);
        else B("w.uint32(%i).%s(%s)", (D.id << 3 | W) >>> 0, Y, I)
      }
    }
    return B("return w")
  }
});
var WI2 = w((FT8, YI2) => {
  var a6 = YI2.exports = xl1();
  a6.build = "light";

  function Il6(A, B, Q) {
    if (typeof B === "function") Q = B, B = new a6.Root;
    else if (!B) B = new a6.Root;
    return B.load(A, Q)
  }
  a6.load = Il6;

  function Gl6(A, B) {
    if (!B) B = new a6.Root;
    return B.loadSync(A)
  }
  a6.loadSync = Gl6;
  a6.encoder = ei1();
  a6.decoder = ui1();
  a6.verifier = li1();
  a6.converter = ai1();
  a6.ReflectionObject = x_();
  a6.Namespace = $m();
  a6.Root = LC1();
  a6.Enum = lV();
  a6.Type = $C1();
  a6.Field = NR();
  a6.OneOf = Em();
  a6.MapField = HC1();
  a6.Service = wC1();
  a6.Method = zC1();
  a6.Message = EC1();
  a6.wrappers = si1();
  a6.types = k_();
  a6.util = VI();
  a6.ReflectionObject._configure(a6.Root);
  a6.Namespace._configure(a6.Type, a6.Service, a6.Enum);
  a6.Root._configure(a6.Type);
  a6.Field._configure(a6.Type)
});
var Yn1 = w((JT8, CI2) => {
  CI2.exports = JI2;
  var Zn1 = /[\s{}=;:[\],'"()<>]/g,
    Dl6 = /(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,
    Zl6 = /(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,
    Yl6 = /^ *[*/]+ */,
    Wl6 = /^\s*\*?\/*/,
    Fl6 = /\n/g,
    Jl6 = /\s/,
    Cl6 = /\\(.?)/g,
    Xl6 = {
      "0": "\x00",
      r: "\r",
      n: `
`,
      t: "\t"
    };

  function FI2(A) {
    return A.replace(Cl6, function(B, Q) {
      switch (Q) {
        case "\\":
        case "":
          return Q;
        default:
          return Xl6[Q] || ""
      }
    })
  }
  JI2.unescape = FI2;

  function JI2(A, B) {
    A = A.toString();
    var Q = 0,
      I = A.length,
      G = 1,
      D = 0,
      Z = {},
      Y = [],
      W = null;

    function F(T) {
      return Error("illegal " + T + " (line " + G + ")")
    }

    function J() {
      var T = W === "'" ? Zl6 : Dl6;
      T.lastIndex = Q - 1;
      var O = T.exec(A);
      if (!O) throw F("string");
      return Q = T.lastIndex, N(W), W = null, FI2(O[1])
    }

    function C(T) {
      return A.charAt(T)
    }

    function X(T, O, S) {
      var f = {
          type: A.charAt(T++),
          lineEmpty: !1,
          leading: S
        },
        a;
      if (B) a = 2;
      else a = 3;
      var g = T - a,
        Y1;
      do
        if (--g < 0 || (Y1 = A.charAt(g)) === `
`) {
          f.lineEmpty = !0;
          break
        } while (Y1 === " " || Y1 === "\t");
      var r = A.substring(T, O).split(Fl6);
      for (var w1 = 0; w1 < r.length; ++w1) r[w1] = r[w1].replace(B ? Wl6 : Yl6, "").trim();
      f.text = r.join(`
`).trim(), Z[G] = f, D = G
    }

    function V(T) {
      var O = K(T),
        S = A.substring(T, O),
        f = /^\s*\/\//.test(S);
      return f
    }

    function K(T) {
      var O = T;
      while (O < I && C(O) !== `
`) O++;
      return O
    }

    function U() {
      if (Y.length > 0) return Y.shift();
      if (W) return J();
      var T, O, S, f, a, g = Q === 0;
      do {
        if (Q === I) return null;
        T = !1;
        while (Jl6.test(S = C(Q))) {
          if (S === `
`) g = !0, ++G;
          if (++Q === I) return null
        }
        if (C(Q) === "/") {
          if (++Q === I) throw F("comment");
          if (C(Q) === "/")
            if (!B) {
              a = C(f = Q + 1) === "/";
              while (C(++Q) !== `
`)
                if (Q === I) return null;
              if (++Q, a) X(f, Q - 1, g), g = !0;
              ++G, T = !0
            } else {
              if (f = Q, a = !1, V(Q - 1)) {
                a = !0;
                do {
                  if (Q = K(Q), Q === I) break;
                  if (Q++, !g) break
                } while (V(Q))
              } else Q = Math.min(I, K(Q) + 1);
              if (a) X(f, Q, g), g = !0;
              G++, T = !0
            }
          else if ((S = C(Q)) === "*") {
            f = Q + 1, a = B || C(f) === "*";
            do {
              if (S === `
`) ++G;
              if (++Q === I) throw F("comment");
              O = S, S = C(Q)
            } while (O !== "*" || S !== "/");
            if (++Q, a) X(f, Q - 2, g), g = !0;
            T = !0
          } else return "/"