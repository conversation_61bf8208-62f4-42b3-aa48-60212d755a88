// Chunk 88
// Lines 264001-267000
// Size: 94803 bytes

      MAVEN_OPTS: "-o"
    }
  }, {
    patterns: [/\bnode\b|\bnodemon\b|\bts-node\b/],
    env: {
      NODE_DISABLE_COLORS: "1",
      NO_UPDATE_NOTIFIER: "1",
      NODE_ENV: "production"
    }
  }, {
    patterns: [/\bpsql\b|\bmysql\b|\bmongo\b|\bredis-cli\b/],
    env: {
      PSQL_HISTORY: "/dev/null",
      MYSQL_HISTFILE: "/dev/null",
      REDISCLI_HISTFILE: "/dev/null"
    }
  }];

function Lj1(A) {
  let B = {},
    Q = [];
  if (/^\s*env\s+/.test(A)) return {
    env: {},
    configArgs: []
  };
  if (/^\s*RUN\s+/.test(A)) return {
    env: {},
    configArgs: []
  };
  if (/^\s*[`$(]|echo\s+[`$(]/.test(A)) return {
    env: {},
    configArgs: []
  };
  let I = Pv.parse(A),
    G = A,
    D = 0;
  for (let Z = 0; Z < I.length; Z++) {
    let Y = I[Z];
    if (typeof Y === "string") {
      if (Y.includes("=") && Z === D) {
        D = Z + 1;
        continue
      }
      break
    }
  }
  if (D < I.length) G = I.slice(D).map((Z) => {
    if (typeof Z === "string") return Pv.quote([Z]);
    return Z
  }).join(" ");
  for (let Z of jE4)
    if (Z.patterns.some((Y) => Y.test(G))) {
      if (B = {
          ...B,
          ...Z.env
        }, Z.configArgs) Q = [...Q, ...Z.configArgs]
    } return {
    env: B,
    configArgs: Q
  }
}

function g40(A) {
  let B = Pv.parse(A),
    Q = -1,
    I = 0;
  for (let G = 0; G < B.length; G++) {
    let D = B[G];
    if (typeof D === "string") {
      if (D.includes("=") && G === I) {
        I++;
        continue
      }
      if (D === "git" || D.endsWith("/git")) {
        Q = G;
        break
      }
      break
    }
  }
  if (Q !== -1) {
    let {
      configArgs: G
    } = Lj1(A);
    if (G && G.length > 0) {
      let D = [...B.slice(0, Q + 1), ...G, ...B.slice(Q + 1)];
      return D.map((Z, Y) => {
        if (typeof Z === "string") {
          if (Z.includes("=") && Y < Q || Z.startsWith("-c")) return Z;
          if (Y > 0 && D[Y - 1] === "-c" && Z.includes("=")) return Z;
          return Pv.quote([Z])
        }
        return ""
      }).filter((Z) => Z !== "").join(" ")
    }
  }
  return A
}
var dE4 = 1800000,
  Rj1 = "\\";

function u40(A) {
  let B = A.includes("zsh") ? ".zshrc" : A.includes("bash") ? ".bashrc" : ".profile";
  return mE4(fE4(), B)
}

function uE4(A, B) {
  let Q = u40(A),
    I = Q.endsWith(".zshrc"),
    G = "";
  if (I) G = `
      echo "# Functions" >> $SNAPSHOT_FILE
      
      # Force autoload all functions first
      typeset -f > /dev/null 2>&1
      
      # Now get user function names - filter system ones and write directly to file
      typeset +f | grep -vE '^(_|__)' | while read func; do
        typeset -f "$func" >> $SNAPSHOT_FILE
      done
      
      echo "# Shell Options" >> $SNAPSHOT_FILE
      setopt | sed 's/^/setopt /' | head -n 1000 >> $SNAPSHOT_FILE
    `;
  else G = `
      echo "# Functions" >> $SNAPSHOT_FILE
      
      # Force autoload all functions first
      declare -f > /dev/null 2>&1
      
      # Now get user function names - filter system ones and give the rest to eval in b64 encoding
      declare -F | cut -d' ' -f3 | grep -vE '^(_|__)' | while read func; do
        # Encode the function to base64, preserving all special characters
        encoded_func=$(declare -f "$func" | base64 )
        # Write the function definition to the snapshot
        echo "eval ${Rj1}"${Rj1}$(echo '$encoded_func' | base64 -d)${Rj1}" > /dev/null 2>&1" >> $SNAPSHOT_FILE
      done

      echo "# Shell Options" >> $SNAPSHOT_FILE
      shopt -p | head -n 1000 >> $SNAPSHOT_FILE
      set -o | grep "on" | awk '{print "set -o " $1}' | head -n 1000 >> $SNAPSHOT_FILE
      echo "shopt -s expand_aliases" >> $SNAPSHOT_FILE
    `;
  return `SNAPSHOT_FILE=${$i.default.quote([B])}
      source "${Q}" < /dev/null
      
      # First, create/clear the snapshot file
      echo "# Snapshot file" >| $SNAPSHOT_FILE
      
      # When this file is sourced, we first unalias to avoid conflicts
      # This is necessary because aliases get "frozen" inside function definitions at definition time,
      # which can cause unexpected behavior when functions use commands that conflict with aliases
      echo "# Unset all aliases to avoid conflicts with functions" >> $SNAPSHOT_FILE
      echo "unalias -a 2>/dev/null || true" >> $SNAPSHOT_FILE
      
      ${G}
      
      echo "# Aliases" >> $SNAPSHOT_FILE
      alias | sed 's/^alias //g' | sed 's/^/alias -- /' | head -n 1000 >> $SNAPSHOT_FILE
      
      # Add PATH to the file
      echo "export PATH='${process.env.PATH}'" >> $SNAPSHOT_FILE
    `
}

function m40(A) {
  try {
    return b1().accessSync(A, yE4.X_OK), !0
  } catch (B) {
    try {
      return d40(`${A} --version`, {
        timeout: 1000,
        stdio: "ignore"
      }), !0
    } catch {
      return !1
    }
  }
}
var p40 = b0(function() {
  let A = (J) => {
      try {
        return d40(`which ${J}`, {
          stdio: ["ignore", "pipe", "ignore"]
        }).toString().trim()
      } catch {
        return null
      }
    },
    B = process.env.SHELL,
    Q = B && (B.includes("bash") || B.includes("zsh")),
    I = B?.includes("bash"),
    G = A("zsh"),
    D = A("bash"),
    Z = ["/bin", "/usr/bin", "/usr/local/bin", "/opt/homebrew/bin"],
    W = (I ? ["bash", "zsh"] : ["zsh", "bash"]).flatMap((J) => Z.map((C) => `${C}/${J}`));
  if (I) {
    if (D) W.unshift(D);
    if (G) W.push(G)
  } else {
    if (G) W.unshift(G);
    if (D) W.push(D)
  }
  if (Q && m40(B)) W.unshift(B);
  let F = W.find((J) => J && m40(J));
  if (!F) {
    let J =
      "No suitable shell found. Claude CLI requires a Posix shell environment. Please ensure you have a valid shell installed and the SHELL environment variable set.";
    throw g1(new Error(J)), new Error(J)
  }
  return F
});

function pE4() {
  let A = Math.floor(Math.random() * 65536).toString(16).padStart(4, "0"),
    B = p40(),
    Q = `${Oj1.tmpdir()}/claude-shell-snapshot-${A}`;
  return new Promise((I) => {
    try {
      let G = u40(B);
      if (!h40(G)) {
        I(void 0);
        return
      }
      let D = uE4(B, Q);
      vE4(B, ["-c", "-l", D], {
        env: {
          ...process.env.CLAUDE_CODE_DONT_INHERIT_ENV ? {} : process.env,
          SHELL: B,
          GIT_EDITOR: "true",
          CLAUDECODE: "1"
        },
        timeout: 1e4,
        maxBuffer: 1048576
      }, (Z, Y, W) => {
        if (Z) g1(new Error(`Failed to create shell snapshot: ${W}`)), j1("shell_snapshot_failed", {
          stderr_length: W.length
        }), I(void 0);
        else if (h40(Q)) {
          let F = xE4(Q).size;
          j1("shell_snapshot_created", {
            snapshot_size: F
          }), I(Q)
        } else j1("shell_unknown_error", {}), I(void 0)
      })
    } catch (G) {
      g1(G instanceof Error ? G : new Error(String(G))), j1("shell_snapshot_error", {}), I(void 0)
    }
  })
}
var Tj1 = b0(async function() {
  let A = await pE4();
  return {
    binShell: p40(),
    snapshotFilePath: A
  }
});
async function cE4(A, B, Q, I = !1, G) {
  let D = Q || dE4,
    {
      binShell: Z,
      snapshotFilePath: Y
    } = await Tj1();
  if (G) Z = G, Y = void 0;
  let W = Math.floor(Math.random() * 65536).toString(16).padStart(4, "0"),
    F = `${Oj1.tmpdir()}/claude-${W}-cwd`,
    J = $i.default.quote([A, "<", "/dev/null"]);
  if (Z.includes("bash") && !I) {
    let U = A.split(/(?<!\|)\|(?!\|)/);
    if (U.length > 1) J = $i.default.quote([U[0], "<", "/dev/null", "|", U.slice(1).join("|")])
  }
  if (I) A = g40(A), J = $i.default.quote([A, "<", "/dev/null"]);
  let C = () => {};
  if (I) {
    let U = b40(J);
    J = U.finalCommand, C = U.cleanup
  }
  let X = [];
  if (Y) X.push(`source ${Y}`);
  X.push(`eval ${J}`), X.push(`pwd >| ${F}`);
  let V = X.join(" && "),
    K = c40();
  if (B.aborted) return k40();
  try {
    let U = Lj1(A),
      N = bE4(Z, ["-c", "-l", V], {
        env: {
          ...process.env,
          SHELL: Z,
          GIT_EDITOR: "true",
          CLAUDECODE: "1",
          ...I ? U.env : {}
        },
        cwd: K,
        detached: !0
      }),
      q = y40(N, B, D);
    return q.result.then((M) => {
      if (M && !M.backgroundTaskId) try {
        dW(kE4(F, {
          encoding: "utf8"
        }).trim(), K)
      } catch {
        j1("shell_set_cwd", {
          success: !1
        })
      }
      C()
    }).catch(() => {
      C()
    }), C = () => {}, q
  } finally {
    C()
  }
}

function c40() {
  return aGA()
}

function dW(A, B) {
  let Q = gE4(A) ? A : hE4(B || b1().cwd(), A);
  if (!b1().existsSync(Q)) throw new Error(`Path "${Q}" does not exist`);
  sGA(Q), j1("shell_set_cwd", {
    success: !0
  })
}
var lE4 = cE4;

function l40() {
  return lE4
}

function uA() {
  try {
    return c40()
  } catch {
    return u4()
  }
}
var Pj1 = 1000,
  Sj1 = 60;

function I2(A, B, Q, I = 10 * Sj1 * Pj1, G = !0, D = !0) {
  return PX(A, B, Q, I, G, D ? uA() : void 0)
}

function PX(A, B, Q, I = 10 * Sj1 * Pj1, G = !0, D) {
  return new Promise((Z) => {
    try {
      nE4(A, B, {
        maxBuffer: 1e6,
        signal: Q,
        timeout: I,
        cwd: D
      }, (Y, W, F) => {
        if (Y)
          if (G) {
            let J = typeof Y.code === "number" ? Y.code : 1;
            Z({
              stdout: W || "",
              stderr: F || "",
              code: J,
              error: typeof Y.code === "string" ? Y.code : String(J)
            })
          } else Z({
            stdout: "",
            stderr: "",
            code: 1
          });
        else Z({
          stdout: W,
          stderr: F,
          code: 0
        })
      })
    } catch (Y) {
      g1(Y), Z({
        stdout: "",
        stderr: "",
        code: 1
      })
    }
  })
}

function cG(A, B, Q = 10 * Sj1 * Pj1) {
  B?.throwIfAborted();
  let I = aE4(A, {
    env: process.env,
    maxBuffer: 1e6,
    timeout: Q,
    cwd: uA(),
    stdio: ["ignore", "pipe", "pipe"]
  });
  if (!I) return null;
  return I.toString().trim() || null
}
import {
  join as k31
} from "path";
import {
  homedir as i40
} from "os";
var n40 = J1(fq1(), 1);
var S5 = process.env.CLAUDE_CONFIG_DIR ?? k31(i40(), ".claude"),
  kJ = process.env.CLAUDE_CONFIG_DIR ? k31(S5, "config.json") : k31(i40(), ".claude.json"),
  Hn5 = k31(S5, "memory"),
  sE4 = b0(async () => {
    let {
      code: A
    } = await I2("test", ["-f", "/.dockerenv"]);
    if (A !== 0) return !1;
    return process.platform === "linux"
  }),
  rE4 = b0(async () => {
    try {
      let A = new AbortController,
        B = setTimeout(() => A.abort(), 1000);
      return await K5.head("http://*******", {
        signal: A.signal
      }), clearTimeout(B), !0
    } catch {
      return !1
    }
  });
async function Sv(A, B = ["--version"]) {
  return (await I2(A, B, void 0, 1000, !0, !1)).code === 0
}
var oE4 = b0(async () => {
    let A = [];
    if (await Sv("npm")) A.push("npm");
    if (await Sv("yarn")) A.push("yarn");
    if (await Sv("pnpm")) A.push("pnpm");
    return A
  }),
  tE4 = b0(async () => {
    let A = [];
    if (await Sv("bun")) A.push("bun");
    if (await Sv("deno")) A.push("deno");
    if (await Sv("node")) A.push("node");
    return A
  }),
  eE4 = b0(() => {
    if (process.versions.bun !== void 0 || process.env.BUN_INSTALL !== void 0) return !0;
    return !1
  }),
  a40 = b0(() => {
    try {
      return b1().existsSync("/proc/sys/fs/binfmt_misc/WSLInterop")
    } catch (A) {
      return !1
    }
  }),
  AU4 = b0(() => {
    try {
      if (!a40()) return !1;
      let {
        cmd: A
      } = n40.findActualExecutable("npm", []);
      return A.startsWith("/mnt/c/")
    } catch (A) {
      return !1
    }
  });

function BU4() {
  if (process.env.CURSOR_TRACE_ID) return "cursor";
  if (process.env.VSCODE_GIT_ASKPASS_MAIN?.includes("/.cursor-server/bin/")) return "cursor";
  if (process.env.VSCODE_GIT_ASKPASS_MAIN?.includes("/.windsurf-server/bin/")) return "windsurf";
  let A = process.env.__CFBundleIdentifier?.toLowerCase();
  if (A?.includes("windsurf")) return "windsurf";
  if (A?.includes("pycharm")) return "pycharm";
  if (A?.includes("intellij")) return "intellij";
  if (A?.includes("webstorm")) return "webstorm";
  if (A?.includes("phpstorm")) return "phpstorm";
  if (A?.includes("rubymine")) return "rubymine";
  if (A?.includes("clion")) return "clion";
  if (A?.includes("goland")) return "goland";
  if (A?.includes("rider")) return "rider";
  if (A?.includes("datagrip")) return "datagrip";
  if (A?.includes("appcode")) return "appcode";
  if (A?.includes("dataspell")) return "dataspell";
  if (A?.includes("aqua")) return "aqua";
  if (A?.includes("gateway")) return "gateway";
  if (A?.includes("fleet")) return "fleet";
  if (A?.includes("com.google.android.studio")) return "androidstudio";
  if (process.env.TERMINAL_EMULATOR === "JetBrains-JediTerm") return "pycharm";
  if (process.env.TERM === "xterm-ghostty") return "ghostty";
  if (process.env.TERM?.includes("kitty")) return "kitty";
  if (process.env.TERM_PROGRAM) return process.env.TERM_PROGRAM;
  if (process.env.TMUX) return "tmux";
  if (process.env.STY) return "screen";
  if (process.env.KONSOLE_VERSION) return "konsole";
  if (process.env.GNOME_TERMINAL_SERVICE) return "gnome-terminal";
  if (process.env.XTERM_VERSION) return "xterm";
  if (process.env.VTE_VERSION) return "vte-based";
  if (process.env.TERMINATOR_UUID) return "terminator";
  if (process.env.KITTY_WINDOW_ID) return "kitty";
  if (process.env.ALACRITTY_LOG) return "alacritty";
  if (process.env.TILIX_ID) return "tilix";
  if (process.env.WT_SESSION) return "windows-terminal";
  if (process.env.SESSIONNAME && process.env.TERM === "cygwin") return "cygwin";
  if (process.env.MSYSTEM) return process.env.MSYSTEM.toLowerCase();
  if (process.env.ConEmuTask) return "conemu";
  if (process.env.WSL_DISTRO_NAME) return `wsl-${process.env.WSL_DISTRO_NAME}`;
  if (process.env.SSH_CONNECTION || process.env.SSH_CLIENT || process.env.SSH_TTY) return "ssh-session";
  if (process.env.TERM) {
    let B = process.env.TERM;
    if (B.includes("alacritty")) return "alacritty";
    if (B.includes("rxvt")) return "rxvt";
    if (B.includes("termite")) return "termite";
    return process.env.TERM
  }
  if (!process.stdout.isTTY) return "non-interactive";
  return null
}
var dA = {
  getIsDocker: sE4,
  hasInternetAccess: rE4,
  isCI: Boolean(!1),
  platform: process.platform === "win32" ? "windows" : process.platform === "darwin" ? "macos" : "linux",
  nodeVersion: process.version,
  terminal: BU4(),
  getPackageManagers: oE4,
  getRuntimes: tE4,
  isRunningWithBun: eE4,
  isWslEnvironment: a40,
  isNpmFromWindowsPath: AU4
};
if (dA.isCI) console.warn("Running in CI environment - interactive features are limited");

function DP(A) {
  if (!A) return !1;
  let B = A.toLowerCase().trim();
  return ["1", "true", "yes", "on"].includes(B)
}

function s40(A) {
  let B = {};
  if (A)
    for (let Q of A) {
      let [I, ...G] = Q.split("=");
      if (!I || G.length === 0) throw new Error(
        `Invalid environment variable format: ${Q}, environment variables should be added as: -e KEY1=value1 -e KEY2=value2`
        );
      B[I] = G.join("=")
    }
  return B
}
import {
  randomBytes as YU4
} from "crypto";
class _j1 extends TypeError {}
class QG extends Error {}
class _v extends Error {
  filePath;
  defaultConfig;
  constructor(A, B, Q) {
    super(A);
    this.name = "ConfigParseError", this.filePath = B, this.defaultConfig = Q
  }
}
class bP extends Error {
  stdout;
  stderr;
  code;
  interrupted;
  constructor(A, B, Q, I) {
    super("Shell command failed");
    this.stdout = A;
    this.stderr = B;
    this.code = Q;
    this.interrupted = I;
    this.name = "ShellError"
  }
}

function qi(A, B) {
  return A instanceof Error && A.message === B
}
var jj1 = i.enum(["local", "user", "project"]),
  Un5 = i.enum(["stdio", "sse", "sse-ide"]),
  yj1 = i.object({
    type: i.literal("stdio").optional(),
    command: i.string().min(1, "Command cannot be empty"),
    args: i.array(i.string()).default([]),
    env: i.record(i.string()).optional()
  }),
  QU4 = i.object({
    type: i.literal("sse"),
    url: i.string().url("Must be a valid URL"),
    headers: i.record(i.string()).optional()
  }),
  IU4 = i.object({
    type: i.literal("sse-ide"),
    url: i.string().url("Must be a valid URL"),
    ideName: i.string()
  }),
  GU4 = i.object({
    type: i.literal("ws-ide"),
    url: i.string().url("Must be a valid URL"),
    ideName: i.string()
  }),
  kj1 = i.union([yj1, QU4, IU4, GU4]);
var jv = i.object({
  mcpServers: i.record(i.string(), kj1)
});
var TM = {
  allowedTools: [],
  history: [],
  dontCrawlDirectory: !1,
  mcpContextUris: [],
  mcpServers: {},
  enabledMcpjsonServers: [],
  disabledMcpjsonServers: [],
  enableAllProjectMcpServers: !1,
  hasTrustDialogAccepted: !1,
  ignorePatterns: [],
  projectOnboardingSeenCount: 0,
  hasClaudeMdExternalIncludesApproved: !1,
  hasClaudeMdExternalIncludesWarningShown: !1
};

function r40(A) {
  let B = {
    ...TM
  };
  if (A === ZU4()) B.dontCrawlDirectory = !0;
  return B
}

function WU4(A) {
  return ["disabled", "enabled", "no_permissions", "not_configured", "migrated"].includes(A)
}
var xJ = {
    numStartups: 0,
    autoUpdaterStatus: "not_configured",
    theme: "dark",
    preferredNotifChannel: "auto",
    verbose: !1,
    editorMode: "normal",
    autoCompactEnabled: !0,
    hasSeenTasksHint: !1,
    queuedCommandUpHintCount: 0,
    diffTool: "auto",
    customApiKeyResponses: {
      approved: [],
      rejected: []
    },
    env: {},
    tipsHistory: {},
    memoryUsageCount: 0,
    parallelAgents: {
      enabled: !1,
      count: 1
    },
    promptQueueUseCount: 0,
    todoFeatureEnabled: !0,
    messageIdleNotifThresholdMs: 60000
  },
  Mi = ["apiKeyHelper", "autoUpdaterStatus", "theme", "verbose", "preferredNotifChannel",
    "shiftEnterKeyBindingInstalled", "editorMode", "hasUsedBackslashReturn", "supervisorMode", "autoCompactEnabled",
    "diffTool", "env", "tipsHistory", "parallelAgents", "todoFeatureEnabled", "messageIdleNotifThresholdMs"
  ];

function bj1(A) {
  return Mi.includes(A)
}
var Li = ["allowedTools", "dontCrawlDirectory", "hasTrustDialogAccepted", "hasCompletedProjectOnboarding",
  "ignorePatterns"
];

function o40() {
  let A = uA(),
    B = hP(kJ, xJ);
  while (!0) {
    if (B.projects?.[A]?.hasTrustDialogAccepted) return !0;
    let I = vj1(A, "..");
    if (I === A) break;
    A = I
  }
  return !1
}
var un5 = {
    ...xJ,
    autoUpdaterStatus: "disabled"
  },
  pn5 = {
    ...TM
  };

function gj1(A) {
  return Li.includes(A)
}

function yv(A, B) {
  if (B) {
    let Q = VA();
    return A in Q && Array.isArray(Q[A])
  } else {
    let Q = TM[A];
    return A in TM && Array.isArray(Q)
  }
}

function FU4(A, B) {
  if (yv(A, B)) return !1;
  if (B) {
    let Q = VA();
    return A in Q && typeof Q[A] === "object"
  } else {
    let Q = TM[A];
    return A in TM && typeof Q === "object"
  }
}

function x31(A, B, Q, I = !0) {
  if (j1("tengu_config_add", {
      key: A,
      global: Q,
      count: B.length
    }), !yv(A, Q)) {
    if (Q) console.error(`Error: '${A}' is not a valid array config key in global config`);
    else console.error(`Error: '${A}' is not a valid array config key in project config`);
    if (I) process.exit(1);
    else return
  }
  if (Q) {
    let G = VA(),
      D = A,
      Z = G[D] || [],
      Y = new Set(Z),
      W = Y.size;
    for (let F of B) Y.add(F);
    if (Y.size > W) {
      let F = Array.from(Y).sort();
      T0({
        ...G,
        [D]: F
      })
    }
  } else {
    let G = E9(),
      D = A,
      Z = G[D] || [],
      Y = new Set(Z),
      W = Y.size;
    for (let F of B) Y.add(F);
    if (Y.size > W) {
      let F = Array.from(Y).sort();
      j6({
        ...G,
        [D]: F
      })
    }
  }
  if (I) process.exit(0)
}

function t40(A, B, Q, I = !0) {
  if (j1("tengu_config_remove", {
      key: A,
      global: Q,
      count: B.length
    }), Q) {
    let G = VA();
    if (!(A in G) || !Array.isArray(G[A]))
      if (console.error(`Error: '${A}' is not a valid array config key in global config`), I) process.exit(1);
      else return;
    let D = A,
      Z = G[D];
    if (!Z) Z = [];
    let Y = new Set(B),
      W = Z.filter((F) => !Y.has(F));
    if (Z.length !== W.length) T0({
      ...G,
      [D]: W.sort()
    })
  } else {
    let G = E9(),
      D = TM[A];
    if (!(A in TM) || !Array.isArray(D))
      if (console.error(`Error: '${A}' is not a valid array config key in project config`), I) process.exit(1);
      else return;
    let Z = A,
      Y = G[Z];
    if (!Y) Y = [];
    let W = new Set(B),
      F = Y.filter((J) => !W.has(J));
    if (Y.length !== F.length) j6({
      ...G,
      [Z]: F.sort()
    })
  }
  if (I) process.exit(0)
}

function T0(A) {
  A60(kJ, {
    ...A,
    projects: hP(kJ, xJ).projects
  }, xJ), gP.config = null, gP.mtime = 0
}
var gP = {
  config: null,
  mtime: 0
};

function VA() {
  try {
    let A = b1().existsSync(kJ) ? b1().statSync(kJ) : null;
    if (gP.config && A) {
      if (A.mtimeMs <= gP.mtime) return gP.config
    }
    let B = hP(kJ, xJ);
    if (A) gP = {
      config: B,
      mtime: A.mtimeMs
    };
    else gP = {
      config: B,
      mtime: Date.now()
    };
    return B
  } catch {
    return hP(kJ, xJ)
  }
}

function e40(A) {
  let B = VA();
  if (B.customApiKeyResponses?.approved?.includes(A)) return "approved";
  if (B.customApiKeyResponses?.rejected?.includes(A)) return "rejected";
  return "new"
}

function A60(A, B, Q) {
  let I = DU4(A),
    G = b1();
  if (!G.existsSync(I)) G.mkdirSync(I);
  let D = Object.fromEntries(Object.entries(B).filter(([Z, Y]) => JSON.stringify(Y) !== JSON.stringify(Q[Z])));
  nE(A, JSON.stringify(D, null, 2))
}
var fj1 = !1;

function B60() {
  if (fj1) return;
  fj1 = !0, hP(kJ, xJ, !0)
}

function hP(A, B, Q) {
  if (!fj1) throw new Error("Config accessed before allowed.");
  if (!b1().existsSync(A)) return ET(B);
  try {
    let I = b1().readFileSync(A, {
      encoding: "utf-8"
    });
    try {
      let G = JSON.parse(I);
      return {
        ...ET(B),
        ...G
      }
    } catch (G) {
      let D = G instanceof Error ? G.message : String(G);
      throw new _v(D, A, B)
    }
  } catch (I) {
    if (I instanceof _v && Q) throw I;
    return ET(B)
  }
}

function E9() {
  let A = vj1(u4()),
    B = hP(kJ, xJ);
  if (!B.projects) return r40(A);
  let Q = B.projects[A] ?? r40(A);
  if (typeof Q.allowedTools === "string") Q.allowedTools = T8(Q.allowedTools) ?? [];
  return Q
}

function j6(A) {
  let B = hP(kJ, xJ);
  A60(kJ, {
    ...B,
    projects: {
      ...B.projects,
      [vj1(u4())]: A
    }
  }, xJ)
}

function f31() {
  return !!(process.env.DISABLE_AUTOUPDATER || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC || VA()
    .autoUpdaterStatus === "disabled")
}

function v31() {
  let A = B3();
  if (bW(A?.scopes)) return !1;
  let Q = VA(),
    I = Q.oauthAccount?.organizationRole,
    G = Q.oauthAccount?.workspaceRole;
  if (!I || !G) return !0;
  return ["admin", "billing"].includes(I) || ["workspace_admin", "workspace_billing"].includes(G)
}

function JU4(A) {
  let B = T8(A),
    Q = {};
  if (B && typeof B === "object") {
    let I = jv.safeParse(B);
    if (I.success) {
      let G = I.data;
      for (let [D, Z] of Object.entries(G.mcpServers)) Q[D] = Z
    }
  }
  return Q
}

function hj1(A) {
  let B = xj1(uA(), ".mcp.json");
  nE(B, JSON.stringify(A, null, 2), {
    encoding: "utf8"
  })
}
var zU = b0(() => {
  let A = xj1(uA(), ".mcp.json");
  if (!b1().existsSync(A)) return {};
  try {
    let B = b1().readFileSync(A, {
        encoding: "utf-8"
      }),
      Q = JU4(B);
    return j1("tengu_mcpjson_found", {
      numServers: Object.keys(Q).length
    }), Q
  } catch {}
  return {}
}, () => {
  let A = uA(),
    B = xj1(A, ".mcp.json");
  if (b1().existsSync(B)) try {
    let Q = b1().readFileSync(B, {
      encoding: "utf-8"
    });
    return `${A}:${Q}`
  } catch {
    return A
  }
  return A
});

function mP() {
  let A = VA();
  if (A.userID) return A.userID;
  let B = YU4(32).toString("hex");
  return T0({
    ...A,
    userID: B
  }), B
}

function Q60() {
  let A = VA();
  if (!A.firstStartTime) T0({
    ...A,
    firstStartTime: new Date().toISOString()
  })
}

function I60(A, B) {
  if (j1("tengu_config_get", {
      key: A,
      global: B
    }), B) {
    if (!bj1(A)) console.error(`Error: '${A}' is not a valid config key. Valid keys are: ${Mi.join(", ")}`), process
      .exit(1);
    return VA()[A]
  } else {
    if (!gj1(A)) console.error(`Error: '${A}' is not a valid config key. Valid keys are: ${Li.join(", ")}`), process
      .exit(1);
    return E9()[A]
  }
}

function G60(A, B, Q) {
  if (j1("tengu_config_set", {
      key: A,
      global: Q
    }), Q) {
    if (!bj1(A)) console.error(`Error: Cannot set '${A}'. Only these keys can be modified: ${Mi.join(", ")}`), process
      .exit(1);
    if (A === "autoUpdaterStatus" && !WU4(B)) console.error(
        "Error: Invalid value for autoUpdaterStatus. Must be one of: disabled, enabled, no_permissions, not_configured"
        ), process.exit(1);
    if (FU4(A, Q) && typeof B === "string") try {
      let G = JSON.parse(B);
      if (typeof G !== "object" || G === null || Array.isArray(G)) console.error(
        "Error: 'env' must be a valid JSON object"), process.exit(1);
      let D = VA();
      T0({
        ...D,
        [A]: G
      }), process.exit(0)
    } catch (G) {
      console.error(`Error: Failed to parse JSON for 'env': ${G instanceof Error?G.message:String(G)}`), process.exit(
        1)
    }
    if (yv(A, Q) && typeof B === "string") {
      console.warn(wA.yellow(
        `Warning: '${A}' is an array type. Automatically using 'config add' instead of 'config set'.`));
      let G = B.split(",").map((D) => D.trim()).filter((D) => D.length > 0);
      x31(A, G, Q);
      return
    }
    let I = VA();
    T0({
      ...I,
      [A]: B
    })
  } else {
    if (!gj1(A)) console.error(
        `Error: Cannot set '${A}'. Only these keys can be modified: ${Li.join(", ")}. Did you mean --global?`), process
      .exit(1);
    if (yv(A, Q) && typeof B === "string") {
      console.warn(wA.yellow(
        `Warning: '${A}' is an array type. Automatically using 'config add' instead of 'config set'.`));
      let G = B.split(",").map((D) => D.trim()).filter((D) => D.length > 0);
      x31(A, G, Q);
      return
    }
    let I = E9();
    j6({
      ...I,
      [A]: B
    })
  }
  process.exit(0)
}

function D60(A, B) {
  if (j1("tengu_config_delete", {
      key: A,
      global: B
    }), B) {
    if (!bj1(A)) console.error(`Error: Cannot delete '${A}'. Only these keys can be modified: ${Mi.join(", ")}`),
      process.exit(1);
    let Q = VA();
    delete Q[A], T0(Q)
  } else {
    if (!gj1(A)) console.error(
        `Error: Cannot delete '${A}'. Only these keys can be modified: ${Li.join(", ")}. Did you mean --global?`),
      process.exit(1);
    let Q = E9();
    delete Q[A], j6(Q)
  }
}

function Z60(A) {
  if (j1("tengu_config_list", {
      global: A
    }), A) return f21(VA(), Mi);
  else return f21(E9(), Li)
}
import {
  execSync as CU4
} from "child_process";
var Ov = b0(async () => {
  let A = mP(),
    B = VA();
  return {
    customIDs: {
      sessionId: B5
    },
    userID: A,
    appVersion: {
      ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
      PACKAGE_URL: "@anthropic-ai/claude-code",
      README_URL: "https://docs.anthropic.com/s/claude-code",
      VERSION: "1.0.3"
    }.VERSION,
    email: XU4(),
    custom: {
      userType: "external",
      organizationUuid: B.oauthAccount?.organizationUuid,
      accountUuid: B.oauthAccount?.accountUuid,
      ...process.env.GITHUB_ACTIONS === "true" && {
        githubActor: process.env.GITHUB_ACTOR,
        githubActorId: process.env.GITHUB_ACTOR_ID,
        githubRepositoryOwner: process.env.GITHUB_REPOSITORY_OWNER,
        githubRepositoryOwnerId: process.env.GITHUB_REPOSITORY_OWNER_ID
      }
    }
  }
});

function XU4() {
  return;
  try {
    return CU4("git config --get user.email").toString().trim()
  } catch {
    return
  }
}

function Y60() {
  KY.init({
    dsn: gjA,
    environment: "external",
    release: {
      ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
      PACKAGE_URL: "@anthropic-ai/claude-code",
      README_URL: "https://docs.anthropic.com/s/claude-code",
      VERSION: "1.0.3"
    }.VERSION,
    integrations: [new KY.Integrations.Http({
        tracing: !0
      }), new KY.Integrations.Modules, new KY.Integrations.Console, new KY.Integrations.FunctionToString, new KY
      .Integrations.LinkedErrors
    ],
    tracesSampleRate: 1,
    tracePropagationTargets: ["localhost"]
  })
}
async function f41(A) {
  try {
    let B = await Ov();
    KY.setExtras({
      nodeVersion: dA.nodeVersion,
      platform: dA.platform,
      isCI: dA.isCI,
      isTest: !1,
      packageVersion: {
        ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
        PACKAGE_URL: "@anthropic-ai/claude-code",
        README_URL: "https://docs.anthropic.com/s/claude-code",
        VERSION: "1.0.3"
      }.VERSION,
      sessionId: B5,
      statsigGates: E40(),
      terminal: dA.terminal,
      userType: "external"
    }), KY.setUser({
      id: B.userID,
      email: B.email
    }), KY.captureException(A)
  } catch {}
}

function W60() {
  let A = process.env.ANTHROPIC_MODEL,
    B = s7();
  Object.assign(process.env, VA().env), Object.assign(process.env, B.env)
}
var oG = J1(_1(), 1);
import {
  Stream as JM4
} from "node:stream";
var nB0 = J1(_1(), 1);

function F60(A, B, {
  signal: Q,
  edges: I
} = {}) {
  let G = void 0,
    D = null,
    Z = I != null && I.includes("leading"),
    Y = I == null || I.includes("trailing"),
    W = () => {
      if (D !== null) A.apply(G, D), G = void 0, D = null
    },
    F = () => {
      if (Y) W();
      V()
    },
    J = null,
    C = () => {
      if (J != null) clearTimeout(J);
      J = setTimeout(() => {
        J = null, F()
      }, B)
    },
    X = () => {
      if (J !== null) clearTimeout(J), J = null
    },
    V = () => {
      X(), G = void 0, D = null
    },
    K = () => {
      X(), W()
    },
    U = function(...N) {
      if (Q?.aborted) return;
      G = this, D = N;
      let q = J == null;
      if (C(), Z && q) W()
    };
  return U.schedule = C, U.cancel = V, U.flush = K, Q?.addEventListener("abort", V, {
    once: !0
  }), U
}

function J60(A, B = 0, Q = {}) {
  if (typeof Q !== "object") Q = {};
  let {
    signal: I,
    leading: G = !1,
    trailing: D = !0,
    maxWait: Z
  } = Q, Y = Array(2);
  if (G) Y[0] = "leading";
  if (D) Y[1] = "trailing";
  let W = void 0,
    F = null,
    J = F60(function(...V) {
      W = A.apply(this, V), F = null
    }, B, {
      signal: I,
      edges: Y
    }),
    C = function(...V) {
      if (Z != null) {
        if (F === null) F = Date.now();
        else if (Date.now() - F >= Z) return W = A.apply(this, V), F = Date.now(), J.cancel(), J.schedule(), W
      }
      return J.apply(this, V), W
    },
    X = () => {
      return J.flush(), W
    };
  return C.cancel = J.cancel, C.flush = X, C
}

function b31(A, B = 0, Q = {}) {
  if (typeof Q !== "object") Q = {};
  let {
    leading: I = !0,
    trailing: G = !0,
    signal: D
  } = Q;
  return J60(A, B, {
    leading: I,
    trailing: G,
    signal: D,
    maxWait: B
  })
}
var PM = {};
Nu(PM, {
  scrollUp: () => jU4,
  scrollDown: () => yU4,
  link: () => gU4,
  image: () => hU4,
  iTerm: () => mU4,
  exitAlternativeScreen: () => vU4,
  eraseUp: () => _U4,
  eraseStartLine: () => PU4,
  eraseScreen: () => mj1,
  eraseLines: () => OU4,
  eraseLine: () => K60,
  eraseEndLine: () => TU4,
  eraseDown: () => SU4,
  enterAlternativeScreen: () => fU4,
  cursorUp: () => X60,
  cursorTo: () => HU4,
  cursorShow: () => uj1,
  cursorSavePosition: () => NU4,
  cursorRestorePosition: () => $U4,
  cursorPrevLine: () => LU4,
  cursorNextLine: () => MU4,
  cursorMove: () => zU4,
  cursorLeft: () => V60,
  cursorHide: () => RU4,
  cursorGetPosition: () => qU4,
  cursorForward: () => EU4,
  cursorDown: () => wU4,
  cursorBackward: () => UU4,
  clearTerminal: () => xU4,
  clearScreen: () => kU4,
  beep: () => bU4
});
import dj1 from "node:process";
var g31 = globalThis.window?.document !== void 0,
  Va5 = globalThis.process?.versions?.node !== void 0,
  Ka5 = globalThis.process?.versions?.bun !== void 0,
  Ha5 = globalThis.Deno?.version?.deno !== void 0,
  za5 = globalThis.process?.versions?.electron !== void 0,
  wa5 = globalThis.navigator?.userAgent?.includes("jsdom") === !0,
  Ea5 = typeof WorkerGlobalScope !== "undefined" && globalThis instanceof WorkerGlobalScope,
  Ua5 = typeof DedicatedWorkerGlobalScope !== "undefined" && globalThis instanceof DedicatedWorkerGlobalScope,
  Na5 = typeof SharedWorkerGlobalScope !== "undefined" && globalThis instanceof SharedWorkerGlobalScope,
  $a5 = typeof ServiceWorkerGlobalScope !== "undefined" && globalThis instanceof ServiceWorkerGlobalScope,
  Ri = globalThis.navigator?.userAgentData?.platform,
  qa5 = Ri === "macOS" || globalThis.navigator?.platform === "MacIntel" || globalThis.navigator?.userAgent?.includes(
    " Mac ") === !0 || globalThis.process?.platform === "darwin",
  Ma5 = Ri === "Windows" || globalThis.navigator?.platform === "Win32" || globalThis.process?.platform === "win32",
  La5 = Ri === "Linux" || globalThis.navigator?.platform?.startsWith("Linux") === !0 || globalThis.navigator?.userAgent
  ?.includes(" Linux ") === !0 || globalThis.process?.platform === "linux",
  Ra5 = Ri === "iOS" || globalThis.navigator?.platform === "MacIntel" && globalThis.navigator?.maxTouchPoints > 1 ||
  /iPad|iPhone|iPod/.test(globalThis.navigator?.platform),
  Oa5 = Ri === "Android" || globalThis.navigator?.platform === "Android" || globalThis.navigator?.userAgent?.includes(
    " Android ") === !0 || globalThis.process?.platform === "android";
var j5 = "\x1B[",
  Ti = "\x1B]",
  kv = "\x07",
  Oi = ";",
  C60 = !g31 && dj1.env.TERM_PROGRAM === "Apple_Terminal",
  VU4 = !g31 && dj1.platform === "win32",
  KU4 = g31 ? () => {
    throw new Error("`process.cwd()` only works in Node.js, not the browser.")
  } : dj1.cwd,
  HU4 = (A, B) => {
    if (typeof A !== "number") throw new TypeError("The `x` argument is required");
    if (typeof B !== "number") return j5 + (A + 1) + "G";
    return j5 + (B + 1) + Oi + (A + 1) + "H"
  },
  zU4 = (A, B) => {
    if (typeof A !== "number") throw new TypeError("The `x` argument is required");
    let Q = "";
    if (A < 0) Q += j5 + -A + "D";
    else if (A > 0) Q += j5 + A + "C";
    if (B < 0) Q += j5 + -B + "A";
    else if (B > 0) Q += j5 + B + "B";
    return Q
  },
  X60 = (A = 1) => j5 + A + "A",
  wU4 = (A = 1) => j5 + A + "B",
  EU4 = (A = 1) => j5 + A + "C",
  UU4 = (A = 1) => j5 + A + "D",
  V60 = j5 + "G",
  NU4 = C60 ? "\x1B7" : j5 + "s",
  $U4 = C60 ? "\x1B8" : j5 + "u",
  qU4 = j5 + "6n",
  MU4 = j5 + "E",
  LU4 = j5 + "F",
  RU4 = j5 + "?25l",
  uj1 = j5 + "?25h",
  OU4 = (A) => {
    let B = "";
    for (let Q = 0; Q < A; Q++) B += K60 + (Q < A - 1 ? X60() : "");
    if (A) B += V60;
    return B
  },
  TU4 = j5 + "K",
  PU4 = j5 + "1K",
  K60 = j5 + "2K",
  SU4 = j5 + "J",
  _U4 = j5 + "1J",
  mj1 = j5 + "2J",
  jU4 = j5 + "S",
  yU4 = j5 + "T",
  kU4 = "\x1Bc",
  xU4 = VU4 ? `${mj1}${j5}0f` : `${mj1}${j5}3J${j5}H`,
  fU4 = j5 + "?1049h",
  vU4 = j5 + "?1049l",
  bU4 = kv,
  gU4 = (A, B) => [Ti, "8", Oi, Oi, B, kv, A, Ti, "8", Oi, Oi, kv].join(""),
  hU4 = (A, B = {}) => {
    let Q = `${Ti}1337;File=inline=1`;
    if (B.width) Q += `;width=${B.width}`;
    if (B.height) Q += `;height=${B.height}`;
    if (B.preserveAspectRatio === !1) Q += ";preserveAspectRatio=0";
    return Q + ":" + Buffer.from(A).toString("base64") + kv
  },
  mU4 = {
    setCwd: (A = KU4()) => `${Ti}50;CurrentDir=${A}${kv}`,
    annotation(A, B = {}) {
      let Q = `${Ti}1337;`,
        I = B.x !== void 0,
        G = B.y !== void 0;
      if ((I || G) && !(I && G && B.length !== void 0)) throw new Error(
        "`x`, `y` and `length` must be defined when `x` or `y` is defined");
      if (A = A.replaceAll("|", ""), Q += B.isHidden ? "AddHiddenAnnotation=" : "AddAnnotation=", B.length > 0) Q += (
        I ? [A, B.length, B.x, B.y] : [B.length, A]).join("|");
      else Q += A;
      return Q + kv
    }
  };
var dU4 = (A) => {
  let B = new Set;
  do
    for (let Q of Reflect.ownKeys(A)) B.add([A, Q]); while ((A = Reflect.getPrototypeOf(A)) && A !== Object
    .prototype);
  return B
};

function pj1(A, {
  include: B,
  exclude: Q
} = {}) {
  let I = (G) => {
    let D = (Z) => typeof Z === "string" ? G === Z : Z.test(G);
    if (B) return B.some(D);
    if (Q) return !Q.some(D);
    return !0
  };
  for (let [G, D] of dU4(A.constructor.prototype)) {
    if (D === "constructor" || !I(D)) continue;
    let Z = Reflect.getOwnPropertyDescriptor(G, D);
    if (Z && typeof Z.value === "function") A[D] = A[D].bind(A)
  }
  return A
}
var dP = [];
dP.push("SIGHUP", "SIGINT", "SIGTERM");
if (process.platform !== "win32") dP.push("SIGALRM", "SIGABRT", "SIGVTALRM", "SIGXCPU", "SIGXFSZ", "SIGUSR2", "SIGTRAP",
  "SIGSYS", "SIGQUIT", "SIGIOT");
if (process.platform === "linux") dP.push("SIGIO", "SIGPOLL", "SIGPWR", "SIGSTKFLT");
var h31 = (A) => !!A && typeof A === "object" && typeof A.removeListener === "function" && typeof A.emit ===
  "function" && typeof A.reallyExit === "function" && typeof A.listeners === "function" && typeof A.kill ===
  "function" && typeof A.pid === "number" && typeof A.on === "function",
  cj1 = Symbol.for("signal-exit emitter"),
  lj1 = globalThis,
  uU4 = Object.defineProperty.bind(Object);
class H60 {
  emitted = {
    afterExit: !1,
    exit: !1
  };
  listeners = {
    afterExit: [],
    exit: []
  };
  count = 0;
  id = Math.random();
  constructor() {
    if (lj1[cj1]) return lj1[cj1];
    uU4(lj1, cj1, {
      value: this,
      writable: !1,
      enumerable: !1,
      configurable: !1
    })
  }
  on(A, B) {
    this.listeners[A].push(B)
  }
  removeListener(A, B) {
    let Q = this.listeners[A],
      I = Q.indexOf(B);
    if (I === -1) return;
    if (I === 0 && Q.length === 1) Q.length = 0;
    else Q.splice(I, 1)
  }
  emit(A, B, Q) {
    if (this.emitted[A]) return !1;
    this.emitted[A] = !0;
    let I = !1;
    for (let G of this.listeners[A]) I = G(B, Q) === !0 || I;
    if (A === "exit") I = this.emit("afterExit", B, Q) || I;
    return I
  }
}
class nj1 {}
var pU4 = (A) => {
  return {
    onExit(B, Q) {
      return A.onExit(B, Q)
    },
    load() {
      return A.load()
    },
    unload() {
      return A.unload()
    }
  }
};
class z60 extends nj1 {
  onExit() {
    return () => {}
  }
  load() {}
  unload() {}
}
class w60 extends nj1 {
  #A = ij1.platform === "win32" ? "SIGINT" : "SIGHUP";
  #B = new H60;
  #Q;
  #I;
  #G;
  #W = {};
  #D = !1;
  constructor(A) {
    super();
    this.#Q = A, this.#W = {};
    for (let B of dP) this.#W[B] = () => {
      let Q = this.#Q.listeners(B),
        {
          count: I
        } = this.#B,
        G = A;
      if (typeof G.__signal_exit_emitter__ === "object" && typeof G.__signal_exit_emitter__.count === "number") I +=
        G.__signal_exit_emitter__.count;
      if (Q.length === I) {
        this.unload();
        let D = this.#B.emit("exit", null, B),
          Z = B === "SIGHUP" ? this.#A : B;
        if (!D) A.kill(A.pid, Z)
      }
    };
    this.#G = A.reallyExit, this.#I = A.emit
  }
  onExit(A, B) {
    if (!h31(this.#Q)) return () => {};
    if (this.#D === !1) this.load();
    let Q = B?.alwaysLast ? "afterExit" : "exit";
    return this.#B.on(Q, A), () => {
      if (this.#B.removeListener(Q, A), this.#B.listeners.exit.length === 0 && this.#B.listeners.afterExit
        .length === 0) this.unload()
    }
  }
  load() {
    if (this.#D) return;
    this.#D = !0, this.#B.count += 1;
    for (let A of dP) try {
      let B = this.#W[A];
      if (B) this.#Q.on(A, B)
    } catch (B) {}
    this.#Q.emit = (A, ...B) => {
      return this.#F(A, ...B)
    }, this.#Q.reallyExit = (A) => {
      return this.#J(A)
    }
  }
  unload() {
    if (!this.#D) return;
    this.#D = !1, dP.forEach((A) => {
      let B = this.#W[A];
      if (!B) throw new Error("Listener not defined for signal: " + A);
      try {
        this.#Q.removeListener(A, B)
      } catch (Q) {}
    }), this.#Q.emit = this.#I, this.#Q.reallyExit = this.#G, this.#B.count -= 1
  }
  #J(A) {
    if (!h31(this.#Q)) return 0;
    return this.#Q.exitCode = A || 0, this.#B.emit("exit", this.#Q.exitCode, null), this.#G.call(this.#Q, this.#Q
      .exitCode)
  }
  #F(A, ...B) {
    let Q = this.#I;
    if (A === "exit" && h31(this.#Q)) {
      if (typeof B[0] === "number") this.#Q.exitCode = B[0];
      let I = Q.call(this.#Q, A, ...B);
      return this.#B.emit("exit", this.#Q.exitCode, null), I
    } else return Q.call(this.#Q, A, ...B)
  }
}
var ij1 = globalThis.process,
  {
    onExit: E60,
    load: fa5,
    unload: va5
  } = pU4(h31(ij1) ? new w60(ij1) : new z60);
import {
  PassThrough as U60
} from "node:stream";
var N60 = ["assert", "count", "countReset", "debug", "dir", "dirxml", "error", "group", "groupCollapsed", "groupEnd",
    "info", "log", "table", "time", "timeEnd", "timeLog", "trace", "warn"
  ],
  aj1 = {},
  cU4 = (A) => {
    let B = new U60,
      Q = new U60;
    B.write = (G) => {
      A("stdout", G)
    }, Q.write = (G) => {
      A("stderr", G)
    };
    let I = new console.Console(B, Q);
    for (let G of N60) aj1[G] = console[G], console[G] = I[G];
    return () => {
      for (let G of N60) console[G] = aj1[G];
      aj1 = {}
    }
  },
  $60 = cU4;
var i80 = J1(_60(), 1);
var Zy1 = 16;
var Y2 = {},
  n31 = Y2.ALIGN_AUTO = 0,
  ji = Y2.ALIGN_FLEX_START = 1,
  yi = Y2.ALIGN_CENTER = 2,
  ki = Y2.ALIGN_FLEX_END = 3,
  a31 = Y2.ALIGN_STRETCH = 4,
  j60 = Y2.ALIGN_BASELINE = 5,
  y60 = Y2.ALIGN_SPACE_BETWEEN = 6,
  k60 = Y2.ALIGN_SPACE_AROUND = 7,
  x60 = Y2.DIMENSION_WIDTH = 0,
  f60 = Y2.DIMENSION_HEIGHT = 1,
  v60 = Y2.DIRECTION_INHERIT = 0,
  b60 = Y2.DIRECTION_LTR = 1,
  g60 = Y2.DIRECTION_RTL = 2,
  fv = Y2.DISPLAY_FLEX = 0,
  _M = Y2.DISPLAY_NONE = 1,
  Bz = Y2.EDGE_LEFT = 0,
  jM = Y2.EDGE_TOP = 1,
  Qz = Y2.EDGE_RIGHT = 2,
  yM = Y2.EDGE_BOTTOM = 3,
  s31 = Y2.EDGE_START = 4,
  r31 = Y2.EDGE_END = 5,
  xi = Y2.EDGE_HORIZONTAL = 6,
  fi = Y2.EDGE_VERTICAL = 7,
  vi = Y2.EDGE_ALL = 8,
  h60 = Y2.EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS = 0,
  m60 = Y2.EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE = 1,
  d60 = Y2.EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN = 2,
  o31 = Y2.FLEX_DIRECTION_COLUMN = 0,
  t31 = Y2.FLEX_DIRECTION_COLUMN_REVERSE = 1,
  e31 = Y2.FLEX_DIRECTION_ROW = 2,
  AQ1 = Y2.FLEX_DIRECTION_ROW_REVERSE = 3,
  BQ1 = Y2.GUTTER_COLUMN = 0,
  QQ1 = Y2.GUTTER_ROW = 1,
  IQ1 = Y2.GUTTER_ALL = 2,
  GQ1 = Y2.JUSTIFY_FLEX_START = 0,
  DQ1 = Y2.JUSTIFY_CENTER = 1,
  ZQ1 = Y2.JUSTIFY_FLEX_END = 2,
  YQ1 = Y2.JUSTIFY_SPACE_BETWEEN = 3,
  WQ1 = Y2.JUSTIFY_SPACE_AROUND = 4,
  FQ1 = Y2.JUSTIFY_SPACE_EVENLY = 5,
  u60 = Y2.LOG_LEVEL_ERROR = 0,
  p60 = Y2.LOG_LEVEL_WARN = 1,
  c60 = Y2.LOG_LEVEL_INFO = 2,
  l60 = Y2.LOG_LEVEL_DEBUG = 3,
  i60 = Y2.LOG_LEVEL_VERBOSE = 4,
  n60 = Y2.LOG_LEVEL_FATAL = 5,
  a60 = Y2.MEASURE_MODE_UNDEFINED = 0,
  s60 = Y2.MEASURE_MODE_EXACTLY = 1,
  r60 = Y2.MEASURE_MODE_AT_MOST = 2,
  o60 = Y2.NODE_TYPE_DEFAULT = 0,
  t60 = Y2.NODE_TYPE_TEXT = 1,
  e60 = Y2.OVERFLOW_VISIBLE = 0,
  A50 = Y2.OVERFLOW_HIDDEN = 1,
  B50 = Y2.OVERFLOW_SCROLL = 2,
  Q50 = Y2.POSITION_TYPE_STATIC = 0,
  JQ1 = Y2.POSITION_TYPE_RELATIVE = 1,
  CQ1 = Y2.POSITION_TYPE_ABSOLUTE = 2,
  I50 = Y2.PRINT_OPTIONS_LAYOUT = 1,
  G50 = Y2.PRINT_OPTIONS_STYLE = 2,
  D50 = Y2.PRINT_OPTIONS_CHILDREN = 4,
  Z50 = Y2.UNIT_UNDEFINED = 0,
  Y50 = Y2.UNIT_POINT = 1,
  W50 = Y2.UNIT_PERCENT = 2,
  F50 = Y2.UNIT_AUTO = 3,
  XQ1 = Y2.WRAP_NO_WRAP = 0,
  VQ1 = Y2.WRAP_WRAP = 1,
  KQ1 = Y2.WRAP_WRAP_REVERSE = 2;
var J50 = (A) => {
  function B(G, D, Z) {
    let Y = G[D];
    G[D] = function(...W) {
      return Z.call(this, Y, ...W)
    }
  }
  for (let G of ["setPosition", "setMargin", "setFlexBasis", "setWidth", "setHeight", "setMinWidth", "setMinHeight",
      "setMaxWidth", "setMaxHeight", "setPadding"
    ]) {
    let D = {
      [Y2.UNIT_POINT]: A.Node.prototype[G],
      [Y2.UNIT_PERCENT]: A.Node.prototype[`${G}Percent`],
      [Y2.UNIT_AUTO]: A.Node.prototype[`${G}Auto`]
    };
    B(A.Node.prototype, G, function(Z, ...Y) {
      let W, F, J = Y.pop();
      if (J === "auto") W = Y2.UNIT_AUTO, F = void 0;
      else if (typeof J == "object") W = J.unit, F = J.valueOf();
      else if (W = typeof J == "string" && J.endsWith("%") ? Y2.UNIT_PERCENT : Y2.UNIT_POINT, F = parseFloat(J), !
        Number.isNaN(J) && Number.isNaN(F)) throw Error(`Invalid value ${J} for ${G}`);
      if (!D[W]) throw Error(`Failed to execute "${G}": Unsupported unit '${J}'`);
      return F !== void 0 ? D[W].call(this, ...Y, F) : D[W].call(this, ...Y)
    })
  }

  function Q(G) {
    return A.MeasureCallback.implement({
      measure: (...D) => {
        let {
          width: Z,
          height: Y
        } = G(...D);
        return {
          width: Z ?? NaN,
          height: Y ?? NaN
        }
      }
    })
  }

  function I(G) {
    return A.DirtiedCallback.implement({
      dirtied: G
    })
  }
  return B(A.Node.prototype, "setMeasureFunc", function(G, D) {
    return D ? G.call(this, Q(D)) : this.unsetMeasureFunc()
  }), B(A.Node.prototype, "setDirtiedFunc", function(G, D) {
    G.call(this, I(D))
  }), B(A.Config.prototype, "free", function() {
    A.Config.destroy(this)
  }), B(A.Node, "create", (G, D) => D ? A.Node.createWithConfig(D) : A.Node.createDefault()), B(A.Node.prototype,
    "free",
    function() {
      A.Node.destroy(this)
    }), B(A.Node.prototype, "freeRecursive", function() {
    for (let G = 0, D = this.getChildCount(); G < D; ++G) this.getChild(0).freeRecursive();
    this.free()
  }), B(A.Node.prototype, "calculateLayout", function(G, D = NaN, Z = NaN, Y = Y2.DIRECTION_LTR) {
    return G.call(this, D, Z, Y)
  }), {
    Config: A.Config,
    Node: A.Node,
    ...Y2
  }
};
var CN4 = (() => {
  var A = typeof document != "undefined" && document.currentScript ? document.currentScript.src : void 0;
  return function(B = {}) {
    W || (W = B !== void 0 ? B : {}), W.ready = new Promise(function(O1, R1) {
      F = O1, J = R1
    });
    var Q, I, G = Object.assign({}, W),
      D = "";
    typeof document != "undefined" && document.currentScript && (D = document.currentScript.src), A && (D = A),
      D = D.indexOf("blob:") !== 0 ? D.substr(0, D.replace(/[?#].*/, "").lastIndexOf("/") + 1) : "";
    var Z = console.log.bind(console),
      Y = console.warn.bind(console);
    Object.assign(W, G), G = null, typeof WebAssembly != "object" && H1("no native wasm support detected");
    var W, F, J, C, X = !1;

    function V(O1, R1, p1) {
      p1 = R1 + p1;
      for (var JA = ""; !(R1 >= p1);) {
        var ZA = O1[R1++];
        if (!ZA) break;
        if (128 & ZA) {
          var $A = 63 & O1[R1++];
          if ((224 & ZA) == 192) JA += String.fromCharCode((31 & ZA) << 6 | $A);
          else {
            var rA = 63 & O1[R1++];
            65536 > (ZA = (240 & ZA) == 224 ? (15 & ZA) << 12 | $A << 6 | rA : (7 & ZA) << 18 | $A << 12 | rA <<
              6 | 63 & O1[R1++]) ? JA += String.fromCharCode(ZA) : (ZA -= 65536, JA += String.fromCharCode(
              55296 | ZA >> 10, 56320 | 1023 & ZA))
          }
        } else JA += String.fromCharCode(ZA)
      }
      return JA
    }

    function K() {
      var O1 = C.buffer;
      W.HEAP8 = U = new Int8Array(O1), W.HEAP16 = q = new Int16Array(O1), W.HEAP32 = R = new Int32Array(O1), W
        .HEAPU8 = N = new Uint8Array(O1), W.HEAPU16 = M = new Uint16Array(O1), W.HEAPU32 = T = new Uint32Array(
        O1), W.HEAPF32 = O = new Float32Array(O1), W.HEAPF64 = S = new Float64Array(O1)
    }
    var U, N, q, M, R, T, O, S, f, a = [],
      g = [],
      Y1 = [],
      r = 0,
      w1 = null;

    function H1(O1) {
      throw Y(O1 = "Aborted(" + O1 + ")"), X = !0, J(O1 = new WebAssembly.RuntimeError(O1 +
        ". Build with -sASSERTIONS for more info.")), O1
    }

    function x() {
      return Q.startsWith("data:application/octet-stream;base64,")
    }

    function F1() {
      try {
        throw "both async and sync fetching of the wasm failed"
      } catch (O1) {
        H1(O1)
      }
    }

    function x1(O1) {
      for (; 0 < O1.length;) O1.shift()(W)
    }

    function o1(O1) {
      if (O1 === void 0) return "_unknown";
      var R1 = (O1 = O1.replace(/[^a-zA-Z0-9_]/g, "$")).charCodeAt(0);
      return 48 <= R1 && 57 >= R1 ? "_" + O1 : O1
    }

    function a1(O1, R1) {
      return O1 = o1(O1),
        function() {
          return R1.apply(this, arguments)
        }
    }
    Q = "yoga.wasm", x() || (Q = D + Q);
    var PA = [{}, {
        value: void 0
      }, {
        value: null
      }, {
        value: !0
      }, {
        value: !1
      }],
      cA = [];

    function FA(O1) {
      var R1 = Error,
        p1 = a1(O1, function(JA) {
          this.name = O1, this.message = JA, (JA = Error(JA).stack) !== void 0 && (this.stack = this
          .toString() + `
` + JA.replace(/^Error(:[^\n]*)?\n/, ""))
        });
      return p1.prototype = Object.create(R1.prototype), p1.prototype.constructor = p1, p1.prototype.toString =
        function() {
          return this.message === void 0 ? this.name : this.name + ": " + this.message
        }, p1
    }
    var f1 = void 0;

    function B1(O1) {
      throw new f1(O1)
    }
    var v1 = (O1) => (O1 || B1("Cannot use deleted val. handle = " + O1), PA[O1].value),
      M1 = (O1) => {
        switch (O1) {
          case void 0:
            return 1;
          case null:
            return 2;
          case !0:
            return 3;
          case !1:
            return 4;
          default:
            var R1 = cA.length ? cA.pop() : PA.length;
            return PA[R1] = {
              fa: 1,
              value: O1
            }, R1
        }
      },
      AA = void 0,
      NA = void 0;

    function OA(O1) {
      for (var R1 = ""; N[O1];) R1 += NA[N[O1++]];
      return R1
    }
    var o = [];

    function A1() {
      for (; o.length;) {
        var O1 = o.pop();
        O1.L.Z = !1, O1.delete()
      }
    }
    var I1 = void 0,
      E1 = {};

    function N1(O1, R1) {
      for (R1 === void 0 && B1("ptr should not be undefined"); O1.P;) R1 = O1.aa(R1), O1 = O1.P;
      return R1
    }
    var t = {};

    function S1(O1) {
      var R1 = OA(O1 = TD(O1));
      return GB(O1), R1
    }

    function k1(O1, R1) {
      var p1 = t[O1];
      return p1 === void 0 && B1(R1 + " has unknown type " + S1(O1)), p1
    }

    function d1() {}
    var e1 = !1;

    function IA(O1) {
      --O1.count.value, O1.count.value === 0 && (O1.S ? O1.T.V(O1.S) : O1.O.M.V(O1.N))
    }
    var zA = {},
      X0 = void 0;

    function kA(O1) {
      throw new X0(O1)
    }

    function z0(O1, R1) {
      return R1.O && R1.N || kA("makeClassHandle requires ptr and ptrType"), !!R1.T != !!R1.S && kA(
        "Both smartPtrType and smartPtr must be specified"), R1.count = {
        value: 1
      }, s2(Object.create(O1, {
        L: {
          value: R1
        }
      }))
    }

    function s2(O1) {
      return typeof FinalizationRegistry == "undefined" ? (s2 = (R1) => R1, O1) : (e1 = new FinalizationRegistry((
        R1) => {
        IA(R1.L)
      }), s2 = (R1) => {
        var p1 = R1.L;
        return p1.S && e1.register(R1, {
          L: p1
        }, R1), R1
      }, d1 = (R1) => {
        e1.unregister(R1)
      }, s2(O1))
    }
    var B2 = {};

    function E2(O1) {
      for (; O1.length;) {
        var R1 = O1.pop();
        O1.pop()(R1)
      }
    }

    function g2(O1) {
      return this.fromWireType(R[O1 >> 2])
    }
    var Q9 = {},
      o4 = {};

    function Z0(O1, R1, p1) {
      function JA(bA) {
        (bA = p1(bA)).length !== O1.length && kA("Mismatched type converter count");
        for (var sA = 0; sA < O1.length; ++sA) m0(O1[sA], bA[sA])
      }
      O1.forEach(function(bA) {
        o4[bA] = R1
      });
      var ZA = Array(R1.length),
        $A = [],
        rA = 0;
      R1.forEach((bA, sA) => {
        t.hasOwnProperty(bA) ? ZA[sA] = t[bA] : ($A.push(bA), Q9.hasOwnProperty(bA) || (Q9[bA] = []), Q9[bA]
          .push(() => {
            ZA[sA] = t[bA], ++rA === $A.length && JA(ZA)
          }))
      }), $A.length === 0 && JA(ZA)
    }

    function h0(O1) {
      switch (O1) {
        case 1:
          return 0;
        case 2:
          return 1;
        case 4:
          return 2;
        case 8:
          return 3;
        default:
          throw TypeError("Unknown type size: " + O1)
      }
    }

    function m0(O1, R1, p1 = {}) {
      if (!("argPackAdvance" in R1)) throw TypeError("registerType registeredInstance requires argPackAdvance");
      var JA = R1.name;
      if (O1 || B1('type "' + JA + '" must have a positive integer typeid pointer'), t.hasOwnProperty(O1)) {
        if (p1.ta) return;
        B1("Cannot register type '" + JA + "' twice")
      }
      t[O1] = R1, delete o4[O1], Q9.hasOwnProperty(O1) && (R1 = Q9[O1], delete Q9[O1], R1.forEach((ZA) => ZA()))
    }

    function L0(O1) {
      B1(O1.L.O.M.name + " instance already deleted")
    }

    function H0() {}

    function j2(O1, R1, p1) {
      if (O1[R1].R === void 0) {
        var JA = O1[R1];
        O1[R1] = function() {
          return O1[R1].R.hasOwnProperty(arguments.length) || B1("Function '" + p1 +
            "' called with an invalid number of arguments (" + arguments.length + ") - expects one of (" + O1[
              R1].R + ")!"), O1[R1].R[arguments.length].apply(this, arguments)
        }, O1[R1].R = [], O1[R1].R[JA.Y] = JA
      }
    }

    function y9(O1, R1, p1, JA, ZA, $A, rA, bA) {
      this.name = O1, this.constructor = R1, this.W = p1, this.V = JA, this.P = ZA, this.oa = $A, this.aa = rA,
        this.ma = bA, this.ia = []
    }

    function z8(O1, R1, p1) {
      for (; R1 !== p1;) R1.aa || B1("Expected null or instance of " + p1.name + ", got an instance of " + R1
        .name), O1 = R1.aa(O1), R1 = R1.P;
      return O1
    }

    function zB(O1, R1) {
      return R1 === null ? (this.da && B1("null is not a valid " + this.name), 0) : (R1.L || B1('Cannot pass "' +
        H3(R1) + '" as a ' + this.name), R1.L.N || B1("Cannot pass deleted object as a pointer of type " +
        this.name), z8(R1.L.N, R1.L.O.M, this.M))
    }

    function H6(O1, R1) {
      if (R1 === null) {
        if (this.da && B1("null is not a valid " + this.name), this.ca) {
          var p1 = this.ea();
          return O1 !== null && O1.push(this.V, p1), p1
        }
        return 0
      }
      if (R1.L || B1('Cannot pass "' + H3(R1) + '" as a ' + this.name), R1.L.N || B1(
          "Cannot pass deleted object as a pointer of type " + this.name), !this.ba && R1.L.O.ba && B1(
          "Cannot convert argument of type " + (R1.L.T ? R1.L.T.name : R1.L.O.name) + " to parameter type " + this
          .name), p1 = z8(R1.L.N, R1.L.O.M, this.M), this.ca) switch (R1.L.S === void 0 && B1(
          "Passing raw pointer to smart pointer is illegal"), this.Aa) {
        case 0:
          R1.L.T === this ? p1 = R1.L.S : B1("Cannot convert argument of type " + (R1.L.T ? R1.L.T.name : R1.L.O
            .name) + " to parameter type " + this.name);
          break;
        case 1:
          p1 = R1.L.S;
          break;
        case 2:
          if (R1.L.T === this) p1 = R1.L.S;
          else {
            var JA = R1.clone();
            p1 = this.wa(p1, M1(function() {
              JA.delete()
            })), O1 !== null && O1.push(this.V, p1)
          }
          break;
        default:
          B1("Unsupporting sharing policy")
      }
      return p1
    }

    function T2(O1, R1) {
      return R1 === null ? (this.da && B1("null is not a valid " + this.name), 0) : (R1.L || B1('Cannot pass "' +
        H3(R1) + '" as a ' + this.name), R1.L.N || B1("Cannot pass deleted object as a pointer of type " +
        this.name), R1.L.O.ba && B1("Cannot convert argument of type " + R1.L.O.name + " to parameter type " +
        this.name), z8(R1.L.N, R1.L.O.M, this.M))
    }

    function x4(O1, R1, p1, JA) {
      this.name = O1, this.M = R1, this.da = p1, this.ba = JA, this.ca = !1, this.V = this.wa = this.ea = this
        .ja = this.Aa = this.va = void 0, R1.P !== void 0 ? this.toWireType = H6 : (this.toWireType = JA ? zB :
          T2, this.U = null)
    }
    var f0 = [];

    function U2(O1) {
      var R1 = f0[O1];
      return R1 || (O1 >= f0.length && (f0.length = O1 + 1), f0[O1] = R1 = f.get(O1)), R1
    }

    function r2(O1, R1) {
      var p1, JA, ZA = (O1 = OA(O1)).includes("j") ? (p1 = O1, JA = [], function() {
        if (JA.length = 0, Object.assign(JA, arguments), p1.includes("j")) {
          var $A = W["dynCall_" + p1];
          $A = JA && JA.length ? $A.apply(null, [R1].concat(JA)) : $A.call(null, R1)
        } else $A = U2(R1).apply(null, JA);
        return $A
      }) : U2(R1);
      return typeof ZA != "function" && B1("unknown function pointer with signature " + O1 + ": " + R1), ZA
    }
    var T6 = void 0;

    function w8(O1, R1) {
      var p1 = [],
        JA = {};
      throw R1.forEach(function ZA($A) {
        JA[$A] || t[$A] || (o4[$A] ? o4[$A].forEach(ZA) : (p1.push($A), JA[$A] = !0))
      }), new T6(O1 + ": " + p1.map(S1).join([", "]))
    }

    function u3(O1, R1, p1, JA, ZA) {
      var $A = R1.length;
      2 > $A && B1("argTypes array size mismatch! Must at least get return value and 'this' types!");
      var rA = R1[1] !== null && p1 !== null,
        bA = !1;
      for (p1 = 1; p1 < R1.length; ++p1)
        if (R1[p1] !== null && R1[p1].U === void 0) {
          bA = !0;
          break
        } var sA = R1[0].name !== "void",
        fA = $A - 2,
        iA = Array(fA),
        P2 = [],
        F2 = [];
      return function() {
        if (arguments.length !== fA && B1("function " + O1 + " called with " + arguments.length +
            " arguments, expected " + fA + " args!"), F2.length = 0, P2.length = rA ? 2 : 1, P2[0] = ZA, rA) {
          var $9 = R1[1].toWireType(F2, this);
          P2[1] = $9
        }
        for (var C1 = 0; C1 < fA; ++C1) iA[C1] = R1[C1 + 2].toWireType(F2, arguments[C1]), P2.push(iA[C1]);
        if (C1 = JA.apply(null, P2), bA) E2(F2);
        else
          for (var c1 = rA ? 1 : 2; c1 < R1.length; c1++) {
            var P1 = c1 === 1 ? $9 : iA[c1 - 2];
            R1[c1].U !== null && R1[c1].U(P1)
          }
        return sA ? R1[0].fromWireType(C1) : void 0
      }
    }

    function iB(O1, R1) {
      for (var p1 = [], JA = 0; JA < O1; JA++) p1.push(T[R1 + 4 * JA >> 2]);
      return p1
    }

    function z6(O1) {
      4 < O1 && --PA[O1].fa == 0 && (PA[O1] = void 0, cA.push(O1))
    }

    function H3(O1) {
      if (O1 === null) return "null";
      var R1 = typeof O1;
      return R1 === "object" || R1 === "array" || R1 === "function" ? O1.toString() : "" + O1
    }

    function E8(O1, R1) {
      for (var p1 = "", JA = 0; !(JA >= R1 / 2); ++JA) {
        var ZA = q[O1 + 2 * JA >> 1];
        if (ZA == 0) break;
        p1 += String.fromCharCode(ZA)
      }
      return p1
    }

    function QB(O1, R1, p1) {
      if (p1 === void 0 && (p1 = 2147483647), 2 > p1) return 0;
      p1 -= 2;
      var JA = R1;
      p1 = p1 < 2 * O1.length ? p1 / 2 : O1.length;
      for (var ZA = 0; ZA < p1; ++ZA) q[R1 >> 1] = O1.charCodeAt(ZA), R1 += 2;
      return q[R1 >> 1] = 0, R1 - JA
    }

    function OQ(O1) {
      return 2 * O1.length
    }

    function V2(O1, R1) {
      for (var p1 = 0, JA = ""; !(p1 >= R1 / 4);) {
        var ZA = R[O1 + 4 * p1 >> 2];
        if (ZA == 0) break;
        ++p1, 65536 <= ZA ? (ZA -= 65536, JA += String.fromCharCode(55296 | ZA >> 10, 56320 | 1023 & ZA)) : JA +=
          String.fromCharCode(ZA)
      }
      return JA
    }

    function N9(O1, R1, p1) {
      if (p1 === void 0 && (p1 = 2147483647), 4 > p1) return 0;
      var JA = R1;
      p1 = JA + p1 - 4;
      for (var ZA = 0; ZA < O1.length; ++ZA) {
        var $A = O1.charCodeAt(ZA);
        if (55296 <= $A && 57343 >= $A && ($A = 65536 + ((1023 & $A) << 10) | 1023 & O1.charCodeAt(++ZA)), R[R1 >>
            2] = $A, (R1 += 4) + 4 > p1) break
      }
      return R[R1 >> 2] = 0, R1 - JA
    }

    function z3(O1) {
      for (var R1 = 0, p1 = 0; p1 < O1.length; ++p1) {
        var JA = O1.charCodeAt(p1);
        55296 <= JA && 57343 >= JA && ++p1, R1 += 4
      }
      return R1
    }
    var G7 = {};

    function IB(O1) {
      var R1 = G7[O1];
      return R1 === void 0 ? OA(O1) : R1
    }
    var nB = [],
      $G = [],
      OZ = [null, [],
        []
      ];
    f1 = W.BindingError = FA("BindingError"), W.count_emval_handles = function() {
      for (var O1 = 0, R1 = 5; R1 < PA.length; ++R1) PA[R1] !== void 0 && ++O1;
      return O1
    }, W.get_first_emval = function() {
      for (var O1 = 5; O1 < PA.length; ++O1)
        if (PA[O1] !== void 0) return PA[O1];
      return null
    }, AA = W.PureVirtualError = FA("PureVirtualError");
    for (var D7 = Array(256), w3 = 0; 256 > w3; ++w3) D7[w3] = String.fromCharCode(w3);
    NA = D7, W.getInheritedInstanceCount = function() {
        return Object.keys(E1).length
      }, W.getLiveInheritedInstances = function() {
        var O1, R1 = [];
        for (O1 in E1) E1.hasOwnProperty(O1) && R1.push(E1[O1]);
        return R1
      }, W.flushPendingDeletes = A1, W.setDelayFunction = function(O1) {
        I1 = O1, o.length && I1 && I1(A1)
      }, X0 = W.InternalError = FA("InternalError"), H0.prototype.isAliasOf = function(O1) {
        if (!(this instanceof H0 && O1 instanceof H0)) return !1;
        var R1 = this.L.O.M,
          p1 = this.L.N,
          JA = O1.L.O.M;
        for (O1 = O1.L.N; R1.P;) p1 = R1.aa(p1), R1 = R1.P;
        for (; JA.P;) O1 = JA.aa(O1), JA = JA.P;
        return R1 === JA && p1 === O1
      }, H0.prototype.clone = function() {
        if (this.L.N || L0(this), this.L.$) return this.L.count.value += 1, this;
        var O1 = s2,
          R1 = Object,
          p1 = R1.create,
          JA = Object.getPrototypeOf(this),
          ZA = this.L;
        return O1 = O1(p1.call(R1, JA, {
          L: {
            value: {
              count: ZA.count,
              Z: ZA.Z,
              $: ZA.$,
              N: ZA.N,
              O: ZA.O,
              S: ZA.S,
              T: ZA.T
            }
          }
        })), O1.L.count.value += 1, O1.L.Z = !1, O1
      }, H0.prototype.delete = function() {
        this.L.N || L0(this), this.L.Z && !this.L.$ && B1("Object already scheduled for deletion"), d1(this), IA(
          this.L), this.L.$ || (this.L.S = void 0, this.L.N = void 0)
      }, H0.prototype.isDeleted = function() {
        return !this.L.N
      }, H0.prototype.deleteLater = function() {
        return this.L.N || L0(this), this.L.Z && !this.L.$ && B1("Object already scheduled for deletion"), o.push(
          this), o.length === 1 && I1 && I1(A1), this.L.Z = !0, this
      }, x4.prototype.pa = function(O1) {
        return this.ja && (O1 = this.ja(O1)), O1
      }, x4.prototype.ga = function(O1) {
        this.V && this.V(O1)
      }, x4.prototype.argPackAdvance = 8, x4.prototype.readValueFromPointer = g2, x4.prototype.deleteObject =
      function(O1) {
        O1 !== null && O1.delete()
      }, x4.prototype.fromWireType = function(O1) {
        function R1() {
          return this.ca ? z0(this.M.W, {
            O: this.va,
            N: JA,
            T: this,
            S: O1
          }) : z0(this.M.W, {
            O: this,
            N: O1
          })
        }
        var p1, JA = this.pa(O1);
        if (!JA) return this.ga(O1), null;
        var ZA = E1[N1(this.M, JA)];
        if (ZA !== void 0) return ZA.L.count.value === 0 ? (ZA.L.N = JA, ZA.L.S = O1, ZA.clone()) : (ZA = ZA
          .clone(), this.ga(O1), ZA);
        if (!(ZA = zA[ZA = this.M.oa(JA)])) return R1.call(this);
        ZA = this.ba ? ZA.ka : ZA.pointerType;
        var $A = function rA(bA, sA, fA) {
          return sA === fA ? bA : fA.P === void 0 ? null : (bA = rA(bA, sA, fA.P)) === null ? null : fA.ma(bA)
        }(JA, this.M, ZA.M);
        return $A === null ? R1.call(this) : this.ca ? z0(ZA.M.W, {
          O: ZA,
          N: $A,
          T: this,
          S: O1
        }) : z0(ZA.M.W, {
          O: ZA,
          N: $A
        })
      }, T6 = W.UnboundTypeError = FA("UnboundTypeError");
    var OD = {
      q: function(O1, R1, p1) {
        O1 = OA(O1), R1 = k1(R1, "wrapper"), p1 = v1(p1);
        var JA = [].slice,
          ZA = R1.M,
          $A = ZA.W,
          rA = ZA.P.W,
          bA = ZA.P.constructor;
        for (var sA in O1 = a1(O1, function() {
            ZA.P.ia.forEach(function(fA) {
              if (this[fA] === rA[fA]) throw new AA("Pure virtual function " + fA +
                " must be implemented in JavaScript")
            }.bind(this)), Object.defineProperty(this, "__parent", {
              value: $A
            }), this.__construct.apply(this, JA.call(arguments))
          }), $A.__construct = function() {
            this === $A && B1("Pass correct 'this' to __construct");
            var fA = bA.implement.apply(void 0, [this].concat(JA.call(arguments)));
            d1(fA);
            var iA = fA.L;
            fA.notifyOnDestruction(), iA.$ = !0, Object.defineProperties(this, {
              L: {
                value: iA
              }
            }), s2(this), fA = N1(ZA, fA = iA.N), E1.hasOwnProperty(fA) ? B1(
              "Tried to register registered instance: " + fA) : E1[fA] = this
          }, $A.__destruct = function() {
            this === $A && B1("Pass correct 'this' to __destruct"), d1(this);
            var fA = this.L.N;
            fA = N1(ZA, fA), E1.hasOwnProperty(fA) ? delete E1[fA] : B1(
              "Tried to unregister unregistered instance: " + fA)
          }, O1.prototype = Object.create($A), p1) O1.prototype[sA] = p1[sA];
        return M1(O1)
      },
      l: function(O1) {
        var R1 = B2[O1];
        delete B2[O1];
        var {
          ea: p1,
          V: JA,
          ha: ZA
        } = R1;
        Z0([O1], ZA.map(($A) => $A.sa).concat(ZA.map(($A) => $A.ya)), ($A) => {
          var rA = {};
          return ZA.forEach((bA, sA) => {
            var fA = $A[sA],
              iA = bA.qa,
              P2 = bA.ra,
              F2 = $A[sA + ZA.length],
              $9 = bA.xa,
              C1 = bA.za;
            rA[bA.na] = {
              read: (c1) => fA.fromWireType(iA(P2, c1)),
              write: (c1, P1) => {
                var QA = [];
                $9(C1, c1, F2.toWireType(QA, P1)), E2(QA)
              }
            }
          }), [{
            name: R1.name,
            fromWireType: function(bA) {
              var sA, fA = {};
              for (sA in rA) fA[sA] = rA[sA].read(bA);
              return JA(bA), fA
            },
            toWireType: function(bA, sA) {
              for (var fA in rA)
                if (!(fA in sA)) throw TypeError('Missing field:  "' + fA + '"');
              var iA = p1();
              for (fA in rA) rA[fA].write(iA, sA[fA]);
              return bA !== null && bA.push(JA, iA), iA
            },
            argPackAdvance: 8,
            readValueFromPointer: g2,
            U: JA
          }]
        })
      },
      v: function() {},
      B: function(O1, R1, p1, JA, ZA) {
        var $A = h0(p1);
        m0(O1, {
          name: R1 = OA(R1),
          fromWireType: function(rA) {
            return !!rA
          },
          toWireType: function(rA, bA) {
            return bA ? JA : ZA
          },
          argPackAdvance: 8,
          readValueFromPointer: function(rA) {
            if (p1 === 1) var bA = U;
            else if (p1 === 2) bA = q;
            else if (p1 === 4) bA = R;
            else throw TypeError("Unknown boolean type size: " + R1);
            return this.fromWireType(bA[rA >> $A])
          },
          U: null
        })
      },
      h: function(O1, R1, p1, JA, ZA, $A, rA, bA, sA, fA, iA, P2, F2) {
        iA = OA(iA), $A = r2(ZA, $A), bA && (bA = r2(rA, bA)), fA && (fA = r2(sA, fA)), F2 = r2(P2, F2);
        var $9, C1 = o1(iA);
        $9 = function() {
          w8("Cannot construct " + iA + " due to unbound types", [JA])
        }, W.hasOwnProperty(C1) ? (B1("Cannot register public name '" + C1 + "' twice"), j2(W, C1, C1), W
          .hasOwnProperty(void 0) && B1(
            "Cannot register multiple overloads of a function with the same number of arguments (undefined)!"
            ), W[C1].R[void 0] = $9) : W[C1] = $9, Z0([O1, R1, p1], JA ? [JA] : [], function(c1) {
          if (c1 = c1[0], JA) var P1, QA = c1.M,
            XA = QA.W;
          else XA = H0.prototype;
          c1 = a1(C1, function() {
            if (Object.getPrototypeOf(this) !== DA) throw new f1("Use 'new' to construct " + iA);
            if (gA.X === void 0) throw new f1(iA + " has no accessible constructor");
            var oA = gA.X[arguments.length];
            if (oA === void 0) throw new f1("Tried to invoke ctor of " + iA +
              " with invalid number of parameters (" + arguments.length + ") - expected (" +
              Object.keys(gA.X).toString() + ") parameters instead!");
            return oA.apply(this, arguments)
          });
          var DA = Object.create(XA, {
            constructor: {
              value: c1
            }
          });
          c1.prototype = DA;
          var gA = new y9(iA, c1, DA, F2, QA, $A, bA, fA);
          QA = new x4(iA, gA, !0, !1), XA = new x4(iA + "*", gA, !1, !1);
          var eA = new x4(iA + " const*", gA, !1, !0);
          return zA[O1] = {
            pointerType: XA,
            ka: eA
          }, P1 = c1, W.hasOwnProperty(C1) || kA("Replacing nonexistant public symbol"), W[C1] = P1, W[
            C1].Y = void 0, [QA, XA, eA]
        })
      },
      d: function(O1, R1, p1, JA, ZA, $A, rA) {
        var bA = iB(p1, JA);
        R1 = OA(R1), $A = r2(ZA, $A), Z0([], [O1], function(sA) {
          function fA() {
            w8("Cannot call " + iA + " due to unbound types", bA)
          }
          var iA = (sA = sA[0]).name + "." + R1;
          R1.startsWith("@@") && (R1 = Symbol[R1.substring(2)]);
          var P2 = sA.M.constructor;
          return P2[R1] === void 0 ? (fA.Y = p1 - 1, P2[R1] = fA) : (j2(P2, R1, iA), P2[R1].R[p1 - 1] =
            fA), Z0([], bA, function(F2) {
            return F2 = u3(iA, [F2[0], null].concat(F2.slice(1)), null, $A, rA), P2[R1].R === void 0 ?
              (F2.Y = p1 - 1, P2[R1] = F2) : P2[R1].R[p1 - 1] = F2, []
          }), []
        })
      },
      p: function(O1, R1, p1, JA, ZA, $A) {
        0 < R1 || H1();
        var rA = iB(R1, p1);
        ZA = r2(JA, ZA), Z0([], [O1], function(bA) {
          var sA = "constructor " + (bA = bA[0]).name;
          if (bA.M.X === void 0 && (bA.M.X = []), bA.M.X[R1 - 1] !== void 0) throw new f1(
            "Cannot register multiple constructors with identical number of parameters (" + (R1 - 1) +
            ") for class '" + bA.name +
            "'! Overload resolution is currently only performed using the parameter count, not actual type info!"
            );
          return bA.M.X[R1 - 1] = () => {
            w8("Cannot construct " + bA.name + " due to unbound types", rA)
          }, Z0([], rA, function(fA) {
            return fA.splice(1, 0, null), bA.M.X[R1 - 1] = u3(sA, fA, null, ZA, $A), []
          }), []
        })
      },
      a: function(O1, R1, p1, JA, ZA, $A, rA, bA) {
        var sA = iB(p1, JA);
        R1 = OA(R1), $A = r2(ZA, $A), Z0([], [O1], function(fA) {
          function iA() {
            w8("Cannot call " + P2 + " due to unbound types", sA)
          }
          var P2 = (fA = fA[0]).name + "." + R1;
          R1.startsWith("@@") && (R1 = Symbol[R1.substring(2)]), bA && fA.M.ia.push(R1);
          var F2 = fA.M.W,
            $9 = F2[R1];
          return $9 === void 0 || $9.R === void 0 && $9.className !== fA.name && $9.Y === p1 - 2 ? (iA.Y =
            p1 - 2, iA.className = fA.name, F2[R1] = iA) : (j2(F2, R1, P2), F2[R1].R[p1 - 2] = iA), Z0(
          [], sA, function(C1) {
            return C1 = u3(P2, C1, fA, $A, rA), F2[R1].R === void 0 ? (C1.Y = p1 - 2, F2[R1] = C1) :
              F2[R1].R[p1 - 2] = C1, []
          }), []
        })
      },
      A: function(O1, R1) {
        m0(O1, {
          name: R1 = OA(R1),
          fromWireType: function(p1) {
            var JA = v1(p1);
            return z6(p1), JA
          },
          toWireType: function(p1, JA) {
            return M1(JA)
          },
          argPackAdvance: 8,
          readValueFromPointer: g2,
          U: null
        })
      },
      n: function(O1, R1, p1) {
        p1 = h0(p1), m0(O1, {
          name: R1 = OA(R1),
          fromWireType: function(JA) {
            return JA
          },
          toWireType: function(JA, ZA) {
            return ZA
          },
          argPackAdvance: 8,
          readValueFromPointer: function(JA, ZA) {
            switch (ZA) {
              case 2:
                return function($A) {
                  return this.fromWireType(O[$A >> 2])
                };
              case 3:
                return function($A) {
                  return this.fromWireType(S[$A >> 3])
                };
              default:
                throw TypeError("Unknown float type: " + JA)
            }
          }(R1, p1),
          U: null
        })
      },
      e: function(O1, R1, p1, JA, ZA) {
        R1 = OA(R1), ZA === -1 && (ZA = 4294967295), ZA = h0(p1);
        var $A = (bA) => bA;
        if (JA === 0) {
          var rA = 32 - 8 * p1;
          $A = (bA) => bA << rA >>> rA
        }
        p1 = R1.includes("unsigned") ? function(bA, sA) {
          return sA >>> 0
        } : function(bA, sA) {
          return sA
        }, m0(O1, {
          name: R1,
          fromWireType: $A,
          toWireType: p1,
          argPackAdvance: 8,
          readValueFromPointer: function(bA, sA, fA) {
            switch (sA) {
              case 0:
                return fA ? function(iA) {
                  return U[iA]
                } : function(iA) {
                  return N[iA]
                };
              case 1:
                return fA ? function(iA) {
                  return q[iA >> 1]
                } : function(iA) {
                  return M[iA >> 1]
                };
              case 2:
                return fA ? function(iA) {
                  return R[iA >> 2]
                } : function(iA) {
                  return T[iA >> 2]
                };
              default:
                throw TypeError("Unknown integer type: " + bA)
            }
          }(R1, ZA, JA !== 0),
          U: null
        })
      },
      b: function(O1, R1, p1) {
        function JA($A) {
          $A >>= 2;
          var rA = T;
          return new ZA(rA.buffer, rA[$A + 1], rA[$A])
        }
        var ZA = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array,
          Float64Array
        ][R1];
        m0(O1, {
          name: p1 = OA(p1),
          fromWireType: JA,
          argPackAdvance: 8,
          readValueFromPointer: JA
        }, {
          ta: !0
        })
      },
      o: function(O1, R1) {
        var p1 = (R1 = OA(R1)) === "std::string";
        m0(O1, {
          name: R1,
          fromWireType: function(JA) {
            var ZA = T[JA >> 2],
              $A = JA + 4;
            if (p1)
              for (var rA = $A, bA = 0; bA <= ZA; ++bA) {
                var sA = $A + bA;
                if (bA == ZA || N[sA] == 0) {
                  if (rA = rA ? V(N, rA, sA - rA) : "", fA === void 0) var fA = rA;
                  else fA += "\x00" + rA;
                  rA = sA + 1
                }
              } else {
                for (bA = 0, fA = Array(ZA); bA < ZA; ++bA) fA[bA] = String.fromCharCode(N[$A + bA]);
                fA = fA.join("")
              }
            return GB(JA), fA
          },
          toWireType: function(JA, ZA) {
            ZA instanceof ArrayBuffer && (ZA = new Uint8Array(ZA));
            var $A, rA = typeof ZA == "string";
            if (rA || ZA instanceof Uint8Array || ZA instanceof Uint8ClampedArray ||
              ZA instanceof Int8Array || B1("Cannot pass non-string to std::string"), p1 && rA) {
              var bA = 0;
              for ($A = 0; $A < ZA.length; ++$A) {
                var sA = ZA.charCodeAt($A);
                127 >= sA ? bA++ : 2047 >= sA ? bA += 2 : 55296 <= sA && 57343 >= sA ? (bA += 4, ++$A) :
                  bA += 3
              }
              $A = bA
            } else $A = ZA.length;
            if (sA = (bA = PD(4 + $A + 1)) + 4, T[bA >> 2] = $A, p1 && rA) {
              if (rA = sA, sA = $A + 1, $A = N, 0 < sA) {
                sA = rA + sA - 1;
                for (var fA = 0; fA < ZA.length; ++fA) {
                  var iA = ZA.charCodeAt(fA);
                  if (55296 <= iA && 57343 >= iA && (iA = 65536 + ((1023 & iA) << 10) | 1023 & ZA
                      .charCodeAt(++fA)), 127 >= iA) {
                    if (rA >= sA) break;
                    $A[rA++] = iA
                  } else {
                    if (2047 >= iA) {
                      if (rA + 1 >= sA) break;
                      $A[rA++] = 192 | iA >> 6
                    } else {
                      if (65535 >= iA) {
                        if (rA + 2 >= sA) break;
                        $A[rA++] = 224 | iA >> 12
                      } else {
                        if (rA + 3 >= sA) break;
                        $A[rA++] = 240 | iA >> 18, $A[rA++] = 128 | iA >> 12 & 63
                      }
                      $A[rA++] = 128 | iA >> 6 & 63
                    }
                    $A[rA++] = 128 | 63 & iA
                  }
                }
                $A[rA] = 0
              }
            } else if (rA)
              for (rA = 0; rA < $A; ++rA) 255 < (fA = ZA.charCodeAt(rA)) && (GB(sA), B1(
                "String has UTF-16 code units that do not fit in 8 bits")), N[sA + rA] = fA;
            else
              for (rA = 0; rA < $A; ++rA) N[sA + rA] = ZA[rA];
            return JA !== null && JA.push(GB, bA), bA
          },
          argPackAdvance: 8,
          readValueFromPointer: g2,
          U: function(JA) {
            GB(JA)
          }
        })
      },
      k: function(O1, R1, p1) {
        if (p1 = OA(p1), R1 === 2) var JA = E8,
          ZA = QB,
          $A = OQ,
          rA = () => M,
          bA = 1;
        else R1 === 4 && (JA = V2, ZA = N9, $A = z3, rA = () => T, bA = 2);
        m0(O1, {
          name: p1,
          fromWireType: function(sA) {
            for (var fA, iA = T[sA >> 2], P2 = rA(), F2 = sA + 4, $9 = 0; $9 <= iA; ++$9) {
              var C1 = sA + 4 + $9 * R1;
              ($9 == iA || P2[C1 >> bA] == 0) && (F2 = JA(F2, C1 - F2), fA === void 0 ? fA = F2 : fA +=
                "\x00" + F2, F2 = C1 + R1)
            }
            return GB(sA), fA
          },
          toWireType: function(sA, fA) {
            typeof fA != "string" && B1("Cannot pass non-string to C++ string type " + p1);
            var iA = $A(fA),
              P2 = PD(4 + iA + R1);
            return T[P2 >> 2] = iA >> bA, ZA(fA, P2 + 4, iA + R1), sA !== null && sA.push(GB, P2), P2
          },
          argPackAdvance: 8,
          readValueFromPointer: g2,
          U: function(sA) {
            GB(sA)
          }
        })
      },
      m: function(O1, R1, p1, JA, ZA, $A) {
        B2[O1] = {
          name: OA(R1),
          ea: r2(p1, JA),
          V: r2(ZA, $A),
          ha: []
        }
      },
      c: function(O1, R1, p1, JA, ZA, $A, rA, bA, sA, fA) {
        B2[O1].ha.push({
          na: OA(R1),
          sa: p1,
          qa: r2(JA, ZA),
          ra: $A,
          ya: rA,
          xa: r2(bA, sA),
          za: fA
        })
      },
      C: function(O1, R1) {
        m0(O1, {
          ua: !0,
          name: R1 = OA(R1),
          argPackAdvance: 0,
          fromWireType: function() {},
          toWireType: function() {}
        })
      },
      t: function(O1, R1, p1, JA, ZA) {
        O1 = nB[O1], R1 = v1(R1), p1 = IB(p1);
        var $A = [];
        return T[JA >> 2] = M1($A), O1(R1, p1, $A, ZA)
      },
      j: function(O1, R1, p1, JA) {
        O1 = nB[O1], O1(R1 = v1(R1), p1 = IB(p1), null, JA)
      },
      f: z6,
      g: function(O1, R1) {
        var p1, JA, ZA = function(sA, fA) {
            for (var iA = Array(sA), P2 = 0; P2 < sA; ++P2) iA[P2] = k1(T[fA + 4 * P2 >> 2], "parameter " +
              P2);
            return iA
          }(O1, R1),
          $A = ZA[0],
          rA = $G[R1 = $A.name + "_$" + ZA.slice(1).map(function(sA) {
            return sA.name
          }).join("_") + "$"];
        if (rA !== void 0) return rA;
        var bA = Array(O1 - 1);
        return p1 = (sA, fA, iA, P2) => {
          for (var F2 = 0, $9 = 0; $9 < O1 - 1; ++$9) bA[$9] = ZA[$9 + 1].readValueFromPointer(P2 + F2),
            F2 += ZA[$9 + 1].argPackAdvance;
          for ($9 = 0, sA = sA[fA].apply(sA, bA); $9 < O1 - 1; ++$9) ZA[$9 + 1].la && ZA[$9 + 1].la(bA[$9]);
          if (!$A.ua) return $A.toWireType(iA, sA)
        }, JA = nB.length, nB.push(p1), rA = JA, $G[R1] = rA
      },
      r: function(O1) {
        4 < O1 && (PA[O1].fa += 1)
      },
      s: function(O1) {
        E2(v1(O1)), z6(O1)
      },
      i: function() {
        H1("")
      },
      x: function(O1, R1, p1) {
        N.copyWithin(O1, R1, R1 + p1)
      },
      w: function(O1) {
        var R1 = N.length;
        if (2147483648 < (O1 >>>= 0)) return !1;
        for (var p1 = 1; 4 >= p1; p1 *= 2) {
          var JA = R1 * (1 + 0.2 / p1);
          JA = Math.min(JA, O1 + 100663296);
          var ZA = Math,
            $A = ZA.min;
          JA = Math.max(O1, JA), JA += (65536 - JA % 65536) % 65536;
          A: {
            var rA = C.buffer;
            try {
              C.grow($A.call(ZA, 2147483648, JA) - rA.byteLength + 65535 >>> 16), K();
              var bA = 1;
              break A
            } catch (sA) {}
            bA = void 0
          }
          if (bA) return !0
        }
        return !1
      },
      z: function() {
        return 52
      },
      u: function() {
        return 70
      },
      y: function(O1, R1, p1, JA) {
        for (var ZA = 0, $A = 0; $A < p1; $A++) {
          var rA = T[R1 >> 2],
            bA = T[R1 + 4 >> 2];
          R1 += 8;
          for (var sA = 0; sA < bA; sA++) {
            var fA = N[rA + sA],
              iA = OZ[O1];
            fA === 0 || fA === 10 ? ((O1 === 1 ? Z : Y)(V(iA, 0)), iA.length = 0) : iA.push(fA)
          }
          ZA += bA
        }
        return T[JA >> 2] = ZA, 0
      }
    };
    (function() {
      function O1(ZA) {
        W.asm = ZA.exports, C = W.asm.D, K(), f = W.asm.I, g.unshift(W.asm.E), --r == 0 && w1 && (ZA = w1, w1 =
          null, ZA())
      }

      function R1(ZA) {
        O1(ZA.instance)
      }

      function p1(ZA) {
        return (typeof fetch == "function" ? fetch(Q, {
          credentials: "same-origin"
        }).then(function($A) {
          if (!$A.ok) throw "failed to load wasm binary file at '" + Q + "'";
          return $A.arrayBuffer()
        }).catch(function() {
          return F1()
        }) : Promise.resolve().then(function() {
          return F1()
        })).then(function($A) {
          return WebAssembly.instantiate($A, JA)
        }).then(function($A) {
          return $A
        }).then(ZA, function($A) {
          Y("failed to asynchronously prepare wasm: " + $A), H1($A)
        })
      }
      var JA = {
        a: OD
      };
      if (r++, W.instantiateWasm) try {
        return W.instantiateWasm(JA, O1)
      } catch (ZA) {
        Y("Module.instantiateWasm callback failed with error: " + ZA), J(ZA)
      }(typeof WebAssembly.instantiateStreaming != "function" || x() || typeof fetch != "function" ? p1(R1) :
        fetch(Q, {
          credentials: "same-origin"
        }).then(function(ZA) {
          return WebAssembly.instantiateStreaming(ZA, JA).then(R1, function($A) {
            return Y("wasm streaming compile failed: " + $A), Y(
              "falling back to ArrayBuffer instantiation"), p1(R1)
          })
        })).catch(J)
    })();
    var TD = W.___getTypeName = function() {
      return (TD = W.___getTypeName = W.asm.F).apply(null, arguments)
    };

    function PD() {
      return (PD = W.asm.H).apply(null, arguments)
    }

    function GB() {
      return (GB = W.asm.J).apply(null, arguments)
    }

    function TZ() {
      0 < r || (x1(a), 0 < r || I || (I = !0, W.calledRun = !0, X || (x1(g), F(W), x1(Y1))))
    }
    return W.__embind_initialize_bindings = function() {
      return (W.__embind_initialize_bindings = W.asm.G).apply(null, arguments)
    }, W.dynCall_jiji = function() {
      return (W.dynCall_jiji = W.asm.K).apply(null, arguments)
    }, w1 = function O1() {
      I || TZ(), I || (w1 = O1)
    }, TZ(), B.ready
  }
})();
async function C50(A) {
  let B = await CN4({
    instantiateWasm(Q, I) {
      WebAssembly.instantiate(A, Q).then((G) => {
        G instanceof WebAssembly.Instance ? I(G) : I(G.instance)
      })
    }
  });
  return J50(B)
}
import {
  readFile as XN4
} from "node:fs/promises";
import {
  createRequire as VN4
} from "node:module";
var zQ1 = await C50(await XN4(VN4(import.meta.url).resolve("./yoga.wasm")));

function Yy1({
  onlyFirst: A = !1
} = {}) {
  let Q = [
    "[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))",
    "(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"
  ].join("|");
  return new RegExp(Q, A ? void 0 : "g")
}
var KN4 = Yy1();

function IG(A) {
  if (typeof A !== "string") throw new TypeError(`Expected a \`string\`, got \`${typeof A}\``);
  return A.replace(KN4, "")
}

function X50(A) {
  return A === 161 || A === 164 || A === 167 || A === 168 || A === 170 || A === 173 || A === 174 || A >= 176 && A <=
    180 || A >= 182 && A <= 186 || A >= 188 && A <= 191 || A === 198 || A === 208 || A === 215 || A === 216 || A >=
    222 && A <= 225 || A === 230 || A >= 232 && A <= 234 || A === 236 || A === 237 || A === 240 || A === 242 || A ===
    243 || A >= 247 && A <= 250 || A === 252 || A === 254 || A === 257 || A === 273 || A === 275 || A === 283 || A ===
    294 || A === 295 || A === 299 || A >= 305 && A <= 307 || A === 312 || A >= 319 && A <= 322 || A === 324 || A >=
    328 && A <= 331 || A === 333 || A === 338 || A === 339 || A === 358 || A === 359 || A === 363 || A === 462 || A ===
    464 || A === 466 || A === 468 || A === 470 || A === 472 || A === 474 || A === 476 || A === 593 || A === 609 || A ===
    708 || A === 711 || A >= 713 && A <= 715 || A === 717 || A === 720 || A >= 728 && A <= 731 || A === 733 || A ===
    735 || A >= 768 && A <= 879 || A >= 913 && A <= 929 || A >= 931 && A <= 937 || A >= 945 && A <= 961 || A >= 963 &&
    A <= 969 || A === 1025 || A >= 1040 && A <= 1103 || A === 1105 || A === 8208 || A >= 8211 && A <= 8214 || A ===
    8216 || A === 8217 || A === 8220 || A === 8221 || A >= 8224 && A <= 8226 || A >= 8228 && A <= 8231 || A === 8240 ||
    A === 8242 || A === 8243 || A === 8245 || A === 8251 || A === 8254 || A === 8308 || A === 8319 || A >= 8321 && A <=
    8324 || A === 8364 || A === 8451 || A === 8453 || A === 8457 || A === 8467 || A === 8470 || A === 8481 || A ===
    8482 || A === 8486 || A === 8491 || A === 8531 || A === 8532 || A >= 8539 && A <= 8542 || A >= 8544 && A <= 8555 ||
    A >= 8560 && A <= 8569 || A === 8585 || A >= 8592 && A <= 8601 || A === 8632 || A === 8633 || A === 8658 || A ===
    8660 || A === 8679 || A === 8704 || A === 8706 || A === 8707 || A === 8711 || A === 8712 || A === 8715 || A ===
    8719 || A === 8721 || A === 8725 || A === 8730 || A >= 8733 && A <= 8736 || A === 8739 || A === 8741 || A >= 8743 &&
    A <= 8748 || A === 8750 || A >= 8756 && A <= 8759 || A === 8764 || A === 8765 || A === 8776 || A === 8780 || A ===
    8786 || A === 8800 || A === 8801 || A >= 8804 && A <= 8807 || A === 8810 || A === 8811 || A === 8814 || A ===
    8815 || A === 8834 || A === 8835 || A === 8838 || A === 8839 || A === 8853 || A === 8857 || A === 8869 || A ===
    8895 || A === 8978 || A >= 9312 && A <= 9449 || A >= 9451 && A <= 9547 || A >= 9552 && A <= 9587 || A >= 9600 &&
    A <= 9615 || A >= 9618 && A <= 9621 || A === 9632 || A === 9633 || A >= 9635 && A <= 9641 || A === 9650 || A ===
    9651 || A === 9654 || A === 9655 || A === 9660 || A === 9661 || A === 9664 || A === 9665 || A >= 9670 && A <=
    9672 || A === 9675 || A >= 9678 && A <= 9681 || A >= 9698 && A <= 9701 || A === 9711 || A === 9733 || A === 9734 ||
    A === 9737 || A === 9742 || A === 9743 || A === 9756 || A === 9758 || A === 9792 || A === 9794 || A === 9824 ||
    A === 9825 || A >= 9827 && A <= 9829 || A >= 9831 && A <= 9834 || A === 9836 || A === 9837 || A === 9839 || A ===
    9886 || A === 9887 || A === 9919 || A >= 9926 && A <= 9933 || A >= 9935 && A <= 9939 || A >= 9941 && A <= 9953 ||
    A === 9955 || A === 9960 || A === 9961 || A >= 9963 && A <= 9969 || A === 9972 || A >= 9974 && A <= 9977 || A ===
    9979 || A === 9980 || A === 9982 || A === 9983 || A === 10045 || A >= 10102 && A <= 10111 || A >= 11094 && A <=
    11097 || A >= 12872 && A <= 12879 || A >= 57344 && A <= 63743 || A >= 65024 && A <= 65039 || A === 65533 || A >=
    127232 && A <= 127242 || A >= 127248 && A <= 127277 || A >= 127280 && A <= 127337 || A >= 127344 && A <= 127373 ||
    A === 127375 || A === 127376 || A >= 127387 && A <= 127404 || A >= 917760 && A <= 917999 || A >= 983040 && A <=
    1048573 || A >= 1048576 && A <= 1114109
}

function V50(A) {
  return A === 12288 || A >= 65281 && A <= 65376 || A >= 65504 && A <= 65510
}

function K50(A) {
  return A >= 4352 && A <= 4447 || A === 8986 || A === 8987 || A === 9001 || A === 9002 || A >= 9193 && A <= 9196 ||
    A === 9200 || A === 9203 || A === 9725 || A === 9726 || A === 9748 || A === 9749 || A >= 9776 && A <= 9783 || A >=
    9800 && A <= 9811 || A === 9855 || A >= 9866 && A <= 9871 || A === 9875 || A === 9889 || A === 9898 || A === 9899 ||
    A === 9917 || A === 9918 || A === 9924 || A === 9925 || A === 9934 || A === 9940 || A === 9962 || A === 9970 ||
    A === 9971 || A === 9973 || A === 9978 || A === 9981 || A === 9989 || A === 9994 || A === 9995 || A === 10024 ||
    A === 10060 || A === 10062 || A >= 10067 && A <= 10069 || A === 10071 || A >= 10133 && A <= 10135 || A === 10160 ||
    A === 10175 || A === 11035 || A === 11036 || A === 11088 || A === 11093 || A >= 11904 && A <= 11929 || A >= 11931 &&
    A <= 12019 || A >= 12032 && A <= 12245 || A >= 12272 && A <= 12287 || A >= 12289 && A <= 12350 || A >= 12353 && A <=
    12438 || A >= 12441 && A <= 12543 || A >= 12549 && A <= 12591 || A >= 12593 && A <= 12686 || A >= 12688 && A <=
    12773 || A >= 12783 && A <= 12830 || A >= 12832 && A <= 12871 || A >= 12880 && A <= 42124 || A >= 42128 && A <=
    42182 || A >= 43360 && A <= 43388 || A >= 44032 && A <= 55203 || A >= 63744 && A <= 64255 || A >= 65040 && A <=
    65049 || A >= 65072 && A <= 65106 || A >= 65108 && A <= 65126 || A >= 65128 && A <= 65131 || A >= 94176 && A <=
    94180 || A === 94192 || A === 94193 || A >= 94208 && A <= 100343 || A >= 100352 && A <= 101589 || A >= 101631 &&
    A <= 101640 || A >= 110576 && A <= 110579 || A >= 110581 && A <= 110587 || A === 110589 || A === 110590 || A >=
    110592 && A <= 110882 || A === 110898 || A >= 110928 && A <= 110930 || A === 110933 || A >= 110948 && A <= 110951 ||
    A >= 110960 && A <= 111355 || A >= 119552 && A <= 119638 || A >= 119648 && A <= 119670 || A === 126980 || A ===
    127183 || A === 127374 || A >= 127377 && A <= 127386 || A >= 127488 && A <= 127490 || A >= 127504 && A <= 127547 ||
    A >= 127552 && A <= 127560 || A === 127568 || A === 127569 || A >= 127584 && A <= 127589 || A >= 127744 && A <=
    127776 || A >= 127789 && A <= 127797 || A >= 127799 && A <= 127868 || A >= 127870 && A <= 127891 || A >= 127904 &&
    A <= 127946 || A >= 127951 && A <= 127955 || A >= 127968 && A <= 127984 || A === 127988 || A >= 127992 && A <=
    128062 || A === 128064 || A >= 128066 && A <= 128252 || A >= 128255 && A <= 128317 || A >= 128331 && A <= 128334 ||
    A >= 128336 && A <= 128359 || A === 128378 || A === 128405 || A === 128406 || A === 128420 || A >= 128507 && A <=
    128591 || A >= 128640 && A <= 128709 || A === 128716 || A >= 128720 && A <= 128722 || A >= 128725 && A <= 128727 ||
    A >= 128732 && A <= 128735 || A === 128747 || A === 128748 || A >= 128756 && A <= 128764 || A >= 128992 && A <=
    129003 || A === 129008 || A >= 129292 && A <= 129338 || A >= 129340 && A <= 129349 || A >= 129351 && A <= 129535 ||
    A >= 129648 && A <= 129660 || A >= 129664 && A <= 129673 || A >= 129679 && A <= 129734 || A >= 129742 && A <=
    129756 || A >= 129759 && A <= 129769 || A >= 129776 && A <= 129784 || A >= 131072 && A <= 196605 || A >= 196608 &&
    A <= 262141
}

function HN4(A) {
  if (!Number.isSafeInteger(A)) throw new TypeError(`Expected a code point, got \`${typeof A}\`.`)
}

function kM(A, {
  ambiguousAsWide: B = !1
} = {}) {
  if (HN4(A), V50(A) || K50(A) || B && X50(A)) return 2;
  return 1
}
var w50 = J1(z50(), 1),
  zN4 = new Intl.Segmenter,
  wN4 = /^\p{Default_Ignorable_Code_Point}$/u;

function bi(A, B = {}) {
  if (typeof A !== "string" || A.length === 0) return 0;
  let {
    ambiguousIsNarrow: Q = !0,
    countAnsiEscapeCodes: I = !1
  } = B;
  if (!I) A = IG(A);
  if (A.length === 0) return 0;
  let G = 0,
    D = {
      ambiguousAsWide: !Q
    };
  for (let {
      segment: Z
    }
    of zN4.segment(A)) {
    let Y = Z.codePointAt(0);
    if (Y <= 31 || Y >= 127 && Y <= 159) continue;
    if (Y >= 8203 && Y <= 8207 || Y === 65279) continue;
    if (Y >= 768 && Y <= 879 || Y >= 6832 && Y <= 6911 || Y >= 7616 && Y <= 7679 || Y >= 8400 && Y <= 8447 || Y >=
      65056 && Y <= 65071) continue;
    if (Y >= 55296 && Y <= 57343) continue;