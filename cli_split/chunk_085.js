// Chunk 85
// Lines 255001-258000
// Size: 80500 bytes

`;
      return Y++, F = I, Z = 14
    }
    switch (R) {
      case 123:
        return I++, Z = 1;
      case 125:
        return I++, Z = 2;
      case 91:
        return I++, Z = 3;
      case 93:
        return I++, Z = 4;
      case 58:
        return I++, Z = 6;
      case 44:
        return I++, Z = 5;
      case 34:
        return I++, G = U(), Z = 10;
      case 47:
        let T = I - 1;
        if (A.charCodeAt(I + 1) === 47) {
          I += 2;
          while (I < Q) {
            if (zc(A.charCodeAt(I))) break;
            I++
          }
          return G = A.substring(T, I), Z = 12
        }
        if (A.charCodeAt(I + 1) === 42) {
          I += 2;
          let O = Q - 1,
            S = !1;
          while (I < O) {
            let f = A.charCodeAt(I);
            if (f === 42 && A.charCodeAt(I + 1) === 47) {
              I += 2, S = !0;
              break
            }
            if (I++, zc(f)) {
              if (f === 13 && A.charCodeAt(I) === 10) I++;
              Y++, F = I
            }
          }
          if (!S) I++, C = 1;
          return G = A.substring(T, I), Z = 13
        }
        return G += String.fromCharCode(R), I++, Z = 16;
      case 45:
        if (G += String.fromCharCode(R), I++, I === Q || !qx(A.charCodeAt(I))) return Z = 16;
      case 48:
      case 49:
      case 50:
      case 51:
      case 52:
      case 53:
      case 54:
      case 55:
      case 56:
      case 57:
        return G += K(), Z = 11;
      default:
        while (I < Q && q(R)) I++, R = A.charCodeAt(I);
        if (D !== I) {
          switch (G = A.substring(D, I), G) {
            case "true":
              return Z = 8;
            case "false":
              return Z = 9;
            case "null":
              return Z = 7
          }
          return Z = 16
        }
        return G += String.fromCharCode(R), I++, Z = 16
    }
  }

  function q(R) {
    if (zM1(R) || zc(R)) return !1;
    switch (R) {
      case 125:
      case 93:
      case 123:
      case 91:
      case 34:
      case 58:
      case 44:
      case 47:
        return !1
    }
    return !0
  }

  function M() {
    let R;
    do R = N(); while (R >= 12 && R <= 15);
    return R
  }
  return {
    setPosition: V,
    getPosition: () => I,
    scan: B ? M : N,
    getToken: () => Z,
    getTokenValue: () => G,
    getTokenOffset: () => D,
    getTokenLength: () => I - D,
    getTokenStartLine: () => W,
    getTokenStartCharacter: () => D - J,
    getTokenError: () => C
  }
}

function zM1(A) {
  return A === 32 || A === 9
}

function zc(A) {
  return A === 10 || A === 13
}

function qx(A) {
  return A >= 48 && A <= 57
}
var bLA;
(function(A) {
  A[A.lineFeed = 10] = "lineFeed", A[A.carriageReturn = 13] = "carriageReturn", A[A.space = 32] = "space", A[A._0 =
      48] = "_0", A[A._1 = 49] = "_1", A[A._2 = 50] = "_2", A[A._3 = 51] = "_3", A[A._4 = 52] = "_4", A[A._5 = 53] =
    "_5", A[A._6 = 54] = "_6", A[A._7 = 55] = "_7", A[A._8 = 56] = "_8", A[A._9 = 57] = "_9", A[A.a = 97] = "a", A[A
      .b = 98] = "b", A[A.c = 99] = "c", A[A.d = 100] = "d", A[A.e = 101] = "e", A[A.f = 102] = "f", A[A.g = 103] =
    "g", A[A.h = 104] = "h", A[A.i = 105] = "i", A[A.j = 106] = "j", A[A.k = 107] = "k", A[A.l = 108] = "l", A[A.m =
      109] = "m", A[A.n = 110] = "n", A[A.o = 111] = "o", A[A.p = 112] = "p", A[A.q = 113] = "q", A[A.r = 114] = "r",
    A[A.s = 115] = "s", A[A.t = 116] = "t", A[A.u = 117] = "u", A[A.v = 118] = "v", A[A.w = 119] = "w", A[A.x = 120] =
    "x", A[A.y = 121] = "y", A[A.z = 122] = "z", A[A.A = 65] = "A", A[A.B = 66] = "B", A[A.C = 67] = "C", A[A.D =
    68] = "D", A[A.E = 69] = "E", A[A.F = 70] = "F", A[A.G = 71] = "G", A[A.H = 72] = "H", A[A.I = 73] = "I", A[A.J =
      74] = "J", A[A.K = 75] = "K", A[A.L = 76] = "L", A[A.M = 77] = "M", A[A.N = 78] = "N", A[A.O = 79] = "O", A[A
      .P = 80] = "P", A[A.Q = 81] = "Q", A[A.R = 82] = "R", A[A.S = 83] = "S", A[A.T = 84] = "T", A[A.U = 85] = "U",
    A[A.V = 86] = "V", A[A.W = 87] = "W", A[A.X = 88] = "X", A[A.Y = 89] = "Y", A[A.Z = 90] = "Z", A[A.asterisk =
    42] = "asterisk", A[A.backslash = 92] = "backslash", A[A.closeBrace = 125] = "closeBrace", A[A.closeBracket =
    93] = "closeBracket", A[A.colon = 58] = "colon", A[A.comma = 44] = "comma", A[A.dot = 46] = "dot", A[A
      .doubleQuote = 34] = "doubleQuote", A[A.minus = 45] = "minus", A[A.openBrace = 123] = "openBrace", A[A
      .openBracket = 91] = "openBracket", A[A.plus = 43] = "plus", A[A.slash = 47] = "slash", A[A.formFeed = 12] =
    "formFeed", A[A.tab = 9] = "tab"
})(bLA || (bLA = {}));
var yW = new Array(20).fill(0).map((A, B) => {
  return " ".repeat(B)
});
var wM1 = {
    " ": {
      "\n": new Array(200).fill(0).map((A, B) => {
        return `
` + " ".repeat(B)
      }),
      "\r": new Array(200).fill(0).map((A, B) => {
        return "\r" + " ".repeat(B)
      }),
      "\r\n": new Array(200).fill(0).map((A, B) => {
        return `\r
` + " ".repeat(B)
      })
    },
    "\t": {
      "\n": new Array(200).fill(0).map((A, B) => {
        return `
` + "\t".repeat(B)
      }),
      "\r": new Array(200).fill(0).map((A, B) => {
        return "\r" + "\t".repeat(B)
      }),
      "\r\n": new Array(200).fill(0).map((A, B) => {
        return `\r
` + "\t".repeat(B)
      })
    }
  },
  gLA = [`
`, "\r", `\r
`];

function EM1(A, B, Q) {
  let I, G, D, Z, Y;
  if (B) {
    Z = B.offset, Y = Z + B.length, D = Z;
    while (D > 0 && !Ec(A, D - 1)) D--;
    let O = Y;
    while (O < A.length && !Ec(A, O)) O++;
    G = A.substring(D, O), I = n_9(G, Q)
  } else G = A, I = 0, D = 0, Z = 0, Y = A.length;
  let W = a_9(Q, A),
    F = gLA.includes(W),
    J = 0,
    C = 0,
    X;
  if (Q.insertSpaces) X = yW[Q.tabSize || 4] ?? Mx(yW[1], Q.tabSize || 4);
  else X = "\t";
  let V = X === "\t" ? "\t" : " ",
    K = wc(G, !1),
    U = !1;

  function N() {
    if (J > 1) return Mx(W, J) + Mx(X, I + C);
    let O = X.length * (I + C);
    if (!F || O > wM1[V][W].length) return W + Mx(X, I + C);
    if (O <= 0) return W;
    return wM1[V][W][O]
  }

  function q() {
    let O = K.scan();
    J = 0;
    while (O === 15 || O === 14) {
      if (O === 14 && Q.keepLines) J += 1;
      else if (O === 14) J = 1;
      O = K.scan()
    }
    return U = O === 16 || K.getTokenError() !== 0, O
  }
  let M = [];

  function R(O, S, f) {
    if (!U && (!B || S < Y && f > Z) && A.substring(S, f) !== O) M.push({
      offset: S,
      length: f - S,
      content: O
    })
  }
  let T = q();
  if (Q.keepLines && J > 0) R(Mx(W, J), 0, 0);
  if (T !== 17) {
    let O = K.getTokenOffset() + D,
      S = X.length * I < 20 && Q.insertSpaces ? yW[X.length * I] : Mx(X, I);
    R(S, D, O)
  }
  while (T !== 17) {
    let O = K.getTokenOffset() + K.getTokenLength() + D,
      S = q(),
      f = "",
      a = !1;
    while (J === 0 && (S === 12 || S === 13)) {
      let Y1 = K.getTokenOffset() + D;
      R(yW[1], O, Y1), O = K.getTokenOffset() + K.getTokenLength() + D, a = S === 12, f = a ? N() : "", S = q()
    }
    if (S === 2) {
      if (T !== 1) C--;
      if (Q.keepLines && J > 0 || !Q.keepLines && T !== 1) f = N();
      else if (Q.keepLines) f = yW[1]
    } else if (S === 4) {
      if (T !== 3) C--;
      if (Q.keepLines && J > 0 || !Q.keepLines && T !== 3) f = N();
      else if (Q.keepLines) f = yW[1]
    } else {
      switch (T) {
        case 3:
        case 1:
          if (C++, Q.keepLines && J > 0 || !Q.keepLines) f = N();
          else f = yW[1];
          break;
        case 5:
          if (Q.keepLines && J > 0 || !Q.keepLines) f = N();
          else f = yW[1];
          break;
        case 12:
          f = N();
          break;
        case 13:
          if (J > 0) f = N();
          else if (!a) f = yW[1];
          break;
        case 6:
          if (Q.keepLines && J > 0) f = N();
          else if (!a) f = yW[1];
          break;
        case 10:
          if (Q.keepLines && J > 0) f = N();
          else if (S === 6 && !a) f = "";
          break;
        case 7:
        case 8:
        case 9:
        case 11:
        case 2:
        case 4:
          if (Q.keepLines && J > 0) f = N();
          else if ((S === 12 || S === 13) && !a) f = yW[1];
          else if (S !== 5 && S !== 17) U = !0;
          break;
        case 16:
          U = !0;
          break
      }
      if (J > 0 && (S === 12 || S === 13)) f = N()
    }
    if (S === 17)
      if (Q.keepLines && J > 0) f = N();
      else f = Q.insertFinalNewline ? W : "";
    let g = K.getTokenOffset() + D;
    R(f, O, g), T = S
  }
  return M
}

function Mx(A, B) {
  let Q = "";
  for (let I = 0; I < B; I++) Q += A;
  return Q
}

function n_9(A, B) {
  let Q = 0,
    I = 0,
    G = B.tabSize || 4;
  while (Q < A.length) {
    let D = A.charAt(Q);
    if (D === yW[1]) I++;
    else if (D === "\t") I += G;
    else break;
    Q++
  }
  return Math.floor(I / G)
}

function a_9(A, B) {
  for (let Q = 0; Q < B.length; Q++) {
    let I = B.charAt(Q);
    if (I === "\r") {
      if (Q + 1 < B.length && B.charAt(Q + 1) === `
`) return `\r
`;
      return "\r"
    } else if (I === `
`) return `
`
  }
  return A && A.eol || `
`
}

function Ec(A, B) {
  return `\r
`.indexOf(A.charAt(B)) !== -1
}
var Uc;
(function(A) {
  A.DEFAULT = {
    allowTrailingComma: !1
  }
})(Uc || (Uc = {}));

function hLA(A, B = [], Q = Uc.DEFAULT) {
  let I = null,
    G = [],
    D = [];

  function Z(W) {
    if (Array.isArray(G)) G.push(W);
    else if (I !== null) G[I] = W
  }
  return NM1(A, {
    onObjectBegin: () => {
      let W = {};
      Z(W), D.push(G), G = W, I = null
    },
    onObjectProperty: (W) => {
      I = W
    },
    onObjectEnd: () => {
      G = D.pop()
    },
    onArrayBegin: () => {
      let W = [];
      Z(W), D.push(G), G = W, I = null
    },
    onArrayEnd: () => {
      G = D.pop()
    },
    onLiteralValue: Z,
    onError: (W, F, J) => {
      B.push({
        error: W,
        offset: F,
        length: J
      })
    }
  }, Q), G[0]
}

function UM1(A, B = [], Q = Uc.DEFAULT) {
  let I = {
    type: "array",
    offset: -1,
    length: -1,
    children: [],
    parent: void 0
  };

  function G(W) {
    if (I.type === "property") I.length = W - I.offset, I = I.parent
  }

  function D(W) {
    return I.children.push(W), W
  }
  NM1(A, {
    onObjectBegin: (W) => {
      I = D({
        type: "object",
        offset: W,
        length: -1,
        parent: I,
        children: []
      })
    },
    onObjectProperty: (W, F, J) => {
      I = D({
        type: "property",
        offset: F,
        length: -1,
        parent: I,
        children: []
      }), I.children.push({
        type: "string",
        value: W,
        offset: F,
        length: J,
        parent: I
      })
    },
    onObjectEnd: (W, F) => {
      G(W + F), I.length = W + F - I.offset, I = I.parent, G(W + F)
    },
    onArrayBegin: (W, F) => {
      I = D({
        type: "array",
        offset: W,
        length: -1,
        parent: I,
        children: []
      })
    },
    onArrayEnd: (W, F) => {
      I.length = W + F - I.offset, I = I.parent, G(W + F)
    },
    onLiteralValue: (W, F, J) => {
      D({
        type: r_9(W),
        offset: F,
        length: J,
        parent: I,
        value: W
      }), G(F + J)
    },
    onSeparator: (W, F, J) => {
      if (I.type === "property") {
        if (W === ":") I.colonOffset = F;
        else if (W === ",") G(F)
      }
    },
    onError: (W, F, J) => {
      B.push({
        error: W,
        offset: F,
        length: J
      })
    }
  }, Q);
  let Y = I.children[0];
  if (Y) delete Y.parent;
  return Y
}

function N41(A, B) {
  if (!A) return;
  let Q = A;
  for (let I of B)
    if (typeof I === "string") {
      if (Q.type !== "object" || !Array.isArray(Q.children)) return;
      let G = !1;
      for (let D of Q.children)
        if (Array.isArray(D.children) && D.children[0].value === I && D.children.length === 2) {
          Q = D.children[1], G = !0;
          break
        } if (!G) return
    } else {
      let G = I;
      if (Q.type !== "array" || G < 0 || !Array.isArray(Q.children) || G >= Q.children.length) return;
      Q = Q.children[G]
    } return Q
}

function NM1(A, B, Q = Uc.DEFAULT) {
  let I = wc(A, !1),
    G = [];

  function D(w1) {
    return w1 ? () => w1(I.getTokenOffset(), I.getTokenLength(), I.getTokenStartLine(), I.getTokenStartCharacter()) :
    () => !0
  }

  function Z(w1) {
    return w1 ? () => w1(I.getTokenOffset(), I.getTokenLength(), I.getTokenStartLine(), I.getTokenStartCharacter(),
    () => G.slice()) : () => !0
  }

  function Y(w1) {
    return w1 ? (H1) => w1(H1, I.getTokenOffset(), I.getTokenLength(), I.getTokenStartLine(), I
    .getTokenStartCharacter()) : () => !0
  }

  function W(w1) {
    return w1 ? (H1) => w1(H1, I.getTokenOffset(), I.getTokenLength(), I.getTokenStartLine(), I
    .getTokenStartCharacter(), () => G.slice()) : () => !0
  }
  let F = Z(B.onObjectBegin),
    J = W(B.onObjectProperty),
    C = D(B.onObjectEnd),
    X = Z(B.onArrayBegin),
    V = D(B.onArrayEnd),
    K = W(B.onLiteralValue),
    U = Y(B.onSeparator),
    N = D(B.onComment),
    q = Y(B.onError),
    M = Q && Q.disallowComments,
    R = Q && Q.allowTrailingComma;

  function T() {
    while (!0) {
      let w1 = I.scan();
      switch (I.getTokenError()) {
        case 4:
          O(14);
          break;
        case 5:
          O(15);
          break;
        case 3:
          O(13);
          break;
        case 1:
          if (!M) O(11);
          break;
        case 2:
          O(12);
          break;
        case 6:
          O(16);
          break
      }
      switch (w1) {
        case 12:
        case 13:
          if (M) O(10);
          else N();
          break;
        case 16:
          O(1);
          break;
        case 15:
        case 14:
          break;
        default:
          return w1
      }
    }
  }

  function O(w1, H1 = [], x = []) {
    if (q(w1), H1.length + x.length > 0) {
      let F1 = I.getToken();
      while (F1 !== 17) {
        if (H1.indexOf(F1) !== -1) {
          T();
          break
        } else if (x.indexOf(F1) !== -1) break;
        F1 = T()
      }
    }
  }

  function S(w1) {
    let H1 = I.getTokenValue();
    if (w1) K(H1);
    else J(H1), G.push(H1);
    return T(), !0
  }

  function f() {
    switch (I.getToken()) {
      case 11:
        let w1 = I.getTokenValue(),
          H1 = Number(w1);
        if (isNaN(H1)) O(2), H1 = 0;
        K(H1);
        break;
      case 7:
        K(null);
        break;
      case 8:
        K(!0);
        break;
      case 9:
        K(!1);
        break;
      default:
        return !1
    }
    return T(), !0
  }

  function a() {
    if (I.getToken() !== 10) return O(3, [], [2, 5]), !1;
    if (S(!1), I.getToken() === 6) {
      if (U(":"), T(), !r()) O(4, [], [2, 5])
    } else O(5, [], [2, 5]);
    return G.pop(), !0
  }

  function g() {
    F(), T();
    let w1 = !1;
    while (I.getToken() !== 2 && I.getToken() !== 17) {
      if (I.getToken() === 5) {
        if (!w1) O(4, [], []);
        if (U(","), T(), I.getToken() === 2 && R) break
      } else if (w1) O(6, [], []);
      if (!a()) O(4, [], [2, 5]);
      w1 = !0
    }
    if (C(), I.getToken() !== 2) O(7, [2], []);
    else T();
    return !0
  }

  function Y1() {
    X(), T();
    let w1 = !0,
      H1 = !1;
    while (I.getToken() !== 4 && I.getToken() !== 17) {
      if (I.getToken() === 5) {
        if (!H1) O(4, [], []);
        if (U(","), T(), I.getToken() === 4 && R) break
      } else if (H1) O(6, [], []);
      if (w1) G.push(0), w1 = !1;
      else G[G.length - 1]++;
      if (!r()) O(4, [], [4, 5]);
      H1 = !0
    }
    if (V(), !w1) G.pop();
    if (I.getToken() !== 4) O(8, [4], []);
    else T();
    return !0
  }

  function r() {
    switch (I.getToken()) {
      case 3:
        return Y1();
      case 1:
        return g();
      case 10:
        return S(!0);
      default:
        return f()
    }
  }
  if (T(), I.getToken() === 17) {
    if (Q.allowEmptyContent) return !0;
    return O(4, [], []), !1
  }
  if (!r()) return O(4, [], []), !1;
  if (I.getToken() !== 17) O(9, [], []);
  return !0
}

function r_9(A) {
  switch (typeof A) {
    case "boolean":
      return "boolean";
    case "number":
      return "number";
    case "string":
      return "string";
    case "object": {
      if (!A) return "null";
      else if (Array.isArray(A)) return "array";
      return "object"
    }
    default:
      return "null"
  }
}

function mLA(A, B, Q, I) {
  let G = B.slice(),
    Z = UM1(A, []),
    Y = void 0,
    W = void 0;
  while (G.length > 0)
    if (W = G.pop(), Y = N41(Z, G), Y === void 0 && Q !== void 0)
      if (typeof W === "string") Q = {
        [W]: Q
      };
      else Q = [Q];
  else break;
  if (!Y) {
    if (Q === void 0) throw new Error("Can not delete in empty document");
    return yT(A, {
      offset: Z ? Z.offset : 0,
      length: Z ? Z.length : 0,
      content: JSON.stringify(Q)
    }, I)
  } else if (Y.type === "object" && typeof W === "string" && Array.isArray(Y.children)) {
    let F = N41(Y, [W]);
    if (F !== void 0)
      if (Q === void 0) {
        if (!F.parent) throw new Error("Malformed AST");
        let J = Y.children.indexOf(F.parent),
          C, X = F.parent.offset + F.parent.length;
        if (J > 0) {
          let V = Y.children[J - 1];
          C = V.offset + V.length
        } else if (C = Y.offset + 1, Y.children.length > 1) X = Y.children[1].offset;
        return yT(A, {
          offset: C,
          length: X - C,
          content: ""
        }, I)
      } else return yT(A, {
        offset: F.offset,
        length: F.length,
        content: JSON.stringify(Q)
      }, I);
    else {
      if (Q === void 0) return [];
      let J = `${JSON.stringify(W)}: ${JSON.stringify(Q)}`,
        C = I.getInsertionIndex ? I.getInsertionIndex(Y.children.map((V) => V.children[0].value)) : Y.children.length,
        X;
      if (C > 0) {
        let V = Y.children[C - 1];
        X = {
          offset: V.offset + V.length,
          length: 0,
          content: "," + J
        }
      } else if (Y.children.length === 0) X = {
        offset: Y.offset + 1,
        length: 0,
        content: J
      };
      else X = {
        offset: Y.offset + 1,
        length: 0,
        content: J + ","
      };
      return yT(A, X, I)
    }
  } else if (Y.type === "array" && typeof W === "number" && Array.isArray(Y.children)) {
    let F = W;
    if (F === -1) {
      let J = `${JSON.stringify(Q)}`,
        C;
      if (Y.children.length === 0) C = {
        offset: Y.offset + 1,
        length: 0,
        content: J
      };
      else {
        let X = Y.children[Y.children.length - 1];
        C = {
          offset: X.offset + X.length,
          length: 0,
          content: "," + J
        }
      }
      return yT(A, C, I)
    } else if (Q === void 0 && Y.children.length >= 0) {
      let J = W,
        C = Y.children[J],
        X;
      if (Y.children.length === 1) X = {
        offset: Y.offset + 1,
        length: Y.length - 2,
        content: ""
      };
      else if (Y.children.length - 1 === J) {
        let V = Y.children[J - 1],
          K = V.offset + V.length,
          U = Y.offset + Y.length;
        X = {
          offset: K,
          length: U - 2 - K,
          content: ""
        }
      } else X = {
        offset: C.offset,
        length: Y.children[J + 1].offset - C.offset,
        content: ""
      };
      return yT(A, X, I)
    } else if (Q !== void 0) {
      let J, C = `${JSON.stringify(Q)}`;
      if (!I.isArrayInsertion && Y.children.length > W) {
        let X = Y.children[W];
        J = {
          offset: X.offset,
          length: X.length,
          content: C
        }
      } else if (Y.children.length === 0 || W === 0) J = {
        offset: Y.offset + 1,
        length: 0,
        content: Y.children.length === 0 ? C : C + ","
      };
      else {
        let X = W > Y.children.length ? Y.children.length : W,
          V = Y.children[X - 1];
        J = {
          offset: V.offset + V.length,
          length: 0,
          content: "," + C
        }
      }
      return yT(A, J, I)
    } else throw new Error(
      `Can not ${Q===void 0?"remove":I.isArrayInsertion?"insert":"modify"} Array index ${F} as length is not sufficient`
      )
  } else throw new Error(`Can not add ${typeof W!=="number"?"index":"property"} to parent of type ${Y.type}`)
}

function yT(A, B, Q) {
  if (!Q.formattingOptions) return [B];
  let I = $41(A, B),
    G = B.offset,
    D = B.offset + B.content.length;
  if (B.length === 0 || B.content.length === 0) {
    while (G > 0 && !Ec(I, G - 1)) G--;
    while (D < I.length && !Ec(I, D)) D++
  }
  let Z = EM1(I, {
    offset: G,
    length: D - G
  }, {
    ...Q.formattingOptions,
    keepLines: !1
  });
  for (let W = Z.length - 1; W >= 0; W--) {
    let F = Z[W];
    I = $41(I, F), G = Math.min(G, F.offset), D = Math.max(D, F.offset + F.length), D += F.content.length - F.length
  }
  let Y = A.length - (I.length - D) - G;
  return [{
    offset: G,
    length: Y,
    content: I.substring(G, D)
  }]
}

function $41(A, B) {
  return A.substring(0, B.offset) + B.content + A.substring(B.offset + B.length)
}
var dLA;
(function(A) {
  A[A.None = 0] = "None", A[A.UnexpectedEndOfComment = 1] = "UnexpectedEndOfComment", A[A.UnexpectedEndOfString = 2] =
    "UnexpectedEndOfString", A[A.UnexpectedEndOfNumber = 3] = "UnexpectedEndOfNumber", A[A.InvalidUnicode = 4] =
    "InvalidUnicode", A[A.InvalidEscapeCharacter = 5] = "InvalidEscapeCharacter", A[A.InvalidCharacter = 6] =
    "InvalidCharacter"
})(dLA || (dLA = {}));
var uLA;
(function(A) {
  A[A.OpenBraceToken = 1] = "OpenBraceToken", A[A.CloseBraceToken = 2] = "CloseBraceToken", A[A.OpenBracketToken =
    3] = "OpenBracketToken", A[A.CloseBracketToken = 4] = "CloseBracketToken", A[A.CommaToken = 5] = "CommaToken", A[A
      .ColonToken = 6] = "ColonToken", A[A.NullKeyword = 7] = "NullKeyword", A[A.TrueKeyword = 8] = "TrueKeyword", A[A
      .FalseKeyword = 9] = "FalseKeyword", A[A.StringLiteral = 10] = "StringLiteral", A[A.NumericLiteral = 11] =
    "NumericLiteral", A[A.LineCommentTrivia = 12] = "LineCommentTrivia", A[A.BlockCommentTrivia = 13] =
    "BlockCommentTrivia", A[A.LineBreakTrivia = 14] = "LineBreakTrivia", A[A.Trivia = 15] = "Trivia", A[A.Unknown =
      16] = "Unknown", A[A.EOF = 17] = "EOF"
})(uLA || (uLA = {}));
var $M1 = hLA;
var pLA;
(function(A) {
  A[A.InvalidSymbol = 1] = "InvalidSymbol", A[A.InvalidNumberFormat = 2] = "InvalidNumberFormat", A[A
      .PropertyNameExpected = 3] = "PropertyNameExpected", A[A.ValueExpected = 4] = "ValueExpected", A[A
      .ColonExpected = 5] = "ColonExpected", A[A.CommaExpected = 6] = "CommaExpected", A[A.CloseBraceExpected = 7] =
    "CloseBraceExpected", A[A.CloseBracketExpected = 8] = "CloseBracketExpected", A[A.EndOfFileExpected = 9] =
    "EndOfFileExpected", A[A.InvalidCommentToken = 10] = "InvalidCommentToken", A[A.UnexpectedEndOfComment = 11] =
    "UnexpectedEndOfComment", A[A.UnexpectedEndOfString = 12] = "UnexpectedEndOfString", A[A.UnexpectedEndOfNumber =
      13] = "UnexpectedEndOfNumber", A[A.InvalidUnicode = 14] = "InvalidUnicode", A[A.InvalidEscapeCharacter = 15] =
    "InvalidEscapeCharacter", A[A.InvalidCharacter = 16] = "InvalidCharacter"
})(pLA || (pLA = {}));

function cLA(A, B, Q, I) {
  return mLA(A, B, Q, I)
}

function lLA(A, B) {
  let Q = B.slice(0).sort((G, D) => {
      let Z = G.offset - D.offset;
      if (Z === 0) return G.length - D.length;
      return Z
    }),
    I = A.length;
  for (let G = Q.length - 1; G >= 0; G--) {
    let D = Q[G];
    if (D.offset + D.length <= I) A = $41(A, D);
    else throw new Error("Overlapping edit");
    I = D.offset
  }
  return A
}

function T8(A) {
  if (!A) return null;
  try {
    return JSON.parse(A)
  } catch (B) {
    return g1(B), null
  }
}

function nLA(A) {
  if (!A) return null;
  try {
    return $M1(A)
  } catch (B) {
    return g1(B), null
  }
}

function aLA(A) {
  try {
    let B = iLA.readFileSync(A, "utf8");
    if (!B.trim()) return [];
    return B.split(`
`).filter((Q) => Q.trim()).map((Q) => {
      try {
        return JSON.parse(Q)
      } catch (I) {
        return g1(new Error(`Error parsing line in ${A}: ${I}`)), null
      }
    }).filter((Q) => Q !== null)
  } catch (B) {
    return g1(new Error(`Error opening file ${A}: ${B}`)), []
  }
}

function sLA(A, B) {
  try {
    if (!A || A.trim() === "") return JSON.stringify([B], null, 4);
    let Q = $M1(A);
    if (Array.isArray(Q)) {
      let I = Q.length,
        Z = cLA(A, I === 0 ? [0] : [I], B, {
          formattingOptions: {
            insertSpaces: !0,
            tabSize: 4
          },
          isArrayInsertion: !0
        });
      if (!Z || Z.length === 0) {
        let Y = [...Q, B];
        return JSON.stringify(Y, null, 4)
      }
      return lLA(A, Z)
    } else return JSON.stringify([B], null, 4)
  } catch (Q) {
    return g1(Q), JSON.stringify([B], null, 4)
  }
}
var e_9 = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

function Lx(A) {
  if (typeof A !== "string") return null;
  return e_9.test(A) ? A : null
}
import {
  dirname as wy9,
  join as _c,
  resolve as VRA
} from "path";
var qM1 = ["macos", "wsl"],
  UJ = b0(() => {
    try {
      if (process.platform === "darwin") return "macos";
      if (process.platform === "win32") return "windows";
      if (process.platform === "linux") {
        try {
          let A = b1().readFileSync("/proc/version", {
            encoding: "utf8"
          });
          if (A.toLowerCase().includes("microsoft") || A.toLowerCase().includes("wsl")) return "wsl"
        } catch (A) {
          g1(A instanceof Error ? A : new Error(String(A)))
        }
        return "linux"
      }
      return "unknown"
    } catch (A) {
      return g1(A instanceof Error ? A : new Error(String(A))), "unknown"
    }
  });
var p6;
(function(A) {
  A.assertEqual = (G) => G;

  function B(G) {}
  A.assertIs = B;

  function Q(G) {
    throw new Error
  }
  A.assertNever = Q, A.arrayToEnum = (G) => {
      let D = {};
      for (let Z of G) D[Z] = Z;
      return D
    }, A.getValidEnumValues = (G) => {
      let D = A.objectKeys(G).filter((Y) => typeof G[G[Y]] !== "number"),
        Z = {};
      for (let Y of D) Z[Y] = G[Y];
      return A.objectValues(Z)
    }, A.objectValues = (G) => {
      return A.objectKeys(G).map(function(D) {
        return G[D]
      })
    }, A.objectKeys = typeof Object.keys === "function" ? (G) => Object.keys(G) : (G) => {
      let D = [];
      for (let Z in G)
        if (Object.prototype.hasOwnProperty.call(G, Z)) D.push(Z);
      return D
    }, A.find = (G, D) => {
      for (let Z of G)
        if (D(Z)) return Z;
      return
    }, A.isInteger = typeof Number.isInteger === "function" ? (G) => Number.isInteger(G) : (G) => typeof G ===
    "number" && isFinite(G) && Math.floor(G) === G;

  function I(G, D = " | ") {
    return G.map((Z) => typeof Z === "string" ? `'${Z}'` : Z).join(D)
  }
  A.joinValues = I, A.jsonStringifyReplacer = (G, D) => {
    if (typeof D === "bigint") return D.toString();
    return D
  }
})(p6 || (p6 = {}));
var LM1;
(function(A) {
  A.mergeShapes = (B, Q) => {
    return {
      ...B,
      ...Q
    }
  }
})(LM1 || (LM1 = {}));
var q2 = p6.arrayToEnum(["string", "nan", "number", "integer", "float", "boolean", "date", "bigint", "symbol",
    "function", "undefined", "null", "array", "object", "unknown", "promise", "void", "never", "map", "set"
  ]),
  sE = (A) => {
    switch (typeof A) {
      case "undefined":
        return q2.undefined;
      case "string":
        return q2.string;
      case "number":
        return isNaN(A) ? q2.nan : q2.number;
      case "boolean":
        return q2.boolean;
      case "function":
        return q2.function;
      case "bigint":
        return q2.bigint;
      case "symbol":
        return q2.symbol;
      case "object":
        if (Array.isArray(A)) return q2.array;
        if (A === null) return q2.null;
        if (A.then && typeof A.then === "function" && A.catch && typeof A.catch === "function") return q2.promise;
        if (typeof Map !== "undefined" && A instanceof Map) return q2.map;
        if (typeof Set !== "undefined" && A instanceof Set) return q2.set;
        if (typeof Date !== "undefined" && A instanceof Date) return q2.date;
        return q2.object;
      default:
        return q2.unknown
    }
  },
  N0 = p6.arrayToEnum(["invalid_type", "invalid_literal", "custom", "invalid_union", "invalid_union_discriminator",
    "invalid_enum_value", "unrecognized_keys", "invalid_arguments", "invalid_return_type", "invalid_date",
    "invalid_string", "too_small", "too_big", "invalid_intersection_types", "not_multiple_of", "not_finite"
  ]),
  Aj9 = (A) => {
    return JSON.stringify(A, null, 2).replace(/"([^"]+)":/g, "$1:")
  };
class kW extends Error {
  get errors() {
    return this.issues
  }
  constructor(A) {
    super();
    this.issues = [], this.addIssue = (Q) => {
      this.issues = [...this.issues, Q]
    }, this.addIssues = (Q = []) => {
      this.issues = [...this.issues, ...Q]
    };
    let B = new.target.prototype;
    if (Object.setPrototypeOf) Object.setPrototypeOf(this, B);
    else this.__proto__ = B;
    this.name = "ZodError", this.issues = A
  }
  format(A) {
    let B = A || function(G) {
        return G.message
      },
      Q = {
        _errors: []
      },
      I = (G) => {
        for (let D of G.issues)
          if (D.code === "invalid_union") D.unionErrors.map(I);
          else if (D.code === "invalid_return_type") I(D.returnTypeError);
        else if (D.code === "invalid_arguments") I(D.argumentsError);
        else if (D.path.length === 0) Q._errors.push(B(D));
        else {
          let Z = Q,
            Y = 0;
          while (Y < D.path.length) {
            let W = D.path[Y];
            if (Y !== D.path.length - 1) Z[W] = Z[W] || {
              _errors: []
            };
            else Z[W] = Z[W] || {
              _errors: []
            }, Z[W]._errors.push(B(D));
            Z = Z[W], Y++
          }
        }
      };
    return I(this), Q
  }
  static assert(A) {
    if (!(A instanceof kW)) throw new Error(`Not a ZodError: ${A}`)
  }
  toString() {
    return this.message
  }
  get message() {
    return JSON.stringify(this.issues, p6.jsonStringifyReplacer, 2)
  }
  get isEmpty() {
    return this.issues.length === 0
  }
  flatten(A = (B) => B.message) {
    let B = {},
      Q = [];
    for (let I of this.issues)
      if (I.path.length > 0) B[I.path[0]] = B[I.path[0]] || [], B[I.path[0]].push(A(I));
      else Q.push(A(I));
    return {
      formErrors: Q,
      fieldErrors: B
    }
  }
  get formErrors() {
    return this.flatten()
  }
}
kW.create = (A) => {
  return new kW(A)
};
var Px = (A, B) => {
    let Q;
    switch (A.code) {
      case N0.invalid_type:
        if (A.received === q2.undefined) Q = "Required";
        else Q = `Expected ${A.expected}, received ${A.received}`;
        break;
      case N0.invalid_literal:
        Q = `Invalid literal value, expected ${JSON.stringify(A.expected,p6.jsonStringifyReplacer)}`;
        break;
      case N0.unrecognized_keys:
        Q = `Unrecognized key(s) in object: ${p6.joinValues(A.keys,", ")}`;
        break;
      case N0.invalid_union:
        Q = "Invalid input";
        break;
      case N0.invalid_union_discriminator:
        Q = `Invalid discriminator value. Expected ${p6.joinValues(A.options)}`;
        break;
      case N0.invalid_enum_value:
        Q = `Invalid enum value. Expected ${p6.joinValues(A.options)}, received '${A.received}'`;
        break;
      case N0.invalid_arguments:
        Q = "Invalid function arguments";
        break;
      case N0.invalid_return_type:
        Q = "Invalid function return type";
        break;
      case N0.invalid_date:
        Q = "Invalid date";
        break;
      case N0.invalid_string:
        if (typeof A.validation === "object")
          if ("includes" in A.validation) {
            if (Q = `Invalid input: must include "${A.validation.includes}"`, typeof A.validation.position === "number")
              Q = `${Q} at one or more positions greater than or equal to ${A.validation.position}`
          } else if ("startsWith" in A.validation) Q = `Invalid input: must start with "${A.validation.startsWith}"`;
        else if ("endsWith" in A.validation) Q = `Invalid input: must end with "${A.validation.endsWith}"`;
        else p6.assertNever(A.validation);
        else if (A.validation !== "regex") Q = `Invalid ${A.validation}`;
        else Q = "Invalid";
        break;
      case N0.too_small:
        if (A.type === "array") Q =
          `Array must contain ${A.exact?"exactly":A.inclusive?"at least":"more than"} ${A.minimum} element(s)`;
        else if (A.type === "string") Q =
          `String must contain ${A.exact?"exactly":A.inclusive?"at least":"over"} ${A.minimum} character(s)`;
        else if (A.type === "number") Q =
          `Number must be ${A.exact?"exactly equal to ":A.inclusive?"greater than or equal to ":"greater than "}${A.minimum}`;
        else if (A.type === "date") Q =
          `Date must be ${A.exact?"exactly equal to ":A.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(A.minimum))}`;
        else Q = "Invalid input";
        break;
      case N0.too_big:
        if (A.type === "array") Q =
          `Array must contain ${A.exact?"exactly":A.inclusive?"at most":"less than"} ${A.maximum} element(s)`;
        else if (A.type === "string") Q =
          `String must contain ${A.exact?"exactly":A.inclusive?"at most":"under"} ${A.maximum} character(s)`;
        else if (A.type === "number") Q =
          `Number must be ${A.exact?"exactly":A.inclusive?"less than or equal to":"less than"} ${A.maximum}`;
        else if (A.type === "bigint") Q =
          `BigInt must be ${A.exact?"exactly":A.inclusive?"less than or equal to":"less than"} ${A.maximum}`;
        else if (A.type === "date") Q =
          `Date must be ${A.exact?"exactly":A.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(A.maximum))}`;
        else Q = "Invalid input";
        break;
      case N0.custom:
        Q = "Invalid input";
        break;
      case N0.invalid_intersection_types:
        Q = "Intersection results could not be merged";
        break;
      case N0.not_multiple_of:
        Q = `Number must be a multiple of ${A.multipleOf}`;
        break;
      case N0.not_finite:
        Q = "Number must be finite";
        break;
      default:
        Q = B.defaultError, p6.assertNever(A)
    }
    return {
      message: Q
    }
  },
  eLA = Px;

function Bj9(A) {
  eLA = A
}

function q41() {
  return eLA
}
var M41 = (A) => {
    let {
      data: B,
      path: Q,
      errorMaps: I,
      issueData: G
    } = A, D = [...Q, ...G.path || []], Z = {
      ...G,
      path: D
    };
    if (G.message !== void 0) return {
      ...G,
      path: D,
      message: G.message
    };
    let Y = "",
      W = I.filter((F) => !!F).slice().reverse();
    for (let F of W) Y = F(Z, {
      data: B,
      defaultError: Y
    }).message;
    return {
      ...G,
      path: D,
      message: Y
    }
  },
  Qj9 = [];

function D2(A, B) {
  let Q = q41(),
    I = M41({
      issueData: B,
      data: A.data,
      path: A.path,
      errorMaps: [A.common.contextualErrorMap, A.schemaErrorMap, Q, Q === Px ? void 0 : Px].filter((G) => !!G)
    });
  A.common.issues.push(I)
}
class uG {
  constructor() {
    this.value = "valid"
  }
  dirty() {
    if (this.value === "valid") this.value = "dirty"
  }
  abort() {
    if (this.value !== "aborted") this.value = "aborted"
  }
  static mergeArray(A, B) {
    let Q = [];
    for (let I of B) {
      if (I.status === "aborted") return t9;
      if (I.status === "dirty") A.dirty();
      Q.push(I.value)
    }
    return {
      status: A.value,
      value: Q
    }
  }
  static async mergeObjectAsync(A, B) {
    let Q = [];
    for (let I of B) {
      let G = await I.key,
        D = await I.value;
      Q.push({
        key: G,
        value: D
      })
    }
    return uG.mergeObjectSync(A, Q)
  }
  static mergeObjectSync(A, B) {
    let Q = {};
    for (let I of B) {
      let {
        key: G,
        value: D
      } = I;
      if (G.status === "aborted") return t9;
      if (D.status === "aborted") return t9;
      if (G.status === "dirty") A.dirty();
      if (D.status === "dirty") A.dirty();
      if (G.value !== "__proto__" && (typeof D.value !== "undefined" || I.alwaysSet)) Q[G.value] = D.value
    }
    return {
      status: A.value,
      value: Q
    }
  }
}
var t9 = Object.freeze({
    status: "aborted"
  }),
  Ox = (A) => ({
    status: "dirty",
    value: A
  }),
  tD = (A) => ({
    status: "valid",
    value: A
  }),
  RM1 = (A) => A.status === "aborted",
  OM1 = (A) => A.status === "dirty",
  kT = (A) => A.status === "valid",
  qc = (A) => typeof Promise !== "undefined" && A instanceof Promise;

function L41(A, B, Q, I) {
  if (Q === "a" && !I) throw new TypeError("Private accessor was defined without a getter");
  if (typeof B === "function" ? A !== B || !I : !B.has(A)) throw new TypeError(
    "Cannot read private member from an object whose class did not declare it");
  return Q === "m" ? I : Q === "a" ? I.call(A) : I ? I.value : B.get(A)
}

function ARA(A, B, Q, I, G) {
  if (I === "m") throw new TypeError("Private method is not writable");
  if (I === "a" && !G) throw new TypeError("Private accessor was defined without a setter");
  if (typeof B === "function" ? A !== B || !G : !B.has(A)) throw new TypeError(
    "Cannot write private member to an object whose class did not declare it");
  return I === "a" ? G.call(A, Q) : G ? G.value = Q : B.set(A, Q), Q
}
var e2;
(function(A) {
  A.errToObj = (B) => typeof B === "string" ? {
    message: B
  } : B || {}, A.toString = (B) => typeof B === "string" ? B : B === null || B === void 0 ? void 0 : B.message
})(e2 || (e2 = {}));
var Nc, $c;
class OX {
  constructor(A, B, Q, I) {
    this._cachedPath = [], this.parent = A, this.data = B, this._path = Q, this._key = I
  }
  get path() {
    if (!this._cachedPath.length)
      if (this._key instanceof Array) this._cachedPath.push(...this._path, ...this._key);
      else this._cachedPath.push(...this._path, this._key);
    return this._cachedPath
  }
}
var rLA = (A, B) => {
  if (kT(B)) return {
    success: !0,
    data: B.value
  };
  else {
    if (!A.common.issues.length) throw new Error("Validation failed but no issues detected.");
    return {
      success: !1,
      get error() {
        if (this._error) return this._error;
        let Q = new kW(A.common.issues);
        return this._error = Q, this._error
      }
    }
  }
};

function y4(A) {
  if (!A) return {};
  let {
    errorMap: B,
    invalid_type_error: Q,
    required_error: I,
    description: G
  } = A;
  if (B && (Q || I)) throw new Error(
    `Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  if (B) return {
    errorMap: B,
    description: G
  };
  return {
    errorMap: (Z, Y) => {
      var W, F;
      let {
        message: J
      } = A;
      if (Z.code === "invalid_enum_value") return {
        message: J !== null && J !== void 0 ? J : Y.defaultError
      };
      if (typeof Y.data === "undefined") return {
        message: (W = J !== null && J !== void 0 ? J : I) !== null && W !== void 0 ? W : Y.defaultError
      };
      if (Z.code !== "invalid_type") return {
        message: Y.defaultError
      };
      return {
        message: (F = J !== null && J !== void 0 ? J : Q) !== null && F !== void 0 ? F : Y.defaultError
      }
    },
    description: G
  }
}
class v4 {
  get description() {
    return this._def.description
  }
  _getType(A) {
    return sE(A.data)
  }
  _getOrReturnCtx(A, B) {
    return B || {
      common: A.parent.common,
      data: A.data,
      parsedType: sE(A.data),
      schemaErrorMap: this._def.errorMap,
      path: A.path,
      parent: A.parent
    }
  }
  _processInputParams(A) {
    return {
      status: new uG,
      ctx: {
        common: A.parent.common,
        data: A.data,
        parsedType: sE(A.data),
        schemaErrorMap: this._def.errorMap,
        path: A.path,
        parent: A.parent
      }
    }
  }
  _parseSync(A) {
    let B = this._parse(A);
    if (qc(B)) throw new Error("Synchronous parse encountered promise.");
    return B
  }
  _parseAsync(A) {
    let B = this._parse(A);
    return Promise.resolve(B)
  }
  parse(A, B) {
    let Q = this.safeParse(A, B);
    if (Q.success) return Q.data;
    throw Q.error
  }
  safeParse(A, B) {
    var Q;
    let I = {
        common: {
          issues: [],
          async: (Q = B === null || B === void 0 ? void 0 : B.async) !== null && Q !== void 0 ? Q : !1,
          contextualErrorMap: B === null || B === void 0 ? void 0 : B.errorMap
        },
        path: (B === null || B === void 0 ? void 0 : B.path) || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: A,
        parsedType: sE(A)
      },
      G = this._parseSync({
        data: A,
        path: I.path,
        parent: I
      });
    return rLA(I, G)
  }
  "~validate"(A) {
    var B, Q;
    let I = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data: A,
      parsedType: sE(A)
    };
    if (!this["~standard"].async) try {
      let G = this._parseSync({
        data: A,
        path: [],
        parent: I
      });
      return kT(G) ? {
        value: G.value
      } : {
        issues: I.common.issues
      }
    } catch (G) {
      if ((Q = (B = G === null || G === void 0 ? void 0 : G.message) === null || B === void 0 ? void 0 : B
          .toLowerCase()) === null || Q === void 0 ? void 0 : Q.includes("encountered")) this["~standard"].async = !0;
      I.common = {
        issues: [],
        async: !0
      }
    }
    return this._parseAsync({
      data: A,
      path: [],
      parent: I
    }).then((G) => kT(G) ? {
      value: G.value
    } : {
      issues: I.common.issues
    })
  }
  async parseAsync(A, B) {
    let Q = await this.safeParseAsync(A, B);
    if (Q.success) return Q.data;
    throw Q.error
  }
  async safeParseAsync(A, B) {
    let Q = {
        common: {
          issues: [],
          contextualErrorMap: B === null || B === void 0 ? void 0 : B.errorMap,
          async: !0
        },
        path: (B === null || B === void 0 ? void 0 : B.path) || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: A,
        parsedType: sE(A)
      },
      I = this._parse({
        data: A,
        path: Q.path,
        parent: Q
      }),
      G = await (qc(I) ? I : Promise.resolve(I));
    return rLA(Q, G)
  }
  refine(A, B) {
    let Q = (I) => {
      if (typeof B === "string" || typeof B === "undefined") return {
        message: B
      };
      else if (typeof B === "function") return B(I);
      else return B
    };
    return this._refinement((I, G) => {
      let D = A(I),
        Z = () => G.addIssue({
          code: N0.custom,
          ...Q(I)
        });
      if (typeof Promise !== "undefined" && D instanceof Promise) return D.then((Y) => {
        if (!Y) return Z(), !1;
        else return !0
      });
      if (!D) return Z(), !1;
      else return !0
    })
  }
  refinement(A, B) {
    return this._refinement((Q, I) => {
      if (!A(Q)) return I.addIssue(typeof B === "function" ? B(Q, I) : B), !1;
      else return !0
    })
  }
  _refinement(A) {
    return new NJ({
      schema: this,
      typeName: M0.ZodEffects,
      effect: {
        type: "refinement",
        refinement: A
      }
    })
  }
  superRefine(A) {
    return this._refinement(A)
  }
  constructor(A) {
    this.spa = this.safeParseAsync, this._def = A, this.parse = this.parse.bind(this), this.safeParse = this.safeParse
      .bind(this), this.parseAsync = this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this),
      this.spa = this.spa.bind(this), this.refine = this.refine.bind(this), this.refinement = this.refinement.bind(
        this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.bind(this), this
      .nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(
      this), this.promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this),
      this.transform = this.transform.bind(this), this.brand = this.brand.bind(this), this.default = this.default
      .bind(this), this.catch = this.catch.bind(this), this.describe = this.describe.bind(this), this.pipe = this.pipe
      .bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.bind(this), this
      .isOptional = this.isOptional.bind(this), this["~standard"] = {
        version: 1,
        vendor: "zod",
        validate: (B) => this["~validate"](B)
      }
  }
  optional() {
    return xW.create(this, this._def)
  }
  nullable() {
    return rE.create(this, this._def)
  }
  nullish() {
    return this.nullable().optional()
  }
  array() {
    return RX.create(this)
  }
  promise() {
    return bT.create(this, this._def)
  }
  or(A) {
    return yx.create([this, A], this._def)
  }
  and(A) {
    return kx.create(this, A, this._def)
  }
  transform(A) {
    return new NJ({
      ...y4(this._def),
      schema: this,
      typeName: M0.ZodEffects,
      effect: {
        type: "transform",
        transform: A
      }
    })
  }
  default (A) {
    let B = typeof A === "function" ? A : () => A;
    return new bx({
      ...y4(this._def),
      innerType: this,
      defaultValue: B,
      typeName: M0.ZodDefault
    })
  }
  brand() {
    return new O41({
      typeName: M0.ZodBranded,
      type: this,
      ...y4(this._def)
    })
  } catch (A) {
    let B = typeof A === "function" ? A : () => A;
    return new gx({
      ...y4(this._def),
      innerType: this,
      catchValue: B,
      typeName: M0.ZodCatch
    })
  }
  describe(A) {
    return new this.constructor({
      ...this._def,
      description: A
    })
  }
  pipe(A) {
    return Pc.create(this, A)
  }
  readonly() {
    return hx.create(this)
  }
  isOptional() {
    return this.safeParse(void 0).success
  }
  isNullable() {
    return this.safeParse(null).success
  }
}
var Ij9 = /^c[^\s-]{8,}$/i,
  Gj9 = /^[0-9a-z]+$/,
  Dj9 = /^[0-9A-HJKMNP-TV-Z]{26}$/i,
  Zj9 = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,
  Yj9 = /^[a-z0-9_-]{21}$/i,
  Wj9 = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,
  Fj9 =
  /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,
  Jj9 = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,
  Cj9 = "^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",
  MM1, Xj9 =
  /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,
  Vj9 =
  /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
  Kj9 =
  /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,
  Hj9 =
  /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
  zj9 = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,
  wj9 = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
  BRA =
  "((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",
  Ej9 = new RegExp(`^${BRA}$`);

function QRA(A) {
  let B = "([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";
  if (A.precision) B = `${B}\\.\\d{${A.precision}}`;
  else if (A.precision == null) B = `${B}(\\.\\d+)?`;
  return B
}

function Uj9(A) {
  return new RegExp(`^${QRA(A)}$`)
}

function IRA(A) {
  let B = `${BRA}T${QRA(A)}`,
    Q = [];
  if (Q.push(A.local ? "Z?" : "Z"), A.offset) Q.push("([+-]\\d{2}:?\\d{2})");
  return B = `${B}(${Q.join("|")})`, new RegExp(`^${B}$`)
}

function Nj9(A, B) {
  if ((B === "v4" || !B) && Xj9.test(A)) return !0;
  if ((B === "v6" || !B) && Kj9.test(A)) return !0;
  return !1
}

function $j9(A, B) {
  if (!Wj9.test(A)) return !1;
  try {
    let [Q] = A.split("."), I = Q.replace(/-/g, "+").replace(/_/g, "/").padEnd(Q.length + (4 - Q.length % 4) % 4, "="),
      G = JSON.parse(atob(I));
    if (typeof G !== "object" || G === null) return !1;
    if (!G.typ || !G.alg) return !1;
    if (B && G.alg !== B) return !1;
    return !0
  } catch (Q) {
    return !1
  }
}

function qj9(A, B) {
  if ((B === "v4" || !B) && Vj9.test(A)) return !0;
  if ((B === "v6" || !B) && Hj9.test(A)) return !0;
  return !1
}
class LX extends v4 {
  _parse(A) {
    if (this._def.coerce) A.data = String(A.data);
    if (this._getType(A) !== q2.string) {
      let G = this._getOrReturnCtx(A);
      return D2(G, {
        code: N0.invalid_type,
        expected: q2.string,
        received: G.parsedType
      }), t9
    }
    let Q = new uG,
      I = void 0;
    for (let G of this._def.checks)
      if (G.kind === "min") {
        if (A.data.length < G.value) I = this._getOrReturnCtx(A, I), D2(I, {
          code: N0.too_small,
          minimum: G.value,
          type: "string",
          inclusive: !0,
          exact: !1,
          message: G.message
        }), Q.dirty()
      } else if (G.kind === "max") {
      if (A.data.length > G.value) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.too_big,
        maximum: G.value,
        type: "string",
        inclusive: !0,
        exact: !1,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "length") {
      let D = A.data.length > G.value,
        Z = A.data.length < G.value;
      if (D || Z) {
        if (I = this._getOrReturnCtx(A, I), D) D2(I, {
          code: N0.too_big,
          maximum: G.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: G.message
        });
        else if (Z) D2(I, {
          code: N0.too_small,
          minimum: G.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: G.message
        });
        Q.dirty()
      }
    } else if (G.kind === "email") {
      if (!Jj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "email",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "emoji") {
      if (!MM1) MM1 = new RegExp(Cj9, "u");
      if (!MM1.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "emoji",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "uuid") {
      if (!Zj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "uuid",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "nanoid") {
      if (!Yj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "nanoid",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "cuid") {
      if (!Ij9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "cuid",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "cuid2") {
      if (!Gj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "cuid2",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "ulid") {
      if (!Dj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "ulid",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "url") try {
      new URL(A.data)
    } catch (D) {
      I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "url",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "regex") {
      if (G.regex.lastIndex = 0, !G.regex.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "regex",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "trim") A.data = A.data.trim();
    else if (G.kind === "includes") {
      if (!A.data.includes(G.value, G.position)) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.invalid_string,
        validation: {
          includes: G.value,
          position: G.position
        },
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "toLowerCase") A.data = A.data.toLowerCase();
    else if (G.kind === "toUpperCase") A.data = A.data.toUpperCase();
    else if (G.kind === "startsWith") {
      if (!A.data.startsWith(G.value)) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.invalid_string,
        validation: {
          startsWith: G.value
        },
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "endsWith") {
      if (!A.data.endsWith(G.value)) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.invalid_string,
        validation: {
          endsWith: G.value
        },
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "datetime") {
      if (!IRA(G).test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.invalid_string,
        validation: "datetime",
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "date") {
      if (!Ej9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.invalid_string,
        validation: "date",
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "time") {
      if (!Uj9(G).test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.invalid_string,
        validation: "time",
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "duration") {
      if (!Fj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "duration",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "ip") {
      if (!Nj9(A.data, G.version)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "ip",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "jwt") {
      if (!$j9(A.data, G.alg)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "jwt",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "cidr") {
      if (!qj9(A.data, G.version)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "cidr",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "base64") {
      if (!zj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "base64",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else if (G.kind === "base64url") {
      if (!wj9.test(A.data)) I = this._getOrReturnCtx(A, I), D2(I, {
        validation: "base64url",
        code: N0.invalid_string,
        message: G.message
      }), Q.dirty()
    } else p6.assertNever(G);
    return {
      status: Q.value,
      value: A.data
    }
  }
  _regex(A, B, Q) {
    return this.refinement((I) => A.test(I), {
      validation: B,
      code: N0.invalid_string,
      ...e2.errToObj(Q)
    })
  }
  _addCheck(A) {
    return new LX({
      ...this._def,
      checks: [...this._def.checks, A]
    })
  }
  email(A) {
    return this._addCheck({
      kind: "email",
      ...e2.errToObj(A)
    })
  }
  url(A) {
    return this._addCheck({
      kind: "url",
      ...e2.errToObj(A)
    })
  }
  emoji(A) {
    return this._addCheck({
      kind: "emoji",
      ...e2.errToObj(A)
    })
  }
  uuid(A) {
    return this._addCheck({
      kind: "uuid",
      ...e2.errToObj(A)
    })
  }
  nanoid(A) {
    return this._addCheck({
      kind: "nanoid",
      ...e2.errToObj(A)
    })
  }
  cuid(A) {
    return this._addCheck({
      kind: "cuid",
      ...e2.errToObj(A)
    })
  }
  cuid2(A) {
    return this._addCheck({
      kind: "cuid2",
      ...e2.errToObj(A)
    })
  }
  ulid(A) {
    return this._addCheck({
      kind: "ulid",
      ...e2.errToObj(A)
    })
  }
  base64(A) {
    return this._addCheck({
      kind: "base64",
      ...e2.errToObj(A)
    })
  }
  base64url(A) {
    return this._addCheck({
      kind: "base64url",
      ...e2.errToObj(A)
    })
  }
  jwt(A) {
    return this._addCheck({
      kind: "jwt",
      ...e2.errToObj(A)
    })
  }
  ip(A) {
    return this._addCheck({
      kind: "ip",
      ...e2.errToObj(A)
    })
  }
  cidr(A) {
    return this._addCheck({
      kind: "cidr",
      ...e2.errToObj(A)
    })
  }
  datetime(A) {
    var B, Q;
    if (typeof A === "string") return this._addCheck({
      kind: "datetime",
      precision: null,
      offset: !1,
      local: !1,
      message: A
    });
    return this._addCheck({
      kind: "datetime",
      precision: typeof(A === null || A === void 0 ? void 0 : A.precision) === "undefined" ? null : A === null ||
        A === void 0 ? void 0 : A.precision,
      offset: (B = A === null || A === void 0 ? void 0 : A.offset) !== null && B !== void 0 ? B : !1,
      local: (Q = A === null || A === void 0 ? void 0 : A.local) !== null && Q !== void 0 ? Q : !1,
      ...e2.errToObj(A === null || A === void 0 ? void 0 : A.message)
    })
  }
  date(A) {
    return this._addCheck({
      kind: "date",
      message: A
    })
  }
  time(A) {
    if (typeof A === "string") return this._addCheck({
      kind: "time",
      precision: null,
      message: A
    });
    return this._addCheck({
      kind: "time",
      precision: typeof(A === null || A === void 0 ? void 0 : A.precision) === "undefined" ? null : A === null ||
        A === void 0 ? void 0 : A.precision,
      ...e2.errToObj(A === null || A === void 0 ? void 0 : A.message)
    })
  }
  duration(A) {
    return this._addCheck({
      kind: "duration",
      ...e2.errToObj(A)
    })
  }
  regex(A, B) {
    return this._addCheck({
      kind: "regex",
      regex: A,
      ...e2.errToObj(B)
    })
  }
  includes(A, B) {
    return this._addCheck({
      kind: "includes",
      value: A,
      position: B === null || B === void 0 ? void 0 : B.position,
      ...e2.errToObj(B === null || B === void 0 ? void 0 : B.message)
    })
  }
  startsWith(A, B) {
    return this._addCheck({
      kind: "startsWith",
      value: A,
      ...e2.errToObj(B)
    })
  }
  endsWith(A, B) {
    return this._addCheck({
      kind: "endsWith",
      value: A,
      ...e2.errToObj(B)
    })
  }
  min(A, B) {
    return this._addCheck({
      kind: "min",
      value: A,
      ...e2.errToObj(B)
    })
  }
  max(A, B) {
    return this._addCheck({
      kind: "max",
      value: A,
      ...e2.errToObj(B)
    })
  }
  length(A, B) {
    return this._addCheck({
      kind: "length",
      value: A,
      ...e2.errToObj(B)
    })
  }
  nonempty(A) {
    return this.min(1, e2.errToObj(A))
  }
  trim() {
    return new LX({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "trim"
      }]
    })
  }
  toLowerCase() {
    return new LX({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "toLowerCase"
      }]
    })
  }
  toUpperCase() {
    return new LX({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "toUpperCase"
      }]
    })
  }
  get isDatetime() {
    return !!this._def.checks.find((A) => A.kind === "datetime")
  }
  get isDate() {
    return !!this._def.checks.find((A) => A.kind === "date")
  }
  get isTime() {
    return !!this._def.checks.find((A) => A.kind === "time")
  }
  get isDuration() {
    return !!this._def.checks.find((A) => A.kind === "duration")
  }
  get isEmail() {
    return !!this._def.checks.find((A) => A.kind === "email")
  }
  get isURL() {
    return !!this._def.checks.find((A) => A.kind === "url")
  }
  get isEmoji() {
    return !!this._def.checks.find((A) => A.kind === "emoji")
  }
  get isUUID() {
    return !!this._def.checks.find((A) => A.kind === "uuid")
  }
  get isNANOID() {
    return !!this._def.checks.find((A) => A.kind === "nanoid")
  }
  get isCUID() {
    return !!this._def.checks.find((A) => A.kind === "cuid")
  }
  get isCUID2() {
    return !!this._def.checks.find((A) => A.kind === "cuid2")
  }
  get isULID() {
    return !!this._def.checks.find((A) => A.kind === "ulid")
  }
  get isIP() {
    return !!this._def.checks.find((A) => A.kind === "ip")
  }
  get isCIDR() {
    return !!this._def.checks.find((A) => A.kind === "cidr")
  }
  get isBase64() {
    return !!this._def.checks.find((A) => A.kind === "base64")
  }
  get isBase64url() {
    return !!this._def.checks.find((A) => A.kind === "base64url")
  }
  get minLength() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "min") {
        if (A === null || B.value > A) A = B.value
      } return A
  }
  get maxLength() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "max") {
        if (A === null || B.value < A) A = B.value
      } return A
  }
}
LX.create = (A) => {
  var B;
  return new LX({
    checks: [],
    typeName: M0.ZodString,
    coerce: (B = A === null || A === void 0 ? void 0 : A.coerce) !== null && B !== void 0 ? B : !1,
    ...y4(A)
  })
};

function Mj9(A, B) {
  let Q = (A.toString().split(".")[1] || "").length,
    I = (B.toString().split(".")[1] || "").length,
    G = Q > I ? Q : I,
    D = parseInt(A.toFixed(G).replace(".", "")),
    Z = parseInt(B.toFixed(G).replace(".", ""));
  return D % Z / Math.pow(10, G)
}
class yq extends v4 {
  constructor() {
    super(...arguments);
    this.min = this.gte, this.max = this.lte, this.step = this.multipleOf
  }
  _parse(A) {
    if (this._def.coerce) A.data = Number(A.data);
    if (this._getType(A) !== q2.number) {
      let G = this._getOrReturnCtx(A);
      return D2(G, {
        code: N0.invalid_type,
        expected: q2.number,
        received: G.parsedType
      }), t9
    }
    let Q = void 0,
      I = new uG;
    for (let G of this._def.checks)
      if (G.kind === "int") {
        if (!p6.isInteger(A.data)) Q = this._getOrReturnCtx(A, Q), D2(Q, {
          code: N0.invalid_type,
          expected: "integer",
          received: "float",
          message: G.message
        }), I.dirty()
      } else if (G.kind === "min") {
      if (G.inclusive ? A.data < G.value : A.data <= G.value) Q = this._getOrReturnCtx(A, Q), D2(Q, {
        code: N0.too_small,
        minimum: G.value,
        type: "number",
        inclusive: G.inclusive,
        exact: !1,
        message: G.message
      }), I.dirty()
    } else if (G.kind === "max") {
      if (G.inclusive ? A.data > G.value : A.data >= G.value) Q = this._getOrReturnCtx(A, Q), D2(Q, {
        code: N0.too_big,
        maximum: G.value,
        type: "number",
        inclusive: G.inclusive,
        exact: !1,
        message: G.message
      }), I.dirty()
    } else if (G.kind === "multipleOf") {
      if (Mj9(A.data, G.value) !== 0) Q = this._getOrReturnCtx(A, Q), D2(Q, {
        code: N0.not_multiple_of,
        multipleOf: G.value,
        message: G.message
      }), I.dirty()
    } else if (G.kind === "finite") {
      if (!Number.isFinite(A.data)) Q = this._getOrReturnCtx(A, Q), D2(Q, {
        code: N0.not_finite,
        message: G.message
      }), I.dirty()
    } else p6.assertNever(G);
    return {
      status: I.value,
      value: A.data
    }
  }
  gte(A, B) {
    return this.setLimit("min", A, !0, e2.toString(B))
  }
  gt(A, B) {
    return this.setLimit("min", A, !1, e2.toString(B))
  }
  lte(A, B) {
    return this.setLimit("max", A, !0, e2.toString(B))
  }
  lt(A, B) {
    return this.setLimit("max", A, !1, e2.toString(B))
  }
  setLimit(A, B, Q, I) {
    return new yq({
      ...this._def,
      checks: [...this._def.checks, {
        kind: A,
        value: B,
        inclusive: Q,
        message: e2.toString(I)
      }]
    })
  }
  _addCheck(A) {
    return new yq({
      ...this._def,
      checks: [...this._def.checks, A]
    })
  }
  int(A) {
    return this._addCheck({
      kind: "int",
      message: e2.toString(A)
    })
  }
  positive(A) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !1,
      message: e2.toString(A)
    })
  }
  negative(A) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !1,
      message: e2.toString(A)
    })
  }
  nonpositive(A) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !0,
      message: e2.toString(A)
    })
  }
  nonnegative(A) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !0,
      message: e2.toString(A)
    })
  }
  multipleOf(A, B) {
    return this._addCheck({
      kind: "multipleOf",
      value: A,
      message: e2.toString(B)
    })
  }
  finite(A) {
    return this._addCheck({
      kind: "finite",
      message: e2.toString(A)
    })
  }
  safe(A) {
    return this._addCheck({
      kind: "min",
      inclusive: !0,
      value: Number.MIN_SAFE_INTEGER,
      message: e2.toString(A)
    })._addCheck({
      kind: "max",
      inclusive: !0,
      value: Number.MAX_SAFE_INTEGER,
      message: e2.toString(A)
    })
  }
  get minValue() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "min") {
        if (A === null || B.value > A) A = B.value
      } return A
  }
  get maxValue() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "max") {
        if (A === null || B.value < A) A = B.value
      } return A
  }
  get isInt() {
    return !!this._def.checks.find((A) => A.kind === "int" || A.kind === "multipleOf" && p6.isInteger(A.value))
  }
  get isFinite() {
    let A = null,
      B = null;
    for (let Q of this._def.checks)
      if (Q.kind === "finite" || Q.kind === "int" || Q.kind === "multipleOf") return !0;
      else if (Q.kind === "min") {
      if (B === null || Q.value > B) B = Q.value
    } else if (Q.kind === "max") {
      if (A === null || Q.value < A) A = Q.value
    }
    return Number.isFinite(B) && Number.isFinite(A)
  }
}
yq.create = (A) => {
  return new yq({
    checks: [],
    typeName: M0.ZodNumber,
    coerce: (A === null || A === void 0 ? void 0 : A.coerce) || !1,
    ...y4(A)
  })
};
class kq extends v4 {
  constructor() {
    super(...arguments);
    this.min = this.gte, this.max = this.lte
  }
  _parse(A) {
    if (this._def.coerce) try {
      A.data = BigInt(A.data)
    } catch (G) {
      return this._getInvalidInput(A)
    }
    if (this._getType(A) !== q2.bigint) return this._getInvalidInput(A);
    let Q = void 0,
      I = new uG;
    for (let G of this._def.checks)
      if (G.kind === "min") {
        if (G.inclusive ? A.data < G.value : A.data <= G.value) Q = this._getOrReturnCtx(A, Q), D2(Q, {
          code: N0.too_small,
          type: "bigint",
          minimum: G.value,
          inclusive: G.inclusive,
          message: G.message
        }), I.dirty()
      } else if (G.kind === "max") {
      if (G.inclusive ? A.data > G.value : A.data >= G.value) Q = this._getOrReturnCtx(A, Q), D2(Q, {
        code: N0.too_big,
        type: "bigint",
        maximum: G.value,
        inclusive: G.inclusive,
        message: G.message
      }), I.dirty()
    } else if (G.kind === "multipleOf") {
      if (A.data % G.value !== BigInt(0)) Q = this._getOrReturnCtx(A, Q), D2(Q, {
        code: N0.not_multiple_of,
        multipleOf: G.value,
        message: G.message
      }), I.dirty()
    } else p6.assertNever(G);
    return {
      status: I.value,
      value: A.data
    }
  }
  _getInvalidInput(A) {
    let B = this._getOrReturnCtx(A);
    return D2(B, {
      code: N0.invalid_type,
      expected: q2.bigint,
      received: B.parsedType
    }), t9
  }
  gte(A, B) {
    return this.setLimit("min", A, !0, e2.toString(B))
  }
  gt(A, B) {
    return this.setLimit("min", A, !1, e2.toString(B))
  }
  lte(A, B) {
    return this.setLimit("max", A, !0, e2.toString(B))
  }
  lt(A, B) {
    return this.setLimit("max", A, !1, e2.toString(B))
  }
  setLimit(A, B, Q, I) {
    return new kq({
      ...this._def,
      checks: [...this._def.checks, {
        kind: A,
        value: B,
        inclusive: Q,
        message: e2.toString(I)
      }]
    })
  }
  _addCheck(A) {
    return new kq({
      ...this._def,
      checks: [...this._def.checks, A]
    })
  }
  positive(A) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !1,
      message: e2.toString(A)
    })
  }
  negative(A) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !1,
      message: e2.toString(A)
    })
  }
  nonpositive(A) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !0,
      message: e2.toString(A)
    })
  }
  nonnegative(A) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !0,
      message: e2.toString(A)
    })
  }
  multipleOf(A, B) {
    return this._addCheck({
      kind: "multipleOf",
      value: A,
      message: e2.toString(B)
    })
  }
  get minValue() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "min") {
        if (A === null || B.value > A) A = B.value
      } return A
  }
  get maxValue() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "max") {
        if (A === null || B.value < A) A = B.value
      } return A
  }
}
kq.create = (A) => {
  var B;
  return new kq({
    checks: [],
    typeName: M0.ZodBigInt,
    coerce: (B = A === null || A === void 0 ? void 0 : A.coerce) !== null && B !== void 0 ? B : !1,
    ...y4(A)
  })
};
class Sx extends v4 {
  _parse(A) {
    if (this._def.coerce) A.data = Boolean(A.data);
    if (this._getType(A) !== q2.boolean) {
      let Q = this._getOrReturnCtx(A);
      return D2(Q, {
        code: N0.invalid_type,
        expected: q2.boolean,
        received: Q.parsedType
      }), t9
    }
    return tD(A.data)
  }
}
Sx.create = (A) => {
  return new Sx({
    typeName: M0.ZodBoolean,
    coerce: (A === null || A === void 0 ? void 0 : A.coerce) || !1,
    ...y4(A)
  })
};
class xT extends v4 {
  _parse(A) {
    if (this._def.coerce) A.data = new Date(A.data);
    if (this._getType(A) !== q2.date) {
      let G = this._getOrReturnCtx(A);
      return D2(G, {
        code: N0.invalid_type,
        expected: q2.date,
        received: G.parsedType
      }), t9
    }
    if (isNaN(A.data.getTime())) {
      let G = this._getOrReturnCtx(A);
      return D2(G, {
        code: N0.invalid_date
      }), t9
    }
    let Q = new uG,
      I = void 0;
    for (let G of this._def.checks)
      if (G.kind === "min") {
        if (A.data.getTime() < G.value) I = this._getOrReturnCtx(A, I), D2(I, {
          code: N0.too_small,
          message: G.message,
          inclusive: !0,
          exact: !1,
          minimum: G.value,
          type: "date"
        }), Q.dirty()
      } else if (G.kind === "max") {
      if (A.data.getTime() > G.value) I = this._getOrReturnCtx(A, I), D2(I, {
        code: N0.too_big,
        message: G.message,
        inclusive: !0,
        exact: !1,
        maximum: G.value,
        type: "date"
      }), Q.dirty()
    } else p6.assertNever(G);
    return {
      status: Q.value,
      value: new Date(A.data.getTime())
    }
  }
  _addCheck(A) {
    return new xT({
      ...this._def,
      checks: [...this._def.checks, A]
    })
  }
  min(A, B) {
    return this._addCheck({
      kind: "min",
      value: A.getTime(),
      message: e2.toString(B)
    })
  }
  max(A, B) {
    return this._addCheck({
      kind: "max",
      value: A.getTime(),
      message: e2.toString(B)
    })
  }
  get minDate() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "min") {
        if (A === null || B.value > A) A = B.value
      } return A != null ? new Date(A) : null
  }
  get maxDate() {
    let A = null;
    for (let B of this._def.checks)
      if (B.kind === "max") {
        if (A === null || B.value < A) A = B.value
      } return A != null ? new Date(A) : null
  }
}
xT.create = (A) => {
  return new xT({
    checks: [],
    coerce: (A === null || A === void 0 ? void 0 : A.coerce) || !1,
    typeName: M0.ZodDate,
    ...y4(A)
  })
};
class Mc extends v4 {
  _parse(A) {
    if (this._getType(A) !== q2.symbol) {
      let Q = this._getOrReturnCtx(A);
      return D2(Q, {
        code: N0.invalid_type,
        expected: q2.symbol,
        received: Q.parsedType
      }), t9
    }
    return tD(A.data)
  }
}
Mc.create = (A) => {
  return new Mc({
    typeName: M0.ZodSymbol,
    ...y4(A)
  })
};
class _x extends v4 {
  _parse(A) {
    if (this._getType(A) !== q2.undefined) {
      let Q = this._getOrReturnCtx(A);
      return D2(Q, {
        code: N0.invalid_type,
        expected: q2.undefined,
        received: Q.parsedType
      }), t9
    }
    return tD(A.data)
  }
}
_x.create = (A) => {
  return new _x({
    typeName: M0.ZodUndefined,
    ...y4(A)
  })
};
class jx extends v4 {
  _parse(A) {
    if (this._getType(A) !== q2.null) {
      let Q = this._getOrReturnCtx(A);
      return D2(Q, {
        code: N0.invalid_type,
        expected: q2.null,
        received: Q.parsedType
      }), t9
    }
    return tD(A.data)
  }
}
jx.create = (A) => {
  return new jx({
    typeName: M0.ZodNull,
    ...y4(A)
  })
};
class fT extends v4 {
  constructor() {
    super(...arguments);
    this._any = !0
  }
  _parse(A) {
    return tD(A.data)
  }
}
fT.create = (A) => {
  return new fT({
    typeName: M0.ZodAny,
    ...y4(A)
  })
};
class jq extends v4 {
  constructor() {
    super(...arguments);
    this._unknown = !0
  }
  _parse(A) {
    return tD(A.data)
  }
}
jq.create = (A) => {
  return new jq({
    typeName: M0.ZodUnknown,
    ...y4(A)
  })
};
class TH extends v4 {
  _parse(A) {
    let B = this._getOrReturnCtx(A);
    return D2(B, {
      code: N0.invalid_type,
      expected: q2.never,
      received: B.parsedType
    }), t9
  }
}
TH.create = (A) => {
  return new TH({
    typeName: M0.ZodNever,
    ...y4(A)
  })
};
class Lc extends v4 {
  _parse(A) {
    if (this._getType(A) !== q2.undefined) {
      let Q = this._getOrReturnCtx(A);
      return D2(Q, {
        code: N0.invalid_type,
        expected: q2.void,
        received: Q.parsedType
      }), t9
    }
    return tD(A.data)
  }
}
Lc.create = (A) => {
  return new Lc({
    typeName: M0.ZodVoid,
    ...y4(A)
  })
};
class RX extends v4 {
  _parse(A) {
    let {
      ctx: B,
      status: Q
    } = this._processInputParams(A), I = this._def;
    if (B.parsedType !== q2.array) return D2(B, {
      code: N0.invalid_type,
      expected: q2.array,
      received: B.parsedType
    }), t9;
    if (I.exactLength !== null) {
      let D = B.data.length > I.exactLength.value,
        Z = B.data.length < I.exactLength.value;
      if (D || Z) D2(B, {
        code: D ? N0.too_big : N0.too_small,
        minimum: Z ? I.exactLength.value : void 0,
        maximum: D ? I.exactLength.value : void 0,
        type: "array",
        inclusive: !0,
        exact: !0,
        message: I.exactLength.message
      }), Q.dirty()
    }
    if (I.minLength !== null) {
      if (B.data.length < I.minLength.value) D2(B, {
        code: N0.too_small,
        minimum: I.minLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: I.minLength.message
      }), Q.dirty()
    }
    if (I.maxLength !== null) {
      if (B.data.length > I.maxLength.value) D2(B, {
        code: N0.too_big,
        maximum: I.maxLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: I.maxLength.message
      }), Q.dirty()
    }
    if (B.common.async) return Promise.all([...B.data].map((D, Z) => {
      return I.type._parseAsync(new OX(B, D, B.path, Z))
    })).then((D) => {
      return uG.mergeArray(Q, D)
    });
    let G = [...B.data].map((D, Z) => {
      return I.type._parseSync(new OX(B, D, B.path, Z))
    });
    return uG.mergeArray(Q, G)
  }
  get element() {
    return this._def.type
  }
  min(A, B) {
    return new RX({
      ...this._def,
      minLength: {
        value: A,
        message: e2.toString(B)
      }
    })
  }
  max(A, B) {
    return new RX({
      ...this._def,
      maxLength: {
        value: A,
        message: e2.toString(B)
      }
    })
  }
  length(A, B) {
    return new RX({
      ...this._def,
      exactLength: {
        value: A,
        message: e2.toString(B)
      }
    })
  }
  nonempty(A) {
    return this.min(1, A)
  }
}
RX.create = (A, B) => {
  return new RX({
    type: A,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: M0.ZodArray,
    ...y4(B)
  })
};

function Rx(A) {
  if (A instanceof eB) {
    let B = {};
    for (let Q in A.shape) {
      let I = A.shape[Q];
      B[Q] = xW.create(Rx(I))
    }
    return new eB({
      ...A._def,
      shape: () => B
    })
  } else if (A instanceof RX) return new RX({
    ...A._def,
    type: Rx(A.element)
  });
  else if (A instanceof xW) return xW.create(Rx(A.unwrap()));
  else if (A instanceof rE) return rE.create(Rx(A.unwrap()));
  else if (A instanceof PH) return PH.create(A.items.map((B) => Rx(B)));
  else return A
}
class eB extends v4 {
  constructor() {
    super(...arguments);
    this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend
  }
  _getCached() {
    if (this._cached !== null) return this._cached;
    let A = this._def.shape(),
      B = p6.objectKeys(A);
    return this._cached = {
      shape: A,
      keys: B
    }
  }
  _parse(A) {
    if (this._getType(A) !== q2.object) {
      let W = this._getOrReturnCtx(A);
      return D2(W, {
        code: N0.invalid_type,
        expected: q2.object,
        received: W.parsedType
      }), t9
    }
    let {
      status: Q,
      ctx: I
    } = this._processInputParams(A), {
      shape: G,
      keys: D
    } = this._getCached(), Z = [];
    if (!(this._def.catchall instanceof TH && this._def.unknownKeys === "strip")) {
      for (let W in I.data)
        if (!D.includes(W)) Z.push(W)
    }
    let Y = [];
    for (let W of D) {
      let F = G[W],
        J = I.data[W];
      Y.push({
        key: {
          status: "valid",
          value: W