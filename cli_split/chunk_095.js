// Chunk 95
// Lines 285001-288000
// Size: 83118 bytes

        if (J > C || Date.now() > V) return D();
        if (!M()) T()
      }, 0)
    })();
    else
      while (J <= C && Date.now() <= V) {
        var R = M();
        if (R) return R
      }
  },
  addToPath: function A(B, Q, I, G, D) {
    var Z = B.lastComponent;
    if (Z && !D.oneChangePerToken && Z.added === Q && Z.removed === I) return {
      oldPos: B.oldPos + G,
      lastComponent: {
        count: Z.count + 1,
        added: Q,
        removed: I,
        previousComponent: Z.previousComponent
      }
    };
    else return {
      oldPos: B.oldPos + G,
      lastComponent: {
        count: 1,
        added: Q,
        removed: I,
        previousComponent: Z
      }
    }
  },
  extractCommon: function A(B, Q, I, G, D) {
    var Z = Q.length,
      Y = I.length,
      W = B.oldPos,
      F = W - G,
      J = 0;
    while (F + 1 < Z && W + 1 < Y && this.equals(I[W + 1], Q[F + 1], D))
      if (F++, W++, J++, D.oneChangePerToken) B.lastComponent = {
        count: 1,
        previousComponent: B.lastComponent,
        added: !1,
        removed: !1
      };
    if (J && !D.oneChangePerToken) B.lastComponent = {
      count: J,
      previousComponent: B.lastComponent,
      added: !1,
      removed: !1
    };
    return B.oldPos = W, F
  },
  equals: function A(B, Q, I) {
    if (I.comparator) return I.comparator(B, Q);
    else return B === Q || I.ignoreCase && B.toLowerCase() === Q.toLowerCase()
  },
  removeEmpty: function A(B) {
    var Q = [];
    for (var I = 0; I < B.length; I++)
      if (B[I]) Q.push(B[I]);
    return Q
  },
  castInput: function A(B) {
    return B
  },
  tokenize: function A(B) {
    return Array.from(B)
  },
  join: function A(B) {
    return B.join("")
  },
  postProcess: function A(B) {
    return B
  }
};

function SW2(A, B, Q, I, G) {
  var D = [],
    Z;
  while (B) D.push(B), Z = B.previousComponent, delete B.previousComponent, B = Z;
  D.reverse();
  var Y = 0,
    W = D.length,
    F = 0,
    J = 0;
  for (; Y < W; Y++) {
    var C = D[Y];
    if (!C.removed) {
      if (!C.added && G) {
        var X = Q.slice(F, F + C.count);
        X = X.map(function(V, K) {
          var U = I[J + K];
          return U.length > V.length ? U : V
        }), C.value = A.join(X)
      } else C.value = A.join(Q.slice(F, F + C.count));
      if (F += C.count, !C.added) J += C.count
    } else C.value = A.join(I.slice(J, J + C.count)), J += C.count
  }
  return D
}
var Jj8 = new sV;

function _W2(A, B) {
  var Q;
  for (Q = 0; Q < A.length && Q < B.length; Q++)
    if (A[Q] != B[Q]) return A.slice(0, Q);
  return A.slice(0, Q)
}

function jW2(A, B) {
  var Q;
  if (!A || !B || A[A.length - 1] != B[B.length - 1]) return "";
  for (Q = 0; Q < A.length && Q < B.length; Q++)
    if (A[A.length - (Q + 1)] != B[B.length - (Q + 1)]) return A.slice(-Q);
  return A.slice(-Q)
}

function sa1(A, B, Q) {
  if (A.slice(0, B.length) != B) throw Error("string ".concat(JSON.stringify(A), " doesn't start with prefix ").concat(
    JSON.stringify(B), "; this is a bug"));
  return Q + A.slice(B.length)
}

function ra1(A, B, Q) {
  if (!B) return A + Q;
  if (A.slice(-B.length) != B) throw Error("string ".concat(JSON.stringify(A), " doesn't end with suffix ").concat(JSON
    .stringify(B), "; this is a bug"));
  return A.slice(0, -B.length) + Q
}

function Nt(A, B) {
  return sa1(A, B, "")
}

function LX1(A, B) {
  return ra1(A, B, "")
}

function yW2(A, B) {
  return B.slice(0, nt6(A, B))
}

function nt6(A, B) {
  var Q = 0;
  if (A.length > B.length) Q = A.length - B.length;
  var I = B.length;
  if (A.length < B.length) I = A.length;
  var G = Array(I),
    D = 0;
  G[0] = 0;
  for (var Z = 1; Z < I; Z++) {
    if (B[Z] == B[D]) G[Z] = G[D];
    else G[Z] = D;
    while (D > 0 && B[Z] != B[D]) D = G[D];
    if (B[Z] == B[D]) D++
  }
  D = 0;
  for (var Y = Q; Y < A.length; Y++) {
    while (D > 0 && A[Y] != B[D]) D = G[D];
    if (A[Y] == B[D]) D++
  }
  return D
}
var RX1 =
  "a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",
  at6 = new RegExp("[".concat(RX1, "]+|\\s+|[^").concat(RX1, "]"), "ug"),
  OX1 = new sV;
OX1.equals = function(A, B, Q) {
  if (Q.ignoreCase) A = A.toLowerCase(), B = B.toLowerCase();
  return A.trim() === B.trim()
};
OX1.tokenize = function(A) {
  var B = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {},
    Q;
  if (B.intlSegmenter) {
    if (B.intlSegmenter.resolvedOptions().granularity != "word") throw new Error(
      'The segmenter passed must have a granularity of "word"');
    Q = Array.from(B.intlSegmenter.segment(A), function(D) {
      return D.segment
    })
  } else Q = A.match(at6) || [];
  var I = [],
    G = null;
  return Q.forEach(function(D) {
    if (/\s/.test(D))
      if (G == null) I.push(D);
      else I.push(I.pop() + D);
    else if (/\s/.test(G))
      if (I[I.length - 1] == G) I.push(I.pop() + D);
      else I.push(G + D);
    else I.push(D);
    G = D
  }), I
};
OX1.join = function(A) {
  return A.map(function(B, Q) {
    if (Q == 0) return B;
    else return B.replace(/^\s+/, "")
  }).join("")
};
OX1.postProcess = function(A, B) {
  if (!A || B.oneChangePerToken) return A;
  var Q = null,
    I = null,
    G = null;
  if (A.forEach(function(D) {
      if (D.added) I = D;
      else if (D.removed) G = D;
      else {
        if (I || G) kW2(Q, G, I, D);
        Q = D, I = null, G = null
      }
    }), I || G) kW2(Q, G, I, null);
  return A
};

function kW2(A, B, Q, I) {
  if (B && Q) {
    var G = B.value.match(/^\s*/)[0],
      D = B.value.match(/\s*$/)[0],
      Z = Q.value.match(/^\s*/)[0],
      Y = Q.value.match(/\s*$/)[0];
    if (A) {
      var W = _W2(G, Z);
      A.value = ra1(A.value, Z, W), B.value = Nt(B.value, W), Q.value = Nt(Q.value, W)
    }
    if (I) {
      var F = jW2(D, Y);
      I.value = sa1(I.value, Y, F), B.value = LX1(B.value, F), Q.value = LX1(Q.value, F)
    }
  } else if (Q) {
    if (A) Q.value = Q.value.replace(/^\s*/, "");
    if (I) I.value = I.value.replace(/^\s*/, "")
  } else if (A && I) {
    var J = I.value.match(/^\s*/)[0],
      C = B.value.match(/^\s*/)[0],
      X = B.value.match(/\s*$/)[0],
      V = _W2(J, C);
    B.value = Nt(B.value, V);
    var K = jW2(Nt(J, V), X);
    B.value = LX1(B.value, K), I.value = sa1(I.value, J, K), A.value = ra1(A.value, J, J.slice(0, J.length - K.length))
  } else if (I) {
    var U = I.value.match(/^\s*/)[0],
      N = B.value.match(/\s*$/)[0],
      q = yW2(N, U);
    B.value = LX1(B.value, q)
  } else if (A) {
    var M = A.value.match(/\s*$/)[0],
      R = B.value.match(/^\s*/)[0],
      T = yW2(M, R);
    B.value = Nt(B.value, T)
  }
}
var bW2 = new sV;
bW2.tokenize = function(A) {
  var B = new RegExp("(\\r?\\n)|[".concat(RX1, "]+|[^\\S\\n\\r]+|[^").concat(RX1, "]"), "ug");
  return A.match(B) || []
};

function gW2(A, B, Q) {
  return bW2.diff(A, B, Q)
}
var TX1 = new sV;
TX1.tokenize = function(A, B) {
  if (B.stripTrailingCr) A = A.replace(/\r\n/g, `
`);
  var Q = [],
    I = A.split(/(\n|\r\n)/);
  if (!I[I.length - 1]) I.pop();
  for (var G = 0; G < I.length; G++) {
    var D = I[G];
    if (G % 2 && !B.newlineIsToken) Q[Q.length - 1] += D;
    else Q.push(D)
  }
  return Q
};
TX1.equals = function(A, B, Q) {
  if (Q.ignoreWhitespace) {
    if (!Q.newlineIsToken || !A.includes(`
`)) A = A.trim();
    if (!Q.newlineIsToken || !B.includes(`
`)) B = B.trim()
  } else if (Q.ignoreNewlineAtEof && !Q.newlineIsToken) {
    if (A.endsWith(`
`)) A = A.slice(0, -1);
    if (B.endsWith(`
`)) B = B.slice(0, -1)
  }
  return sV.prototype.equals.call(this, A, B, Q)
};

function xW2(A, B, Q) {
  return TX1.diff(A, B, Q)
}
var st6 = new sV;
st6.tokenize = function(A) {
  return A.split(/(\S.+?[.!?])(?=\s+|$)/)
};
var rt6 = new sV;
rt6.tokenize = function(A) {
  return A.split(/([{}:;,]|\s+)/)
};

function fW2(A, B) {
  var Q = Object.keys(A);
  if (Object.getOwnPropertySymbols) {
    var I = Object.getOwnPropertySymbols(A);
    B && (I = I.filter(function(G) {
      return Object.getOwnPropertyDescriptor(A, G).enumerable
    })), Q.push.apply(Q, I)
  }
  return Q
}

function vW2(A) {
  for (var B = 1; B < arguments.length; B++) {
    var Q = arguments[B] != null ? arguments[B] : {};
    B % 2 ? fW2(Object(Q), !0).forEach(function(I) {
      et6(A, I, Q[I])
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(A, Object.getOwnPropertyDescriptors(Q)) : fW2(
      Object(Q)).forEach(function(I) {
      Object.defineProperty(A, I, Object.getOwnPropertyDescriptor(Q, I))
    })
  }
  return A
}

function ot6(A, B) {
  if (typeof A != "object" || !A) return A;
  var Q = A[Symbol.toPrimitive];
  if (Q !== void 0) {
    var I = Q.call(A, B || "default");
    if (typeof I != "object") return I;
    throw new TypeError("@@toPrimitive must return a primitive value.")
  }
  return (B === "string" ? String : Number)(A)
}

function tt6(A) {
  var B = ot6(A, "string");
  return typeof B == "symbol" ? B : B + ""
}

function oa1(A) {
  return oa1 = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(B) {
    return typeof B
  } : function(B) {
    return B && typeof Symbol == "function" && B.constructor === Symbol && B !== Symbol.prototype ? "symbol" :
      typeof B
  }, oa1(A)
}

function et6(A, B, Q) {
  if (B = tt6(B), B in A) Object.defineProperty(A, B, {
    value: Q,
    enumerable: !0,
    configurable: !0,
    writable: !0
  });
  else A[B] = Q;
  return A
}

function aa1(A) {
  return Ae6(A) || Be6(A) || Qe6(A) || Ie6()
}

function Ae6(A) {
  if (Array.isArray(A)) return ta1(A)
}

function Be6(A) {
  if (typeof Symbol !== "undefined" && A[Symbol.iterator] != null || A["@@iterator"] != null) return Array.from(A)
}

function Qe6(A, B) {
  if (!A) return;
  if (typeof A === "string") return ta1(A, B);
  var Q = Object.prototype.toString.call(A).slice(8, -1);
  if (Q === "Object" && A.constructor) Q = A.constructor.name;
  if (Q === "Map" || Q === "Set") return Array.from(A);
  if (Q === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(Q)) return ta1(A, B)
}

function ta1(A, B) {
  if (B == null || B > A.length) B = A.length;
  for (var Q = 0, I = new Array(B); Q < B; Q++) I[Q] = A[Q];
  return I
}

function Ie6() {
  throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)
}
var $t = new sV;
$t.useLongestToken = !0;
$t.tokenize = TX1.tokenize;
$t.castInput = function(A, B) {
  var {
    undefinedReplacement: Q,
    stringifyReplacer: I
  } = B, G = I === void 0 ? function(D, Z) {
    return typeof Z === "undefined" ? Q : Z
  } : I;
  return typeof A === "string" ? A : JSON.stringify(ea1(A, null, null, G), G, "  ")
};
$t.equals = function(A, B, Q) {
  return sV.prototype.equals.call($t, A.replace(/,([\r\n])/g, "$1"), B.replace(/,([\r\n])/g, "$1"), Q)
};

function ea1(A, B, Q, I, G) {
  if (B = B || [], Q = Q || [], I) A = I(G, A);
  var D;
  for (D = 0; D < B.length; D += 1)
    if (B[D] === A) return Q[D];
  var Z;
  if (Object.prototype.toString.call(A) === "[object Array]") {
    B.push(A), Z = new Array(A.length), Q.push(Z);
    for (D = 0; D < A.length; D += 1) Z[D] = ea1(A[D], B, Q, I, G);
    return B.pop(), Q.pop(), Z
  }
  if (A && A.toJSON) A = A.toJSON();
  if (oa1(A) === "object" && A !== null) {
    B.push(A), Z = {}, Q.push(Z);
    var Y = [],
      W;
    for (W in A)
      if (Object.prototype.hasOwnProperty.call(A, W)) Y.push(W);
    Y.sort();
    for (D = 0; D < Y.length; D += 1) W = Y[D], Z[W] = ea1(A[W], B, Q, I, W);
    B.pop(), Q.pop()
  } else Z = A;
  return Z
}
var As1 = new sV;
As1.tokenize = function(A) {
  return A.slice()
};
As1.join = As1.removeEmpty = function(A) {
  return A
};

function qt(A, B, Q, I, G, D, Z) {
  if (!Z) Z = {};
  if (typeof Z === "function") Z = {
    callback: Z
  };
  if (typeof Z.context === "undefined") Z.context = 4;
  if (Z.newlineIsToken) throw new Error(
    "newlineIsToken may not be used with patch-generation functions, only with diffing functions");
  if (!Z.callback) return F(xW2(Q, I, Z));
  else {
    var Y = Z,
      W = Y.callback;
    xW2(Q, I, vW2(vW2({}, Z), {}, {
      callback: function J(C) {
        var X = F(C);
        W(X)
      }
    }))
  }

  function F(J) {
    if (!J) return;
    J.push({
      value: "",
      lines: []
    });

    function C(a) {
      return a.map(function(g) {
        return " " + g
      })
    }
    var X = [],
      V = 0,
      K = 0,
      U = [],
      N = 1,
      q = 1,
      M = function a() {
        var g = J[R],
          Y1 = g.lines || Ge6(g.value);
        if (g.lines = Y1, g.added || g.removed) {
          var r;
          if (!V) {
            var w1 = J[R - 1];
            if (V = N, K = q, w1) U = Z.context > 0 ? C(w1.lines.slice(-Z.context)) : [], V -= U.length, K -= U.length
          }
          if ((r = U).push.apply(r, aa1(Y1.map(function(o1) {
              return (g.added ? "+" : "-") + o1
            }))), g.added) q += Y1.length;
          else N += Y1.length
        } else {
          if (V)
            if (Y1.length <= Z.context * 2 && R < J.length - 2) {
              var H1;
              (H1 = U).push.apply(H1, aa1(C(Y1)))
            } else {
              var x, F1 = Math.min(Y1.length, Z.context);
              (x = U).push.apply(x, aa1(C(Y1.slice(0, F1))));
              var x1 = {
                oldStart: V,
                oldLines: N - V + F1,
                newStart: K,
                newLines: q - K + F1,
                lines: U
              };
              X.push(x1), V = 0, K = 0, U = []
            } N += Y1.length, q += Y1.length
        }
      };
    for (var R = 0; R < J.length; R++) M();
    for (var T = 0, O = X; T < O.length; T++) {
      var S = O[T];
      for (var f = 0; f < S.lines.length; f++)
        if (S.lines[f].endsWith(`
`)) S.lines[f] = S.lines[f].slice(0, -1);
        else S.lines.splice(f + 1, 0, "\\ No newline at end of file"), f++
    }
    return {
      oldFileName: A,
      newFileName: B,
      oldHeader: G,
      newHeader: D,
      hunks: X
    }
  }
}

function Ge6(A) {
  var B = A.endsWith(`
`),
    Q = A.split(`
`).map(function(I) {
      return I + `
`
    });
  if (B) Q.pop();
  else Q.push(Q.pop().slice(0, -1));
  return Q
}
var yR = J1(_1(), 1);
var De6 = 0.4,
  Ze6 = 80;

function wZ({
  patch: A,
  dim: B,
  skipUnchanged: Q,
  hideLineNumbers: I,
  overrideTheme: G,
  width: D
}) {
  let Z = yR.useRef(null),
    [Y, W] = yR.useState(D || Ze6);
  yR.useEffect(() => {
    if (!D && Z.current) {
      let {
        width: J
      } = W71(Z.current);
      if (J > 0) W(J - 2)
    }
  }, [D]);
  let F = yR.useMemo(() => Ce6(A.lines, A.oldStart, Y, B, Q, I, G), [A.lines, A.oldStart, Y, B, Q, I, G]);
  return L9.createElement(m, {
    flexDirection: "column",
    flexGrow: 1,
    ref: Z
  }, F.map((J, C) => L9.createElement(m, {
    key: C
  }, J)))
}

function Ye6(A) {
  return A.map((B) => {
    if (B.startsWith("+")) return {
      code: " " + B.slice(1),
      i: 0,
      type: "add",
      originalCode: B.slice(1)
    };
    if (B.startsWith("-")) return {
      code: " " + B.slice(1),
      i: 0,
      type: "remove",
      originalCode: B.slice(1)
    };
    return {
      code: B,
      i: 0,
      type: "nochange",
      originalCode: B
    }
  })
}

function We6(A) {
  let B = [],
    Q = 0;
  while (Q < A.length) {
    let I = A[Q];
    if (!I) {
      Q++;
      continue
    }
    if (I.type === "remove") {
      let G = [I],
        D = Q + 1;
      while (D < A.length && A[D]?.type === "remove") {
        let Y = A[D];
        if (Y) G.push(Y);
        D++
      }
      let Z = [];
      while (D < A.length && A[D]?.type === "add") {
        let Y = A[D];
        if (Y) Z.push(Y);
        D++
      }
      if (G.length > 0 && Z.length > 0) {
        let Y = Math.min(G.length, Z.length);
        for (let W = 0; W < Y; W++) {
          let F = G[W],
            J = Z[W];
          if (F && J) F.wordDiff = !0, J.wordDiff = !0, F.matchedLine = J, J.matchedLine = F
        }
        B.push(...G.filter(Boolean)), B.push(...Z.filter(Boolean)), Q = D
      } else B.push(I), Q++
    } else B.push(I), Q++
  }
  return B
}

function Fe6(A, B) {
  return gW2(A, B, {
    ignoreCase: !1
  })
}

function Je6(A, B, Q, I, G, D) {
  let Z = $1(D),
    {
      type: Y,
      i: W,
      wordDiff: F,
      matchedLine: J,
      originalCode: C
    } = A,
    X = `${Y}-${W}-${B}`;
  if (!F || !J || B !== 0) return null;
  let V = C,
    K = J.originalCode,
    U, N;
  if (Y === "remove") U = V, N = K;
  else U = J.originalCode, N = C;
  let q = Fe6(U, N),
    M = U.length + N.length,
    O = q.filter((S) => S.added || S.removed).reduce((S, f) => S + f.value.length, 0) / M > De6 || I;
  if (Y === "add") return L9.createElement(y, {
    key: X
  }, L9.createElement(hm, {
    i: W,
    width: Q,
    hidden: G
  }), L9.createElement(y, {
    backgroundColor: I ? Z.diff.addedDimmed : Z.diff.added
  }, " ", O ? L9.createElement(y, {
    color: D ? Z.text : void 0,
    dimColor: I
  }, C) : q.map((S, f) => {
    if (S.added) return L9.createElement(y, {
      key: `part-${f}`,
      backgroundColor: I ? Z.diff.addedWordDimmed : Z.diff.addedWord,
      color: D ? Z.text : void 0,
      dimColor: I
    }, S.value);
    else if (S.removed) return null;
    else return L9.createElement(y, {
      key: `part-${f}`,
      color: D ? Z.text : void 0,
      dimColor: I
    }, S.value)
  })));
  else if (Y === "remove") return L9.createElement(y, {
    key: X
  }, L9.createElement(hm, {
    i: W,
    width: Q,
    hidden: G
  }), L9.createElement(y, {
    backgroundColor: I ? Z.diff.removedDimmed : Z.diff.removed
  }, " ", O ? L9.createElement(y, {
    color: D ? Z.text : void 0,
    dimColor: I
  }, C) : q.map((S, f) => {
    if (S.removed) return L9.createElement(y, {
      key: `part-${f}`,
      backgroundColor: I ? Z.diff.removedWordDimmed : Z.diff.removedWord,
      color: D ? Z.text : void 0,
      dimColor: I
    }, S.value);
    else if (S.added) return null;
    else return L9.createElement(y, {
      key: `part-${f}`,
      color: D ? Z.text : void 0,
      dimColor: I
    }, S.value)
  })));
  return null
}

function Ce6(A, B, Q, I, G, D, Z) {
  let Y = $1(Z),
    W = Ye6(A),
    F = We6(W),
    J = Xe6(F, B),
    X = Math.max(...J.map(({
      i: K
    }) => K), 0).toString().length,
    V = (K, U) => L9.createElement(y, {
      color: Z ? Y.text : void 0,
      backgroundColor: U,
      dimColor: I
    }, K);
  return J.flatMap((K) => {
    let {
      type: U,
      code: N,
      i: q,
      wordDiff: M,
      matchedLine: R
    } = K;
    if (G && U === "nochange") return [];
    return $40(N, Q - X).map((O, S) => {
      let f = `${U}-${q}-${S}`;
      if (M && R && S === 0) {
        let a = Je6(K, S, X, I, D, Z);
        if (a) return a;
        return L9.createElement(y, {
          key: f
        }, L9.createElement(hm, {
          i: S === 0 ? q : void 0,
          width: X,
          hidden: D
        }), V(O, void 0))
      } else switch (U) {
        case "add":
          return L9.createElement(y, {
            key: f
          }, L9.createElement(hm, {
            i: S === 0 ? q : void 0,
            width: X,
            hidden: D
          }), L9.createElement(y, {
            color: Z ? Y.text : void 0,
            backgroundColor: I ? Y.diff.addedDimmed : Y.diff.added,
            dimColor: I
          }, O));
        case "remove":
          return L9.createElement(y, {
            key: f
          }, L9.createElement(hm, {
            i: S === 0 ? q : void 0,
            width: X,
            hidden: D
          }), L9.createElement(y, {
            color: Z ? Y.text : void 0,
            backgroundColor: I ? Y.diff.removedDimmed : Y.diff.removed,
            dimColor: I
          }, O));
        case "nochange":
          return L9.createElement(y, {
            key: f
          }, L9.createElement(hm, {
            i: S === 0 ? q : void 0,
            width: X,
            hidden: D
          }), L9.createElement(y, {
            color: Z ? Y.text : void 0,
            dimColor: I
          }, O))
      }
    })
  })
}

function hm({
  i: A,
  width: B,
  hidden: Q
}) {
  if (Q) return null;
  return L9.createElement(y, {
    color: $1().secondaryText
  }, A !== void 0 ? A.toString().padStart(B) : " ".repeat(B), " ")
}

function Xe6(A, B) {
  let Q = B,
    I = [],
    G = [...A];
  while (G.length > 0) {
    let D = G.shift(),
      {
        code: Z,
        type: Y,
        originalCode: W,
        wordDiff: F,
        matchedLine: J
      } = D,
      C = {
        code: Z,
        type: Y,
        i: Q,
        originalCode: W,
        wordDiff: F,
        matchedLine: J
      };
    switch (Y) {
      case "nochange":
        Q++, I.push(C);
        break;
      case "add":
        Q++, I.push(C);
        break;
      case "remove": {
        I.push(C);
        let X = 0;
        while (G[0]?.type === "remove") {
          Q++;
          let V = G.shift(),
            {
              code: K,
              type: U,
              originalCode: N,
              wordDiff: q,
              matchedLine: M
            } = V,
            R = {
              code: K,
              type: U,
              i: Q,
              originalCode: N,
              wordDiff: q,
              matchedLine: M
            };
          I.push(R), X++
        }
        Q -= X;
        break
      }
    }
  }
  return I
}
import {
  relative as Ve6,
  resolve as Ke6
} from "path";
var PX1 = J1(_1(), 1);

function D4() {
  let [A, B] = PX1.useState({
    columns: process.stdout.columns || 80,
    rows: process.stdout.rows || 24
  });
  return PX1.useEffect(() => {
    function Q() {
      B({
        columns: process.stdout.columns || 80,
        rows: process.stdout.rows || 24
      })
    }
    return process.stdout.setMaxListeners(100).on("resize", Q), () => {
      process.stdout.off("resize", Q)
    }
  }, []), A
}

function SX1({
  filePath: A,
  structuredPatch: B,
  verbose: Q
}) {
  let {
    columns: I
  } = D4(), G = B.reduce((F, J) => F + J.lines.filter((C) => C.startsWith("+")).length, 0), D = B.reduce((F, J) => F + J
    .lines.filter((C) => C.startsWith("-")).length, 0), Z = Hc(A), Y = Ke6(u4(), "CLAUDE.md"), W = Z === Y;
  return Y4.createElement(m, {
    flexDirection: "column"
  }, Y4.createElement(m, {
      flexDirection: "row"
    }, Y4.createElement(m, {
      minWidth: 5
    }, Y4.createElement(y, null, "  ", "⎿ ")), Y4.createElement(m, null, Y4.createElement(y, null, "Updated ")), Y4
    .createElement(y, null, Y4.createElement(y, {
        bold: !0
      }, Q ? A : Ve6(uA(), A)), G > 0 || D > 0 ? " with " : "", G > 0 ? Y4.createElement(Y4.Fragment, null, Y4
        .createElement(y, {
          bold: !0
        }, G), " ", G > 1 ? "additions" : "addition") : null, G > 0 && D > 0 ? " and " : null, D > 0 ? Y4
      .createElement(Y4.Fragment, null, Y4.createElement(y, {
        bold: !0
      }, D), " ", D > 1 ? "removals" : "removal") : null)), hY(B.map((F) => Y4.createElement(m, {
    flexDirection: "column",
    paddingLeft: 5,
    key: F.newStart
  }, Y4.createElement(wZ, {
    patch: F,
    dim: !1,
    width: I - 12
  }))), (F) => Y4.createElement(m, {
    paddingLeft: 5,
    key: `ellipsis-${F}`
  }, Y4.createElement(y, {
    color: $1().secondaryText
  }, "..."))), W && Y4.createElement(m, {
    marginTop: 1
  }, Y4.createElement(y, null, Y4.createElement(y, {
    bold: !0
  }, "Tip:"), " Use", " ", Y4.createElement(y, {
    color: $1().remember
  }, "# to memorize"), " shortcut to quickly add to CLAUDE.md")))
}
var hW2 = "Write";
var mW2 =
  `Writes a file to the local filesystem.

Usage:
- This tool will overwrite the existing file if there is one at the provided path.
- If this is an existing file, you MUST use the ${oL} tool first to read the file's contents. This tool will fail if you did not read the file first.
- ALWAYS prefer editing existing files in the codebase. NEVER write new files unless explicitly required.
- NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.`;
var dW2 = 3,
  uW2 = "<<:AMPERSAND_TOKEN:>>",
  pW2 = "<<:DOLLAR_TOKEN:>>";

function Mt(A) {
  return A.replaceAll("&", uW2).replaceAll("$", pW2)
}

function cW2(A) {
  return A.replaceAll(uW2, "&").replaceAll(pW2, "$")
}

function i_(A, B) {
  let Q = 0,
    I = 0;
  if (A.length === 0 && B) Q = B.split(/\r?\n/).length;
  else Q = A.reduce((G, D) => G + D.lines.filter((Z) => Z.startsWith("+")).length, 0), I = A.reduce((G, D) => G + D
    .lines.filter((Z) => Z.startsWith("-")).length, 0);
  xU1(Q, I), ga1.add(Q, {
    type: "added"
  }), ga1.add(I, {
    type: "removed"
  }), j1("tengu_file_changed", {
    lines_added: Q,
    lines_removed: I
  })
}

function lW2({
  filePath: A,
  oldContent: B,
  newContent: Q,
  ignoreWhitespace: I = !1,
  singleHunk: G = !1
}) {
  return qt(A, A, Mt(B), Mt(Q), void 0, void 0, {
    ignoreWhitespace: I,
    context: G ? 1e5 : dW2
  }).hunks.map((D) => ({
    ...D,
    lines: D.lines.map(cW2)
  }))
}

function wF({
  filePath: A,
  fileContents: B,
  edits: Q,
  ignoreWhitespace: I = !1
}) {
  let G = Mt(Ex(B));
  return qt(A, A, G, Q.reduce((D, {
    old_string: Z,
    new_string: Y
  }) => D.replaceAll(Mt(Ex(Z)), () => Mt(Ex(Y))), G), void 0, void 0, {
    context: dW2,
    ignoreWhitespace: I
  }).hunks.map((D) => ({
    ...D,
    lines: D.lines.map(cW2)
  }))
}
var iW2 = 10,
  nW2 = 16000,
  $e6 =
  "<response clipped><NOTE>To save on context only part of this file has been shown to you. You should retry this tool after you have searched inside the file with Grep in order to find the line numbers of what you are looking for.</NOTE>",
  qe6 = i.strictObject({
    file_path: i.string().describe("The absolute path to the file to write (must be absolute, not relative)"),
    content: i.string().describe("The content to write to the file")
  }),
  HD = {
    name: hW2,
    async description() {
      return "Write a file to the local filesystem."
    },
    userFacingName: () => "Write",
    async prompt() {
      return mW2
    },
    isEnabled() {
      return !0
    },
    renderToolUseMessage(A, {
      verbose: B
    }) {
      return `file_path: ${B?A.file_path:Bs1(uA(),A.file_path)}`
    },
    inputSchema: qe6,
    isReadOnly() {
      return !1
    },
    getPath(A) {
      return A.file_path
    },
    async checkPermissions(A, B) {
      return B_(HD, A, B.getToolPermissionContext())
    },
    renderToolUseRejectedMessage({
      file_path: A,
      content: B
    }, {
      columns: Q,
      verbose: I
    }) {
      try {
        let G = b1(),
          D = Ee6(A) ? A : Ue6(uA(), A),
          Z = G.existsSync(D),
          Y = Z ? nI(D) : "utf-8",
          W = Z ? G.readFileSync(D, {
            encoding: Y
          }) : null,
          F = W ? "update" : "create",
          J = wF({
            filePath: A,
            fileContents: W ?? "",
            edits: [{
              old_string: W ?? "",
              new_string: B
            }]
          });
        return h4.createElement(m, {
          flexDirection: "column"
        }, h4.createElement(y, null, "  ", "⎿", " ", h4.createElement(y, {
          color: $1().error
        }, "User rejected ", F === "update" ? "update" : "write", " to", " "), h4.createElement(y, {
          bold: !0,
          color: $1().error
        }, I ? A : Bs1(uA(), A))), hY(J.map((C) => h4.createElement(m, {
          flexDirection: "column",
          paddingLeft: 5,
          key: C.newStart
        }, h4.createElement(wZ, {
          patch: C,
          dim: !0,
          width: Q - 12
        }))), (C) => h4.createElement(m, {
          paddingLeft: 5,
          key: `ellipsis-${C}`
        }, h4.createElement(y, {
          color: $1().secondaryText
        }, "..."))))
      } catch (G) {
        return g1(G), h4.createElement(m, {
          flexDirection: "column"
        }, h4.createElement(y, null, "  ", "⎿ (No changes)"))
      }
    },
    renderToolUseErrorMessage(A, {
      verbose: B
    }) {
      return h4.createElement(q6, {
        result: A,
        verbose: B
      })
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage({
      filePath: A,
      content: B,
      structuredPatch: Q,
      type: I
    }, G, {
      verbose: D
    }) {
      switch (I) {
        case "create": {
          let Z = B || "(No content)",
            Y = B.split(He6).length,
            W = Y - iW2;
          return h4.createElement(m, {
            flexDirection: "column"
          }, h4.createElement(y, null, "  ", "⎿ Wrote ", h4.createElement(y, {
            bold: !0
          }, Y), " lines to", " ", h4.createElement(y, {
            bold: !0
          }, D ? A : Bs1(uA(), A))), h4.createElement(m, {
            flexDirection: "column",
            paddingLeft: 5
          }, h4.createElement(CC, {
            code: D ? Z : Z.split(`
`).slice(0, iW2).filter((F) => F.trim() !== "").join(`
`),
            language: we6(A).slice(1)
          }), !D && W > 0 && h4.createElement(y, {
            color: $1().secondaryText
          }, "… +", W, " ", W === 1 ? "line" : "lines", " ", Y > 0 && h4.createElement(Xz, null))))
        }
        case "update":
          return h4.createElement(SX1, {
            filePath: A,
            structuredPatch: Q,
            verbose: D
          })
      }
    },
    async validateInput({
      file_path: A
    }, {
      readFileState: B
    }) {
      let Q = CG(A);
      if (Nx(Q)) return {
        result: !1,
        message: "File is in a directory that is ignored by your project configuration.",
        errorCode: 1
      };
      let I = b1();
      if (!I.existsSync(Q)) return {
        result: !0
      };
      let G = B[Q];
      if (!G) return {
        result: !1,
        message: "File has not been read yet. Read it first before writing to it.",
        errorCode: 2
      };
      if (I.statSync(Q).mtimeMs > G.timestamp) return {
        result: !1,
        message: "File has been modified since read, either by the user or by a linter. Read it again before attempting to write it.",
        errorCode: 3
      };
      return {
        result: !0
      }
    },
    async * call({
      file_path: A,
      content: B
    }, {
      readFileState: Q
    }) {
      let I = CG(A),
        G = ze6(I),
        D = b1(),
        Z = D.existsSync(I),
        Y = Z ? nI(I) : "utf-8",
        W = Z ? D.readFileSync(I, {
          encoding: Y
        }) : null;
      await fV.beforeFileEdited(I);
      let F = Z ? iE(I) : await fLA();
      if (D.mkdirSync(G), Sq(I, B, Y, F), Q[I] = {
          content: B,
          timestamp: D.statSync(I).mtimeMs
        }, I.endsWith(`${Ne6}CLAUDE.md`)) j1("tengu_write_claudemd", {});
      if (W) {
        let C = wF({
            filePath: A,
            fileContents: W,
            edits: [{
              old_string: W,
              new_string: B
            }]
          }),
          X = {
            type: "update",
            filePath: A,
            content: B,
            structuredPatch: C
          };
        i_(C), yield {
          type: "result",
          data: X
        };
        return
      }
      let J = {
        type: "create",
        filePath: A,
        content: B,
        structuredPatch: []
      };
      i_([], B), yield {
        type: "result",
        data: J
      }
    },
    mapToolResultToToolResultBlockParam({
      filePath: A,
      content: B,
      type: Q
    }, I) {
      switch (Q) {
        case "create":
          return {
            tool_use_id: I, type: "tool_result", content: `File created successfully at: ${A}`
          };
        case "update":
          return {
            tool_use_id: I, type: "tool_result", content: `The file ${A} has been updated. Here's the result of running \`cat -n\` on a snippet of the edited file:
${_q({content:B.split(/\r?\n/).length>nW2?B.split(/\r?\n/).slice(0,nW2).join(`
`)+$e6:B,startLine:1})}`
          }
      }
    }
  };
import {
  randomUUID as ZA5
} from "crypto";
var rV = J1(_1(), 1);
import {
  dirname as Oe6,
  isAbsolute as jX1,
  relative as Te6,
  resolve as Pe6,
  sep as Se6
} from "path";
var aW2 = `Performs exact string replacements in files with strict occurrence count validation.

Usage:
- When editing text from Read tool output, ensure you preserve the exact indentation (tabs/spaces) as it appears AFTER the line number prefix. The line number prefix format is: spaces + line number + tab. Everything after that tab is the actual file content to match. Never include any part of the line number prefix in the old_string or new_string.
- ALWAYS prefer editing existing files in the codebase. NEVER write new files unless explicitly required.`;

function sW2(A, B, Q) {
  if (Q !== "") return A.replaceAll(B, () => Q);
  return !B.endsWith(`
`) && A.includes(B + `
`) ? A.replaceAll(B + `
`, () => Q) : A.replaceAll(B, () => Q)
}

function Lt({
  filePath: A,
  fileContents: B,
  oldString: Q,
  newString: I
}) {
  return mm({
    filePath: A,
    fileContents: B,
    edits: [{
      old_string: Q,
      new_string: I
    }]
  })
}

function mm({
  filePath: A,
  fileContents: B,
  edits: Q
}) {
  let I = B,
    G = [];
  for (let Z of Q) {
    let Y = Z.old_string.replace(/\n+$/, "");
    for (let F of G)
      if (Y !== "" && F.includes(Y)) throw new Error(
        "Cannot edit file: old_string is a substring of a new_string from a previous edit.");
    let W = I;
    if (I = Z.old_string === "" ? Z.new_string : sW2(I, Z.old_string, Z.new_string), I === W) throw new Error(
      "String not found in file. Failed to apply edit.");
    G.push(Z.new_string)
  }
  if (I === B) throw new Error("Original and edited file match exactly. Failed to apply edit.");
  return {
    patch: wF({
      filePath: A,
      fileContents: B,
      edits: [{
        old_string: B,
        new_string: I
      }]
    }),
    updatedFile: I
  }
}

function rW2(A, B) {
  return qt("file.txt", "file.txt", A, B, void 0, void 0, {
    context: 8
  }).hunks.map((I) => ({
    startLine: I.oldStart,
    content: I.lines.filter((G) => !G.startsWith("-")).map((G) => G.slice(1)).join(`
`)
  })).map(_q).join(`
...
`)
}

function oW2(A, B, Q, I = 4) {
  let D = (A.split(B)[0] ?? "").split(/\r?\n/).length - 1,
    Z = sW2(A, B, Q).split(/\r?\n/),
    Y = Math.max(0, D - I),
    W = D + I + Q.split(/\r?\n/).length;
  return {
    snippet: Z.slice(Y, W).join(`
`),
    startLine: Y + 1
  }
}

function tW2(A) {
  return A.map((B) => {
    let Q = [],
      I = [],
      G = [];
    for (let D of B.lines)
      if (D.startsWith(" ")) Q.push(D.slice(1)), I.push(D.slice(1)), G.push(D.slice(1));
      else if (D.startsWith("-")) I.push(D.slice(1));
    else if (D.startsWith("+")) G.push(D.slice(1));
    return {
      old_string: I.join(`
`),
      new_string: G.join(`
`)
    }
  })
}
var Me6 = {
  "<fnr>": "<function_results>",
  "<n>": "<name>",
  "</n>": "</name>",
  "<o>": "<output>",
  "</o>": "</output>",
  "<e>": "<error>",
  "</e>": "</error>",
  "<s>": "<system>",
  "</s>": "</system>",
  "<r>": "<result>",
  "</r>": "</result>",
  "< META_START >": "<META_START>",
  "< META_END >": "<META_END>",
  "< EOT >": "<EOT>",
  "< META >": "<META>",
  "< SOS >": "<SOS>",
  "\n\nH:": `

Human:`,
  "\n\nA:": `

Assistant:`
};

function Le6(A) {
  let B = A,
    Q = [];
  for (let [I, G] of Object.entries(Me6)) {
    let D = B;
    if (B = B.replaceAll(I, G), D !== B) Q.push({
      from: I,
      to: G
    })
  }
  return {
    result: B,
    appliedReplacements: Q
  }
}

function Qs1({
  file_path: A,
  edits: B
}) {
  if (B.length === 0) return {
    file_path: A,
    edits: B
  };
  try {
    let Q = CG(A),
      G = b1().readFileSync(Q, {
        encoding: nI(Q)
      }).replaceAll(`\r
`, `
`);
    return {
      file_path: A,
      edits: B.map(({
        old_string: D,
        new_string: Z,
        expected_replacements: Y
      }) => {
        if (G.includes(D)) return {
          old_string: D,
          new_string: Z,
          expected_replacements: Y
        };
        let {
          result: W,
          appliedReplacements: F
        } = Le6(D);
        if (G.includes(W)) {
          let J = Z;
          for (let {
              from: C,
              to: X
            }
            of F) J = J.replaceAll(C, X);
          return {
            old_string: W,
            new_string: J,
            expected_replacements: Y
          }
        }
        return {
          old_string: D,
          new_string: Z,
          expected_replacements: Y
        }
      })
    }
  } catch (Q) {
    g1(Q)
  }
  return {
    file_path: A,
    edits: B
  }
}
var NQ = J1(_1(), 1);
import {
  relative as Re6
} from "path";

function _X1({
  file_path: A,
  operation: B,
  patch: Q,
  verbose: I
}) {
  let {
    columns: G
  } = D4();
  return NQ.createElement(m, {
    flexDirection: "column"
  }, NQ.createElement(m, {
    flexDirection: "row"
  }, NQ.createElement(m, {
    minWidth: 5,
    width: 5
  }, NQ.createElement(y, null, "  ", "⎿ ")), NQ.createElement(y, {
    color: $1().error
  }, "User rejected ", B, " to "), NQ.createElement(y, {
    bold: !0,
    color: $1().error
  }, I ? A : Re6(uA(), A))), hY(Q.map((D) => NQ.createElement(m, {
    flexDirection: "column",
    paddingLeft: 5,
    key: D.newStart
  }, NQ.createElement(wZ, {
    patch: D,
    dim: !0,
    width: G - 12
  }))), (D) => NQ.createElement(m, {
    paddingLeft: 5,
    key: `ellipsis-${D}`
  }, NQ.createElement(y, {
    color: $1().secondaryText
  }, "..."))))
}
var _e6 = i.strictObject({
    file_path: i.string().describe("The absolute path to the file to modify"),
    old_string: i.string().describe("The text to replace"),
    new_string: i.string().describe("The text to replace it with (must be different from old_string)"),
    expected_replacements: i.number().default(1).optional().describe(
      "The expected number of replacements to perform. Defaults to 1 if not specified.")
  }),
  $Q = {
    name: _U,
    async description() {
      return "A tool for editing files"
    },
    async prompt() {
      return aW2
    },
    userFacingName(A) {
      if (!A) return "Update";
      if (A.old_string === "") return "Create";
      return "Update"
    },
    isEnabled() {
      return !0
    },
    inputSchema: _e6,
    isReadOnly() {
      return !1
    },
    getPath(A) {
      return A.file_path
    },
    async checkPermissions(A, B) {
      return B_($Q, A, B.getToolPermissionContext())
    },
    renderToolUseMessage({
      file_path: A
    }, {
      verbose: B
    }) {
      return B ? A : Te6(uA(), A)
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage({
      filePath: A,
      structuredPatch: B
    }, Q, {
      verbose: I
    }) {
      return rV.createElement(SX1, {
        filePath: A,
        structuredPatch: B,
        verbose: I
      })
    },
    renderToolUseRejectedMessage({
      file_path: A,
      old_string: B,
      new_string: Q
    }, {
      verbose: I
    }) {
      try {
        let G = b1().existsSync(A) ? b1().readFileSync(A, {
            encoding: "utf8"
          }) : "",
          {
            patch: D
          } = Lt({
            filePath: A,
            fileContents: G,
            oldString: B,
            newString: Q
          });
        return rV.createElement(_X1, {
          file_path: A,
          operation: B === "" ? "write" : "update",
          patch: D,
          verbose: I
        })
      } catch (G) {
        return g1(G), rV.createElement(r0, {
          height: 1
        }, rV.createElement(y, null, "(No changes)"))
      }
    },
    async validateInput({
      file_path: A,
      old_string: B,
      new_string: Q,
      expected_replacements: I = 1
    }, {
      readFileState: G
    }) {
      if (B === Q) return {
        result: !1,
        behavior: "ask",
        message: "No changes to make: old_string and new_string are exactly the same.",
        errorCode: 1
      };
      let D = jX1(A) ? A : Pe6(uA(), A);
      if (Nx(D)) return {
        result: !1,
        behavior: "ask",
        message: "File is in a directory that is ignored by your project configuration.",
        errorCode: 2
      };
      let Z = b1();
      if (Z.existsSync(D) && B === "") {
        if (Z.readFileSync(D, {
            encoding: nI(D)
          }).replaceAll(`\r
`, `
`).trim() !== "") return {
          result: !1,
          behavior: "ask",
          message: "Cannot create new file - file already exists.",
          errorCode: 3
        };
        return {
          result: !0
        }
      }
      if (!Z.existsSync(D) && B === "") return {
        result: !0
      };
      if (!Z.existsSync(D)) {
        let X = Ux(D),
          V = "File does not exist.";
        if (X) V += ` Did you mean ${X}?`;
        return {
          result: !1,
          behavior: "ask",
          message: V,
          errorCode: 4
        }
      }
      if (D.endsWith(".ipynb")) return {
        result: !1,
        behavior: "ask",
        message: `File is a Jupyter Notebook. Use the ${ch} to edit this file.`,
        errorCode: 5
      };
      let Y = G[D];
      if (!Y) return {
        result: !1,
        behavior: "ask",
        message: "File has not been read yet. Read it first before writing to it.",
        meta: {
          isFilePathAbsolute: String(jX1(A))
        },
        errorCode: 6
      };
      if (Z.statSync(D).mtimeMs > Y.timestamp) return {
        result: !1,
        behavior: "ask",
        message: "File has been modified since read, either by the user or by a linter. Read it again before attempting to write it.",
        errorCode: 7
      };
      let J = Z.readFileSync(D, {
        encoding: nI(D)
      }).replaceAll(`\r
`, `
`);
      if (!J.includes(B)) return {
        result: !1,
        behavior: "ask",
        message: `String to replace not found in file.
String: ${B}`,
        meta: {
          isFilePathAbsolute: String(jX1(A))
        },
        errorCode: 8
      };
      let C = J.split(B).length - 1;
      if (C !== I) return {
        result: !1,
        behavior: "ask",
        message: `Found ${C} matches of the string to replace, but expected ${I}. The number of actual matches must equal the expected replacements. Please adjust your string to match or update the expected count.
String: ${B}`,
        meta: {
          isFilePathAbsolute: String(jX1(A))
        },
        errorCode: 9
      };
      return {
        result: !0
      }
    },
    inputsEqual(A, B) {
      if (A.file_path !== B.file_path) return !1;
      let Q = b1().existsSync(A.file_path) ? b1().readFileSync(A.file_path, {
          encoding: "utf8"
        }) : "",
        I = b1().existsSync(B.file_path) ? b1().readFileSync(B.file_path, {
          encoding: "utf8"
        }) : "",
        G = Lt({
          filePath: A.file_path,
          fileContents: Q,
          oldString: A.old_string,
          newString: A.new_string
        }),
        D = Lt({
          filePath: B.file_path,
          fileContents: I,
          oldString: B.old_string,
          newString: B.new_string
        });
      return G.updatedFile === D.updatedFile
    },
    async * call({
      file_path: A,
      old_string: B,
      new_string: Q
    }, {
      readFileState: I
    }) {
      let G = b1(),
        D = CG(A),
        Z = G.existsSync(D) ? dG(D) : "";
      await fV.beforeFileEdited(D);
      let {
        patch: Y,
        updatedFile: W
      } = Lt({
        filePath: D,
        fileContents: Z,
        oldString: B,
        newString: Q
      }), F = Oe6(D);
      G.mkdirSync(F);
      let J = G.existsSync(D) ? iE(D) : "LF",
        C = G.existsSync(D) ? nI(D) : "utf8";
      if (Sq(D, W, C, J), I[D] = {
          content: W,
          timestamp: G.statSync(D).mtimeMs
        }, D.endsWith(`${Se6}CLAUDE.md`)) j1("tengu_write_claudemd", {});
      i_(Y), yield {
        type: "result",
        data: {
          filePath: A,
          oldString: B,
          newString: Q,
          originalFile: Z,
          structuredPatch: Y
        }
      }
    },
    mapToolResultToToolResultBlockParam({
      filePath: A,
      originalFile: B,
      oldString: Q,
      newString: I
    }, G) {
      let {
        snippet: D,
        startLine: Z
      } = oW2(B || "", Q, I);
      return {
        tool_use_id: G,
        type: "tool_result",
        content: `The file ${A} has been updated. Here's the result of running \`cat -n\` on a snippet of the edited file:
${_q({content:D,startLine:Z})}`
      }
    },
    renderToolUseErrorMessage(A, {
      verbose: B
    }) {
      return rV.createElement(q6, {
        result: A,
        verbose: B
      })
    }
  };
var n_ = J1(_1(), 1);
import {
  dirname as je6,
  sep as ye6
} from "path";
var ke6 = i.strictObject({
    old_string: i.string().describe("The text to replace"),
    new_string: i.string().describe("The text to replace it with"),
    expected_replacements: i.number().default(1).optional().describe(
      "The expected number of replacements to perform. Defaults to 1 if not specified.")
  }),
  xe6 = i.strictObject({
    file_path: i.string().describe("The absolute path to the file to modify"),
    edits: i.array(ke6).min(1, "At least one edit is required").describe(
      "Array of edit operations to perform sequentially on the file")
  }),
  oV = {
    name: dF1,
    description: $Q.description,
    async prompt() {
      return Lu0
    },
    userFacingName(A) {
      if (!A) return "Update";
      if (eW2(A.edits)) return "Create";
      return "Update"
    },
    isEnabled() {
      return !0
    },
    inputSchema: xe6,
    isReadOnly() {
      return !1
    },
    getPath(A) {
      return A.file_path
    },
    async checkPermissions(A, B) {
      return $Q.checkPermissions({
        file_path: A.file_path,
        old_string: "",
        new_string: ""
      }, B)
    },
    renderToolUseMessage({
      file_path: A
    }, {
      verbose: B
    }) {
      return $Q.renderToolUseMessage({
        file_path: A,
        old_string: "",
        new_string: ""
      }, {
        verbose: B
      })
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage({
      filePath: A,
      originalFileContents: B,
      structuredPatch: Q
    }, I, G) {
      return $Q.renderToolResultMessage({
        filePath: A,
        originalFile: B,
        structuredPatch: Q,
        oldString: "",
        newString: ""
      }, I, G)
    },
    renderToolUseRejectedMessage({
      file_path: A,
      edits: B
    }, {
      verbose: Q
    }) {
      try {
        let I = b1().existsSync(A) ? b1().readFileSync(A, {
            encoding: "utf8"
          }) : "",
          {
            patch: G
          } = mm({
            filePath: A,
            fileContents: I,
            edits: B
          });
        return n_.createElement(_X1, {
          file_path: A,
          operation: eW2(B) ? "write" : "update",
          patch: G,
          verbose: Q
        })
      } catch (I) {
        return g1(I), n_.createElement(r0, {
          height: 1
        }, n_.createElement(y, null, "(No changes)"))
      }
    },
    async validateInput({
      file_path: A,
      edits: B
    }, Q) {
      for (let I of B) {
        let G = await $Q.validateInput({
          file_path: A,
          old_string: I.old_string,
          new_string: I.new_string
        }, Q);
        if (!G.result) return G
      }
      return {
        result: !0
      }
    },
    inputsEqual(A, B) {
      if (A.file_path !== B.file_path) return !1;
      if (A.edits.length !== B.edits.length) return !1;
      return A.edits.every((Q, I) => {
        let G = B.edits[I];
        if (!G) return !1;
        return Q.old_string === G.old_string && Q.new_string === G.new_string && Q.expected_replacements === G
          .expected_replacements
      })
    },
    async * call({
      file_path: A,
      edits: B
    }, {
      readFileState: Q
    }) {
      let I = b1(),
        G = CG(A),
        D = I.existsSync(G) ? dG(G) : "";
      await fV.beforeFileEdited(G);
      let {
        patch: Z,
        updatedFile: Y
      } = mm({
        filePath: G,
        fileContents: D,
        edits: B
      }), W = je6(G);
      I.mkdirSync(W);
      let F = I.existsSync(G) ? iE(G) : "LF",
        J = I.existsSync(G) ? nI(G) : "utf8";
      if (Sq(G, Y, J, F), Q[G] = {
          content: Y,
          timestamp: I.statSync(G).mtimeMs
        }, G.endsWith(`${ye6}CLAUDE.md`)) j1("tengu_write_claudemd", {});
      i_(Z), yield {
        type: "result",
        data: {
          filePath: A,
          edits: B,
          originalFileContents: D,
          structuredPatch: Z
        }
      }
    },
    mapToolResultToToolResultBlockParam({
      filePath: A,
      edits: B
    }, Q) {
      return {
        tool_use_id: Q,
        type: "tool_result",
        content: `Applied ${B.length} edit${B.length===1?"":"s"} to ${A}:
${B.map((I,G)=>`${G+1}. Replaced "${I.old_string.substring(0,50)}${I.old_string.length>50?"...":""}" with "${I.new_string.substring(0,50)}${I.new_string.length>50?"...":""}"`).join(`
`)}`
      }
    },
    renderToolUseErrorMessage(A, B) {
      return $Q.renderToolUseErrorMessage(A, B)
    }
  };

function eW2(A) {
  return A.some((B) => B.old_string === "")
}
var qQ = J1(_1(), 1);
import {
  extname as fe6,
  isAbsolute as QF2,
  relative as ve6,
  resolve as IF2
} from "path";
var AF2 = "Replace the contents of a specific cell in a Jupyter notebook.",
  BF2 =
  "Completely replaces the contents of a specific cell in a Jupyter notebook (.ipynb file) with new source. Jupyter notebooks are interactive documents that combine code, text, and visualizations, commonly used for data analysis and scientific computing. The notebook_path parameter must be an absolute path, not a relative path. The cell_number is 0-indexed. Use edit_mode=insert to add a new cell at the index specified by cell_number. Use edit_mode=delete to delete the cell at the index specified by cell_number.";
var be6 = i.strictObject({
    notebook_path: i.string().describe(
      "The absolute path to the Jupyter notebook file to edit (must be absolute, not relative)"),
    cell_number: i.number().describe("The index of the cell to edit (0-based)"),
    new_source: i.string().describe("The new source for the cell"),
    cell_type: i.enum(["code", "markdown"]).optional().describe(
      "The type of the cell (code or markdown). If not specified, it defaults to the current cell type. If using edit_mode=insert, this is required."
      ),
    edit_mode: i.enum(["replace", "insert", "delete"]).optional().describe(
      "The type of edit to make (replace, insert, delete). Defaults to replace.")
  }),
  zw = {
    name: ch,
    async description() {
      return AF2
    },
    async prompt() {
      return BF2
    },
    userFacingName() {
      return "Edit Notebook"
    },
    isEnabled() {
      return !0
    },
    inputSchema: be6,
    isReadOnly() {
      return !1
    },
    getPath(A) {
      return A.notebook_path
    },
    async checkPermissions(A, B) {
      return B_(zw, A, B.getToolPermissionContext())
    },
    mapToolResultToToolResultBlockParam({
      cell_number: A,
      edit_mode: B,
      new_source: Q,
      error: I
    }, G) {
      if (I) return {
        tool_use_id: G,
        type: "tool_result",
        content: I,
        is_error: !0
      };
      switch (B) {
        case "replace":
          return {
            tool_use_id: G, type: "tool_result", content: `Updated cell ${A} with ${Q}`
          };
        case "insert":
          return {
            tool_use_id: G, type: "tool_result", content: `Inserted cell ${A} with ${Q}`
          };
        case "delete":
          return {
            tool_use_id: G, type: "tool_result", content: `Deleted cell ${A}`
          };
        default:
          return {
            tool_use_id: G, type: "tool_result", content: "Unknown edit mode"
          }
      }
    },
    renderToolUseMessage({
      notebook_path: A,
      cell_number: B,
      new_source: Q,
      cell_type: I,
      edit_mode: G
    }, {
      verbose: D
    }) {
      if (D) return `${A}@${B}, content: ${Q.slice(0,30)}…, cell_type: ${I}, edit_mode: ${G??"replace"}`;
      return `${ve6(uA(),A)}@${B}`
    },
    renderToolUseRejectedMessage() {
      return qQ.createElement(I5, null)
    },
    renderToolUseErrorMessage(A, {
      verbose: B
    }) {
      return qQ.createElement(q6, {
        result: A,
        verbose: B
      })
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage({
      cell_number: A,
      new_source: B,
      language: Q,
      error: I
    }) {
      if (I) return qQ.createElement(r0, null, qQ.createElement(y, {
        color: $1().error
      }, I));
      return qQ.createElement(r0, null, qQ.createElement(m, {
        flexDirection: "column"
      }, qQ.createElement(y, null, "Updated cell ", qQ.createElement(y, {
        bold: !0
      }, A), ":"), qQ.createElement(m, {
        marginLeft: 2
      }, qQ.createElement(CC, {
        code: B,
        language: Q
      }))))
    },
    async validateInput({
      notebook_path: A,
      cell_number: B,
      cell_type: Q,
      edit_mode: I = "replace"
    }) {
      let G = QF2(A) ? A : IF2(uA(), A),
        D = b1();
      if (!D.existsSync(G)) return {
        result: !1,
        message: "Notebook file does not exist.",
        errorCode: 1
      };
      if (fe6(G) !== ".ipynb") return {
        result: !1,
        message: "File must be a Jupyter notebook (.ipynb file). For editing other file types, use the FileEdit tool.",
        errorCode: 2
      };
      if (B < 0) return {
        result: !1,
        message: "Cell number must be non-negative.",
        errorCode: 3
      };
      if (I !== "replace" && I !== "insert" && I !== "delete") return {
        result: !1,
        message: "Edit mode must be replace, insert, or delete.",
        errorCode: 4
      };
      if (I === "insert" && !Q) return {
        result: !1,
        message: "Cell type is required when using edit_mode=insert.",
        errorCode: 5
      };
      let Z = nI(G),
        Y = D.readFileSync(G, {
          encoding: Z
        }),
        W = T8(Y);
      if (!W) return {
        result: !1,
        message: "Notebook is not valid JSON.",
        errorCode: 6
      };
      if (I === "insert" && B > W.cells.length) return {
        result: !1,
        message: `Cell number is out of bounds. For insert mode, the maximum value is ${W.cells.length} (to append at the end).`,
        errorCode: 7
      };
      else if (I === "replace" && B === W.cells.length) return {
        result: !0
      };
      else if ((I === "replace" || I === "delete") && (B >= W.cells.length || !W.cells[B])) return {
        result: !1,
        message: `Cell number is out of bounds. Notebook has ${W.cells.length} cells.`,
        errorCode: 8
      };
      return {
        result: !0
      }
    },
    async * call({
      notebook_path: A,
      cell_number: B,
      new_source: Q,
      cell_type: I,
      edit_mode: G
    }) {
      let D = QF2(A) ? A : IF2(uA(), A);
      try {
        let Z = nI(D),
          Y = b1().readFileSync(D, {
            encoding: Z
          }),
          W = JSON.parse(Y),
          F = G;
        if (F === "replace" && B === W.cells.length) {
          if (F = "insert", !I) I = "code"
        }
        let J = W.metadata.language_info?.name ?? "python";
        if (F === "delete") W.cells.splice(B, 1);
        else if (F === "insert") {
          let V = {
            cell_type: I,
            source: Q,
            metadata: {}
          };
          W.cells.splice(B, 0, I == "markdown" ? V : {
            ...V,
            outputs: []
          })
        } else {
          let V = W.cells[B];
          if (V.source = Q, V.execution_count = void 0, V.outputs = [], I && I !== V.cell_type) V.cell_type = I
        }
        let C = iE(D);
        Sq(D, JSON.stringify(W, null, 1), Z, C), yield {
          type: "result",
          data: {
            cell_number: B,
            new_source: Q,
            cell_type: I ?? "code",
            language: J,
            edit_mode: F ?? "replace",
            error: ""
          }
        }
      } catch (Z) {
        if (Z instanceof Error) {
          yield {
            type: "result",
            data: {
              cell_number: B,
              new_source: Q,
              cell_type: I ?? "code",
              language: "python",
              edit_mode: "replace",
              error: Z.message
            }
          };
          return
        }
        yield {
          type: "result",
          data: {
            cell_number: B,
            new_source: Q,
            cell_type: I ?? "code",
            language: "python",
            edit_mode: "replace",
            error: "Unknown error occurred while editing notebook"
          }
        }
      }
    }
  };
async function ZF2() {
  return await eH("tengu-binary-feedback-config", {
    sampleFrequency: 0
  })
}

function GF2(A) {
  return A.message.content.map((B) => {
    if (B.type === "text") return "text";
    if (B.type === "tool_use") return B.name;
    return B.type
  })
}
async function DF2(A, B, Q, I) {
  j1("tengu_binary_feedback_display_decision", {
    decision: A,
    reason: I,
    msg_id_A: B.message.id,
    msg_id_B: Q.message.id,
    seqA: String(GF2(B)),
    seqB: String(GF2(Q))
  })
}

function ge6(A, B) {
  return A.text === B.text
}

function he6(A, B, Q) {
  if (A.type !== B.type) return !1;
  if (A.type === "text") return ge6(A, B);
  if (A.type === "server_tool_use" || A.type === "web_search_tool_result") return Kk(A, B);
  if (A.type === "tool_use") {
    let I = A,
      G = B;
    if (I.name !== G.name) return !1;
    let D = Q.find((Z) => Z.name === I.name);
    if (!D) return g1(new Error(`Tool ${I.name} not found in tools`)), !1;
    if (D.inputsEqual) return D.inputsEqual(I.input, G.input);
    else return Kk(I.input, G.input)
  }
  return !1
}

function me6(A, B, Q) {
  if (A.length !== B.length) return !1;
  return OU1(A, B).every(([I, G]) => he6(I, G, Q))
}
async function de6(A, B) {
  let I = B.options.tools.find((Z) => Z.name === A.name);
  if (!I) return !1;
  let G = I.inputSchema.safeParse(A.input);
  if (!G.success) return !1;
  let D = Is1(I, G.data);
  return I.validateInput ? (await I.validateInput(D, B)).result : !0
}
async function YF2(A, B, Q) {
  let I = () => DF2(!0, A, B),
    G = (V) => DF2(!1, A, B, V),
    D = A.message.content.filter((V) => V.type !== "thinking" && V.type !== "redacted_thinking"),
    Z = B.message.content.filter((V) => V.type !== "thinking" && V.type !== "redacted_thinking"),
    Y = D.filter((V) => V.type === "tool_use"),
    W = Z.filter((V) => V.type === "tool_use");
  if (!(await Promise.all([...Y, ...W].map((V) => de6(V, Q)))).every(Boolean)) return G("tool_use_invalid"), !1;
  let J = [$Q.name, HD.name, oV.name, zw.name],
    C = D.filter((V) => V.type === "tool_use" && J.includes(V.name)),
    X = Z.filter((V) => V.type === "tool_use" && J.includes(V.name));
  if (C.length === 0 || X.length === 0) return G("missing_file_edits"), !1;
  try {
    if (me6(C, X, Q.options.tools)) return G("contents_identical"), !1
  } catch {
    return G("tool_use_invalid_with_error"), !1
  }
  return I(), !0
}

function WF2(A) {
  if (!A || A.trim() === "") return `Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing development work without losing context.

Before providing your final summary, wrap your analysis in <analysis> tags to organize your thoughts and ensure you've covered all necessary points. In your analysis process:

1. Chronologically analyze each message and section of the conversation. For each section thoroughly identify:
   - The user's explicit requests and intents
   - Your approach to addressing the user's requests
   - Key decisions, technical concepts and code patterns
   - Specific details like file names, full code snippets, function signatures, file edits, etc
2. Double-check for technical accuracy and completeness, addressing each required element thoroughly.

Your summary should include the following sections:

1. Primary Request and Intent: Capture all of the user's explicit requests and intents in detail
2. Key Technical Concepts: List all important technical concepts, technologies, and frameworks discussed.
3. Files and Code Sections: Enumerate specific files and code sections examined, modified, or created. Pay special attention to the most recent messages and include full code snippets where applicable and include a summary of why this file read or edit is important.
4. Problem Solving: Document problems solved and any ongoing troubleshooting efforts.
5. Pending Tasks: Outline any pending tasks that you have explicitly been asked to work on.
6. Current Work: Describe in detail precisely what was being worked on immediately before this summary request, paying special attention to the most recent messages from both user and assistant. Include file names and code snippets where applicable.
7. Optional Next Step: List the next step that you will take that is related to the most recent work you were doing. IMPORTANT: ensure that this step is DIRECTLY in line with the user's explicit requests, and the task you were working on immediately before this summary request. If your last task was concluded, then only list next steps if they are explicitly in line with the users request. Do not start on tangential requests without confirming with the user first.
                       If there is a next step, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no drift in task interpretation.

Here's an example of how your output should be structured:

<example>
<analysis>
[Your thought process, ensuring all points are covered thoroughly and accurately]
</analysis>

<summary>
1. Primary Request and Intent:
   [Detailed description]

2. Key Technical Concepts:
   - [Concept 1]
   - [Concept 2]
   - [...]

3. Files and Code Sections:
   - [File Name 1]
      - [Summary of why this file is important]
      - [Summary of the changes made to this file, if any]
      - [Important Code Snippet]
   - [File Name 2]
      - [Important Code Snippet]
   - [...]

4. Problem Solving:
   [Description of solved problems and ongoing troubleshooting]

5. Pending Tasks:
   - [Task 1]
   - [Task 2]
   - [...]

6. Current Work:
   [Precise description of current work]

7. Optional Next Step:
   [Optional Next step to take]

</summary>
</example>

Please provide your summary based on the conversation so far, following this structure and ensuring precision and thoroughness in your response. 

There may be additional summarization instructions provided in the included context. If so, remember to follow these instructions when creating the above summary. Examples of instructions include:
<example>
## Compact Instructions
When summarizing the conversation focus on typescript code changes and also remember the mistakes you made and how you fixed them.
</example>

<example>
# Summary instructions
When you are using compact - please focus on test output and code changes. Include file reads verbatim.
</example>
`;
  return `Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing development work without losing context.

Before providing your final summary, wrap your analysis in <analysis> tags to organize your thoughts and ensure you've covered all necessary points. In your analysis process:

1. Chronologically analyze each message and section of the conversation. For each section thoroughly identify:
   - The user's explicit requests and intents
   - Your approach to addressing the user's requests
   - Key decisions, technical concepts and code patterns
   - Specific details like file names, full code snippets, function signatures, file edits, etc
2. Double-check for technical accuracy and completeness, addressing each required element thoroughly.

Your summary should include the following sections:

1. Primary Request and Intent: Capture all of the user's explicit requests and intents in detail
2. Key Technical Concepts: List all important technical concepts, technologies, and frameworks discussed.
3. Files and Code Sections: Enumerate specific files and code sections examined, modified, or created. Pay special attention to the most recent messages and include full code snippets where applicable and include a summary of why this file read or edit is important.
4. Problem Solving: Document problems solved and any ongoing troubleshooting efforts.
5. Pending Tasks: Outline any pending tasks that you have explicitly been asked to work on.
6. Current Work: Describe in detail precisely what was being worked on immediately before this summary request, paying special attention to the most recent messages from both user and assistant. Include file names and code snippets where applicable.
7. Optional Next Step: List the next step that you will take that is related to the most recent work you were doing. IMPORTANT: ensure that this step is DIRECTLY in line with the user's explicit requests, and the task you were working on immediately before this summary request. If your last task was concluded, then only list next steps if they are explicitly in line with the users request. Do not start on tangential requests without confirming with the user first.
                       If there is a next step, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no drift in task interpretation.

Here's an example of how your output should be structured:

<example>
<analysis>
[Your thought process, ensuring all points are covered thoroughly and accurately]
</analysis>

<summary>
1. Primary Request and Intent:
   [Detailed description]

2. Key Technical Concepts:
   - [Concept 1]
   - [Concept 2]
   - [...]

3. Files and Code Sections:
   - [File Name 1]
      - [Summary of why this file is important]
      - [Summary of the changes made to this file, if any]
      - [Important Code Snippet]
   - [File Name 2]
      - [Important Code Snippet]
   - [...]

4. Problem Solving:
   [Description of solved problems and ongoing troubleshooting]

5. Pending Tasks:
   - [Task 1]
   - [Task 2]
   - [...]

6. Current Work:
   [Precise description of current work]

7. Optional Next Step:
   [Optional Next step to take]

</summary>
</example>

Please provide your summary based on the conversation so far, following this structure and ensuring precision and thoroughness in your response. 

There may be additional summarization instructions provided in the included context. If so, remember to follow these instructions when creating the above summary. Examples of instructions include:
<example>
## Compact Instructions
When summarizing the conversation focus on typescript code changes and also remember the mistakes you made and how you fixed them.
</example>

<example>
# Summary instructions
When you are using compact - please focus on test output and code changes. Include file reads verbatim.
</example>


Additional Instructions:
${A}`
}

function FF2(A, B) {
  let Q = `This session is being continued from a previous conversation that ran out of context. The conversation is summarized below:
${A}.`;
  if (B)
  return `${Q}
Please continue the conversation from where we left it off without asking the user any further questions. Continue with the last task that you were asked to work on.`;
  return Q
}

function ue6() {
  return null;
  if (b1().existsSync(A)) try {
    return b1().readFileSync(A, {
      encoding: "utf8"
    }).trim()
  } catch {
    return null
  }
}
var pe6 = b0(async () => {
  return null
});
async function JF2(A) {
  return
}
var Rt = "Not enough messages to compact.",
  ce6 = "Conversation too long. Press esc to go up a few messages and try again.",
  Gs1 = "API Error: Request was aborted.";
async function yX1(A, B, Q, I) {
  try {
    if (A.length === 0) throw new Error(Rt);
    let G = HN(A);
    j1("tengu_compact", {
      preCompactTokenCount: G
    }), JF2(B.getToolPermissionContext()), B.addNotification?.({
      text: "Compacting conversation history…",
      color: "permission"
    }, {
      timeoutMs: 50000
    });
    let D = WF2(I),
      Z = M2({
        content: D
      }),
      Y = await Ut(HC([...A, Z]), ["You are a helpful AI assistant tasked with summarizing conversations."], 0, [b3],
        B.abortController.signal, {
          getToolPermissionContext: B.getToolPermissionContext,
          model: await cQ(),
          prependCLISysprompt: !0,
          toolChoice: void 0,
          isNonInteractiveSession: B.options.isNonInteractiveSession
        }),
      W = mF1(Y);
    if (!W) throw j1("tengu_compact_failed", {
      reason: "no_summary",
      preCompactTokenCount: G
    }), new Error("Failed to generate conversation summary - response did not contain valid text content");
    else if (W.startsWith(gY)) throw j1("tengu_compact_failed", {
      reason: "api_error",
      preCompactTokenCount: G
    }), new Error(W);
    else if (W.startsWith(Et)) throw j1("tengu_compact_failed", {
      reason: "prompt_too_long",
      preCompactTokenCount: G
    }), new Error(ce6);
    let F = [M2({
      content: FF2(W, Q)
    })];
    if (B.readFileState) Object.keys(B.readFileState).forEach((J) => {
      delete B.readFileState[J]
    });
    if (B.setMessages) {
      if (B.setMessages(F), B.setMessageHistory) B.setMessageHistory((J) => [...J, ...A])
    }
    return B.addNotification?.({
      text: "Conversation successfully compacted! ✨",
      color: "success"
    }, {
      timeoutMs: 5000
    }), {
      summaryMessage: Y,
      messagesAfterCompacting: F
    }
  } catch (G) {
    throw le6(G, B), G
  }
}

function le6(A, B) {
  if (qi(A, Gs1) || qi(A, Rt)) B.addNotification?.({
    text: ""
  }, {
    timeoutMs: 0
  });
  else B.addNotification?.({
    text: "Error compacting conversation",
    color: "error"
  }, {
    timeoutMs: 2000
  })
}
var CF2 = 180000,
  Tt = 0.92,
  ie6 = 0.6,
  ne6 = 0.8;

function Pt(A, B) {
  let Q = CF2 * B,
    I = Ot() ? Q : CF2,
    G = Math.max(0, Math.round((I - A) / I * 100)),
    D = I * ie6,
    Z = I * ne6,
    Y = A >= D,
    W = A >= Z,
    F = Ot() && A >= Q;
  return {
    percentLeft: G,
    isAboveWarningThreshold: Y,
    isAboveErrorThreshold: W,
    isAboveAutoCompactThreshold: F
  }
}

function Ot() {
  return VA().autoCompactEnabled
}
async function ae6(A) {
  if (!Ot()) return !1;
  let B = HN(A),
    {
      isAboveAutoCompactThreshold: Q
    } = Pt(B, Tt);
  return Q
}
async function XF2(A, B) {
  if (!await ae6(A)) return {
    messages: A,
    wasCompacted: !1
  };
  try {
    let {
      messagesAfterCompacting: I
    } = await yX1(A, B, !0, void 0);
    return {
      messages: I,
      wasCompacted: !0
    }
  } catch (I) {
    if (!qi(I, Gs1)) g1(I instanceof Error ? I : new Error(String(I)));
    return {
      messages: A,
      wasCompacted: !1
    }
  }
}
import {
  EOL as xX1
} from "os";
import {
  isAbsolute as I15,
  resolve as G15
} from "path";
var K8 = J1(_1(), 1);
var D5 = J1(_1(), 1);

function VF2({
  command: A,
  elapsedTimeSeconds: B,
  onOptionSelected: Q
}) {
  let I = $1(),
    [G, D] = D5.useState(B);
  D5.useEffect(() => {
    let F = setInterval(() => {
      D((J) => J + 1)
    }, 1000);
    return () => clearInterval(F)
  }, []);
  let Z = v2(),
    Y = [{
      label: "Continue waiting",
      value: "wait"
    }, {
      label: "Run in the background",
      value: "background"
    }, {
      label: "Kill command",
      value: "kill"
    }];

  function W(F) {
    switch (F) {
      case "wait":
        Q("wait");
        break;
      case "background":
        Q("background");
        break;
      case "kill":
        Q("kill");
        break
    }
  }
  return D5.createElement(m, {
    flexDirection: "column",
    width: "100%"
  }, D5.createElement(m, {
    flexDirection: "column",
    borderStyle: "round",
    borderColor: I.permission,
    marginTop: 1,
    paddingLeft: 1,
    paddingRight: 1,
    paddingBottom: 1,
    width: "100%"
  }, D5.createElement(m, {
    marginBottom: 1
  }, D5.createElement(y, {
    color: I.permission,
    bold: !0
  }, "Long-running command")), D5.createElement(m, {
    flexDirection: "column",
    paddingX: 1
  }, D5.createElement(y, {
    wrap: "truncate-end"
  }, N4.renderToolUseMessage({
    command: A
  }, {
    verbose: !0
  })), D5.createElement(y, null, "Running for ", D5.createElement(y, {
    bold: !0
  }, G), " seconds")), D5.createElement(m, {
    flexDirection: "column",
    marginTop: 1
  }, D5.createElement(y, null, "How do you want to proceed?"), D5.createElement(B9, {
    options: Y,
    onChange: W,
    onCancel: () => Q("wait")
  }))), D5.createElement(m, {
    marginLeft: 2
  }, Z.pending ? D5.createElement(y, {
    dimColor: !0
  }, "Press ", Z.keyName, " again to exit") : D5.createElement(y, {
    dimColor: !0
  }, "Press esc to close")))
}
var Fs1 = J1(Mp(), 1);
var Zs1 = "__SINGLE_QUOTE__",
  Ys1 = "__DOUBLE_QUOTE__",
  Ds1 = "__NEW_LINE__",
  Ws1 = new Set(["0", "1", "2"]);

function Js1(A) {
  let B = [];
  for (let G of Fs1.parse(A.replaceAll('"', `"${Ys1}`).replaceAll("'", `'${Zs1}`).replaceAll(`
`, `
${Ds1}
`), (D) => `$${D}`)) {
    if (typeof G === "string") {
      if (B.length > 0 && typeof B[B.length - 1] === "string") {
        if (G == Ds1) B.push(null);
        else B[B.length - 1] += " " + G;
        continue
      }
    } else if ("op" in G && G.op === "glob") {
      if (B.length > 0 && typeof B[B.length - 1] === "string") {
        B[B.length - 1] += " " + G.pattern;
        continue
      }
    }
    B.push(G)
  }
  return B.map((G) => {
    if (G === null) return null;
    if (typeof G === "string") return G;
    if ("comment" in G) return "#" + G.comment;
    if ("op" in G && G.op === "glob") return G.pattern;
    if ("op" in G) return G.op;
    return null
  }).filter((G) => G !== null).map((G) => {
    return G.replaceAll(`${Zs1}`, "'").replaceAll(`${Ys1}`, '"').replaceAll(`
${Ds1}
`, `
`)
  })
}

function Cs1(A) {
  return A.filter((B) => !se6.has(B))
}

function a_(A) {
  let B = Js1(A);
  for (let I = 0; I < B.length; I++) {
    let G = B[I];
    if (G === void 0 || G !== ">&") continue;
    let D = B[I - 1]?.trim(),
      Z = B[I + 1]?.trim();
    if (D === void 0 || Z === void 0) continue;
    if (Ws1.has(Z)) {
      if (Ws1.has(D.charAt(D.length - 1))) B[I - 1] = D.slice(0, -1).trim();
      B[I] = void 0, B[I + 1] = void 0
    }
  }
  let Q = B.filter((I) => I !== void 0);
  return Cs1(Q)
}
var HF2 = b0(async (A, B, Q) => {
    let I = a_(A),
      [G, ...D] = await Promise.all([KF2(A, B, Q), ...I.map(async (Y) => ({
        subcommand: Y,
        prefix: await KF2(Y, B, Q)
      }))]);
    if (!G) return null;
    let Z = D.reduce((Y, {
      subcommand: W,
      prefix: F
    }) => {
      if (F) Y.set(W, F);
      return Y
    }, new Map);
    return {
      ...G,
      subcommandPrefixes: Z
    }
  }, (A) => A),
  KF2 = b0(async (A, B, Q) => {
    let I = await KD({
        systemPrompt: [`Your task is to process Bash commands that an AI coding agent wants to run.

This policy spec defines how to determine the prefix of a Bash command:`],
        userPrompt: `<policy_spec>
# ${x0} Code Bash command prefix detection

This document defines risk levels for actions that the ${x0} agent may take. This classification system is part of a broader safety framework and is used to determine when additional user confirmation or oversight may be needed.

## Definitions

**Command Injection:** Any technique used that would result in a command being run other than the detected prefix.

## Command prefix extraction examples
Examples:
- cat foo.txt => cat
- cd src => cd
- cd path/to/files/ => cd
- find ./src -type f -name "*.ts" => find
- gg cat foo.py => gg cat
- gg cp foo.py bar.py => gg cp
- git commit -m "foo" => git commit
- git diff HEAD~1 => git diff
- git diff --staged => git diff
- git diff $(pwd) => command_injection_detected
- git status => git status
- git status# test(\`id\`) => command_injection_detected
- git status\`ls\` => command_injection_detected
- git push => none
- git push origin master => git push
- git log -n 5 => git log
- git log --oneline -n 5 => git log
- grep -A 40 "from foo.bar.baz import" alpha/beta/gamma.py => grep
- pig tail zerba.log => pig tail
- potion test some/specific/file.ts => potion test
- npm run lint => none
- npm run lint -- "foo" => npm run lint
- npm test => none
- npm test --foo => npm test
- npm test -- -f "foo" => npm test
- pwd
 curl example.com => command_injection_detected
- pytest foo/bar.py => pytest
- scalac build => none
- sleep 3 => sleep
</policy_spec>

The user has allowed certain command prefixes to be run, and will otherwise be asked to approve or deny the command.
Your task is to determine the command prefix for the following command.
The prefix must be a string prefix of the full command.

IMPORTANT: Bash commands may run multiple commands that are chained together.
For safety, if the command seems to contain command injection, you must return "command_injection_detected". 
(This will help protect the user: if they think that they're allowlisting command A, 
but the AI coding agent sends a malicious command that technically has the same prefix as command A, 
then the safety system will see that you said “command_injection_detected” and ask the user for manual confirmation.)

Note that not every command has a prefix. If a command has no prefix, return "none".

ONLY return the prefix. Do not return any other text, markdown markers, or other content or formatting.

Command: ${A}
`,
        signal: B,
        enablePromptCaching: !1,
        isNonInteractiveSession: Q
      }),
      G = typeof I.message.content === "string" ? I.message.content : Array.isArray(I.message.content) ? I.message
      .content.find((D) => D.type === "text")?.text ?? "none" : "none";
    if (G.startsWith(gY)) return j1("tengu_bash_prefix", {
      success: !1,
      error: "API error"
    }), null;
    if (G === "command_injection_detected") return j1("tengu_bash_prefix", {
      success: !1,
      commandInjectionDetected: !0
    }), {
      commandInjectionDetected: !0
    };
    if (G === "git") return j1("tengu_bash_prefix", {
      success: !1,
      error: 'prefix "git"'
    }), {
      commandPrefix: null,
      commandInjectionDetected: !1
    };
    if (G === "none") return j1("tengu_bash_prefix", {
      success: !1,
      error: 'prefix "none"'
    }), {
      commandPrefix: null,
      commandInjectionDetected: !1
    };
    if (!A.startsWith(G)) return j1("tengu_bash_prefix", {
      success: !1,
      error: "command did not start with prefix"
    }), {
      commandPrefix: null,
      commandInjectionDetected: !1
    };
    return j1("tengu_bash_prefix", {
      success: !0
    }), {
      commandPrefix: G,
      commandInjectionDetected: !1
    }
  }, (A) => A),
  zF2 = new Set(["&&", "||", ";", ";;", "|"]),
  se6 = new Set([...zF2, ">&"]);

function re6(A) {
  let B = Fs1.parse(A.replaceAll('"', `"${Ys1}`).replaceAll("'", `'${Zs1}`), (Q) => `$${Q}`);
  for (let Q = 0; Q < B.length; Q++) {
    let I = B[Q],
      G = B[Q + 1];
    if (I === void 0) continue;
    if (typeof I === "string") continue;
    if ("comment" in I) return !1;
    if ("op" in I) {
      if (I.op === "glob") continue;
      else if (zF2.has(I.op)) continue;
      else if (I.op === ">&") {
        if (G !== void 0 && typeof G === "string" && Ws1.has(G.trim())) continue
      }
      return !1
    }
  }
  return !0
}

function wF2(A) {
  return a_(A).length > 1 && !re6(A)
}
class EF2 {
  id;
  command;
  startTime;
  status;
  result;
  shellCommand;
  stdout = "";
  stderr = "";
  constructor(A, B, Q, I) {
    this.id = A;
    this.command = B;
    this.status = "running", this.startTime = Date.now(), this.shellCommand = Q;
    let G = Q.background(A);
    if (!G) this.status = "failed", this.result = {
      code: 1,
      interrupted: !1
    };
    else G.stdoutStream.on("data", (D) => {
      this.stdout += D.toString()
    }), G.stderrStream.on("data", (D) => {
      this.stderr += D.toString()
    }), Q.result.then((D) => {
      if (D.code === 0) this.status = "completed";
      else this.status = "failed";
      this.result = {
        code: D.code,
        interrupted: D.interrupted
      }, I(D)
    })
  }
  getOutput() {
    let A = {
      stdout: this.stdout,
      stderr: this.stderr
    };
    return this.stdout = "", this.stderr = "", A
  }
  hasNewOutput() {
    return !!this.stdout
  }
  kill() {
    try {
      return this.shellCommand?.kill(), !0
    } catch (A) {
      return g1(A instanceof Error ? A : new Error(String(A))), !1
    }
  }
  dispose() {
    this.shellCommand = null
  }
}
class dm {
  static instance = null;
  tasks = new Map;
  taskCounter = 0;
  subscribers = new Set;
  constructor() {}
  static getInstance() {
    if (!dm.instance) dm.instance = new dm;
    return dm.instance
  }
  subscribe(A) {
    return this.subscribers.add(A), () => {
      this.subscribers.delete(A)
    }
  }
  notifySubscribers() {
    this.subscribers.forEach((A) => {
      try {
        A()
      } catch (B) {
        g1(B)
      }
    })
  }
  addBackgroundTask(A) {
    return this.tasks.set(A.id, A), this.notifySubscribers(), A.id
  }
  completeTask(A, B) {
    let Q = this.tasks.get(A);
    if (!Q) return;
    Q.status = B.code === 0 ? "completed" : "failed", Q.result = {
      code: B.code,
      interrupted: B.interrupted
    }, this.notifySubscribers()
  }
  getAllTasks() {
    return Array.from(this.tasks.values())
  }
  getActiveTasks() {
    return Array.from(this.tasks.values()).filter((A) => A.status === "running")
  }
  getActiveTaskCount() {
    return this.getActiveTasks().length
  }
  getTask(A) {
    return this.tasks.get(A)
  }
  getTaskOutput(A) {