// Module: euA
// Lines: 71174-71342
// Purpose: networking, command_line
// Dependencies: this, suA, B, tuA, Object, k24, Array, A, I, Q

var euA = w((nd5, tuA) => {
  var {
    defineProperty: d81,
    getOwnPropertyDescriptor: j24,
    getOwnPropertyNames: y24
  } = Object, k24 = Object.prototype.hasOwnProperty, VM = (A, B) => d81(A, "name", {
    value: B,
    configurable: !0
  }), x24 = (A, B) => {
    for (var Q in B) d81(A, Q, {
      get: B[Q],
      enumerable: !0
    })
  }, f24 = (A, B, Q, I) => {
    if (B && typeof B === "object" || typeof B === "function") {
      for (let G of y24(B))
        if (!k24.call(A, G) && G !== Q) d81(A, G, {
          get: () => B[G],
          enumerable: !(I = j24(B, <PERSON>)) || I.enumerable
        })
    }
    return A
  }, v24 = (A) => f24(d81({}, "__esModule", {
    value: !0
  }), A), auA = {};
  x24(auA, {
    Field: () => h24,
    Fields: () => m24,
    HttpRequest: () => d24,
    HttpResponse: () => u24,
    IHttpRequest: () => suA.HttpRequest,
    getHttpHandlerExtensionConfiguration: () => b24,
    isValidHostname: () => ouA,
    resolveHttpHandlerRuntimeConfig: () => g24
  });
  tuA.exports = v24(auA);
  var b24 = VM((A) => {
      return {
        setHttpHandler(B) {
          A.httpHandler = B
        },
        httpHandler() {
          return A.httpHandler
        },
        updateHttpClientConfig(B, Q) {
          A.httpHandler?.updateHttpClientConfig(B, Q)
        },
        httpHandlerConfigs() {
          return A.httpHandler.httpHandlerConfigs()
        }
      }
    }, "getHttpHandlerExtensionConfiguration"),
    g24 = VM((A) => {
      return {
        httpHandler: A.httpHandler()
      }
    }, "resolveHttpHandlerRuntimeConfig"),
    suA = PT1(),
    h24 = class {
      static {
        VM(this, "Field")
      }
      constructor({
        name: A,
        kind: B = suA.FieldPosition.HEADER,
        values: Q = []
      }) {
        this.name = A, this.kind = B, this.values = Q
      }
      add(A) {
        this.values.push(A)
      }
      set(A) {
        this.values = A
      }
      remove(A) {
        this.values = this.values.filter((B) => B !== A)
      }
      toString() {
        return this.values.map((A) => A.includes(",") || A.includes(" ") ? `"${A}"` : A).join(", ")
      }
      get() {
        return this.values
      }
    },
    m24 = class {
      constructor({
        fields: A = [],
        encoding: B = "utf-8"
      }) {
        this.entries = {}, A.forEach(this.setField.bind(this)), this.encoding = B
      }
      static {
        VM(this, "Fields")
      }
      setField(A) {
        this.entries[A.name.toLowerCase()] = A
      }
      getField(A) {
        return this.entries[A.toLowerCase()]
      }
      removeField(A) {
        delete this.entries[A.toLowerCase()]
      }
      getByType(A) {
        return Object.values(this.entries).filter((B) => B.kind === A)
      }
    },
    d24 = class A {
      static {
        VM(this, "HttpRequest")
      }
      constructor(B) {
        this.method = B.method || "GET", this.hostname = B.hostname || "localhost", this.port = B.port, this
          .query = B.query || {}, this.headers = B.headers || {}, this.body = B.body, this.protocol = B.protocol ?
          B.protocol.slice(-1) !== ":" ? `${B.protocol}:` : B.protocol : "https:", this.path = B.path ? B.path
          .charAt(0) !== "/" ? `/${B.path}` : B.path : "/", this.username = B.username, this.password = B
          .password, this.fragment = B.fragment
      }
      static clone(B) {
        let Q = new A({
          ...B,
          headers: {
            ...B.headers
          }
        });
        if (Q.query) Q.query = ruA(Q.query);
        return Q
      }
      static isInstance(B) {
        if (!B) return !1;
        let Q = B;
        return "method" in Q && "protocol" in Q && "hostname" in Q && "path" in Q && typeof Q.query ===
          "object" && typeof Q.headers === "object"
      }
      clone() {
        return A.clone(this)
      }
    };

  function ruA(A) {
    return Object.keys(A).reduce((B, Q) => {
      let I = A[Q];
      return {
        ...B,
        [Q]: Array.isArray(I) ? [...I] : I
      }
    }, {})
  }
  VM(ruA, "cloneQuery");
  var u24 = class {
    static {
      VM(this, "HttpResponse")
    }
    constructor(A) {
      this.statusCode = A.statusCode, this.reason = A.reason, this.headers = A.headers || {}, this.body = A.body
    }
    static isInstance(A) {
      if (!A) return !1;
      let B = A;
      return typeof B.statusCode === "number" && typeof B.headers === "object"
    }
  };

  function ouA(A) {
    return /^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)
  }
  VM(ouA, "isValidHostname")
});