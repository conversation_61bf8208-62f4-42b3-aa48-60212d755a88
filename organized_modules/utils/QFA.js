// Module: qfA
// Lines: 62151-62184
// Purpose: utility
// Dependencies: jl9, fA, Object, A, I

var qfA = w((Gm5, $fA) => {
  var {
    defineProperty: O51,
    getOwnPropertyDescriptor: Sl9,
    getOwnPropertyNames: _l9
  } = Object, jl9 = Object.prototype.hasOwnProperty, JO1 = (A, B) => O51(A, "name", {
    value: B,
    configurable: !0
  }), yl9 = (A, B) => {
    for (var Q in B) O51(A, Q, {
      get: B[Q],
      enumerable: !0
    })
  }, kl9 = (A, B, Q, I) => {
    if (B && typeof B === "object" || typeof B === "function") {
      for (let G of _l9(B))
        if (!jl9.call(A, G) && G !== Q) O51(A, G, {
          get: () => B[G],
          enumerable: !(I = Sl9(B, G)) || I.enumerable
        })
    }
    return A
  }, xl9 = (A) => kl9(O51({}, "__esModule", {
    value: !0
  }), A), UfA = {};
  yl9(UfA, {
    escapeUri: () => NfA,
    escapeUriPath: () => vl9
  });
  $fA.exports = xl9(UfA);
  var NfA = JO1((A) => encodeURIComponent(A).replace(/[!'()*]/g, fl9), "escapeUri"),
    fl9 = JO1((A) => `%${A.charCodeAt(0).toString(16).toUpperCase()}`, "hexEncode"),
    vl9 = JO1((A) => A.split("/").map(NfA).join("/"), "escapeUriPath")
});