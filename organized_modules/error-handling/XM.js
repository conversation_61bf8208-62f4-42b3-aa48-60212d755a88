// Module: XM
// Lines: 70514-70652
// Purpose: error_tracking, file_operations, ai_integration
// Dependencies: G, E04, V04, z04, I, Object, j81, A, Z04, N04, Promise, B, DuA, process, w04, D, WuA, Tl, y81, C04, Q

var XM = w((hd5, Tl) => {
  var {
    defineProperty: k81,
    getOwnPropertyDescriptor: G04,
    getOwnPropertyNames: D04
  } = Object, Z04 = Object.prototype.hasOwnProperty, LJ = (A, B) => k81(A, "name", {
    value: B,
    configurable: !0
  }), Y04 = (A, B) => {
    for (var Q in B) k81(A, Q, {
      get: B[Q],
      enumerable: !0
    })
  }, qT1 = (A, B, Q, I) => {
    if (B && typeof B === "object" || typeof B === "function") {
      for (let G of D04(B))
        if (!Z04.call(A, G) && G !== Q) k81(A, G, {
          get: () => B[G],
          enumerable: !(I = G04(B, G)) || I.enumerable
        })
    }
    return A
  }, LT1 = (A, B, Q) => (qT1(A, B, "default"), Q && qT1(Q, B, "default")), W04 = (A) => qT1(k81({},
  "__esModule", {
    value: !0
  }), A), Ol = {};
  Y04(Ol, {
    CONFIG_PREFIX_SEPARATOR: () => wP,
    DEFAULT_PROFILE: () => CuA,
    ENV_PROFILE: () => JuA,
    getProfileName: () => F04,
    loadSharedConfigFiles: () => VuA,
    loadSsoSessionData: () => q04,
    parseKnownFiles: () => L04
  });
  Tl.exports = W04(Ol);
  LT1(Ol, Pf(), Tl.exports);
  var JuA = "AWS_PROFILE",
    CuA = "default",
    F04 = LJ((A) => A.profile || process.env[JuA] || CuA, "getProfileName");
  LT1(Ol, UT1(), Tl.exports);
  LT1(Ol, sdA(), Tl.exports);
  var j81 = DuA(),
    J04 = LJ((A) => Object.entries(A).filter(([B]) => {
      let Q = B.indexOf(wP);
      if (Q === -1) return !1;
      return Object.values(j81.IniSectionType).includes(B.substring(0, Q))
    }).reduce((B, [Q, I]) => {
      let G = Q.indexOf(wP),
        D = Q.substring(0, G) === j81.IniSectionType.PROFILE ? Q.substring(G + 1) : Q;
      return B[D] = I, B
    }, {
      ...A.default && {
        default: A.default
      }
    }), "getConfigData"),
    y81 = D1("path"),
    C04 = Pf(),
    X04 = "AWS_CONFIG_FILE",
    XuA = LJ(() => process.env[X04] || y81.join(C04.getHomeDir(), ".aws", "config"), "getConfigFilepath"),
    V04 = Pf(),
    K04 = "AWS_SHARED_CREDENTIALS_FILE",
    H04 = LJ(() => process.env[K04] || y81.join(V04.getHomeDir(), ".aws", "credentials"), "getCredentialsFilepath"),
    z04 = Pf(),
    w04 = /^([\w-]+)\s(["'])?([\w-@\+\.%:/]+)\2$/,
    E04 = ["__proto__", "profile __proto__"],
    MT1 = LJ((A) => {
      let B = {},
        Q, I;
      for (let G of A.split(/\r?\n/)) {
        let D = G.split(/(^|\s)[;#]/)[0].trim();
        if (D[0] === "[" && D[D.length - 1] === "]") {
          Q = void 0, I = void 0;
          let Y = D.substring(1, D.length - 1),
            W = w04.exec(Y);
          if (W) {
            let [, F, , J] = W;
            if (Object.values(j81.IniSectionType).includes(F)) Q = [F, J].join(wP)
          } else Q = Y;
          if (E04.includes(Y)) throw new Error(`Found invalid profile name "${Y}"`)
        } else if (Q) {
          let Y = D.indexOf("=");
          if (![0, -1].includes(Y)) {
            let [W, F] = [D.substring(0, Y).trim(), D.substring(Y + 1).trim()];
            if (F === "") I = W;
            else {
              if (I && G.trimStart() === G) I = void 0;
              B[Q] = B[Q] || {};
              let J = I ? [I, W].join(wP) : W;
              B[Q][J] = F
            }
          }
        }
      }
      return B
    }, "parseIni"),
    WuA = $T1(),
    FuA = LJ(() => ({}), "swallowError"),
    wP = ".",
    VuA = LJ(async (A = {}) => {
      let {
        filepath: B = H04(),
        configFilepath: Q = XuA()
      } = A, I = z04.getHomeDir(), G = "~/", D = B;
      if (B.startsWith("~/")) D = y81.join(I, B.slice(2));
      let Z = Q;
      if (Q.startsWith("~/")) Z = y81.join(I, Q.slice(2));
      let Y = await Promise.all([WuA.slurpFile(Z, {
        ignoreCache: A.ignoreCache
      }).then(MT1).then(J04).catch(FuA), WuA.slurpFile(D, {
        ignoreCache: A.ignoreCache
      }).then(MT1).catch(FuA)]);
      return {
        configFile: Y[0],
        credentialsFile: Y[1]
      }
    }, "loadSharedConfigFiles"),
    U04 = LJ((A) => Object.entries(A).filter(([B]) => B.startsWith(j81.IniSectionType.SSO_SESSION + wP)).reduce((B,
      [Q, I]) => ({
      ...B,
      [Q.substring(Q.indexOf(wP) + 1)]: I
    }), {}), "getSsoSessionData"),
    N04 = $T1(),
    $04 = LJ(() => ({}), "swallowError"),
    q04 = LJ(async (A = {}) => N04.slurpFile(A.configFilepath ?? XuA()).then(MT1).then(U04).catch($04),
      "loadSsoSessionData"),
    M04 = LJ((...A) => {
      let B = {};
      for (let Q of A)
        for (let [I, G] of Object.entries(Q))
          if (B[I] !== void 0) Object.assign(B[I], G);
          else B[I] = G;
      return B
    }, "mergeConfigFiles"),
    L04 = LJ(async (A) => {
      let B = await VuA(A);
      return M04(B.configFile, B.credentialsFile)
    }, "parseKnownFiles")
});