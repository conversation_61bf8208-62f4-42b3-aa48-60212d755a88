// Module: jMA
// Lines: 43506-43861
// Purpose: error_tracking, file_operations, ai_integration
// Dependencies: N1, a1, Y1, I1, A1, g, A, _MA, Z, ZM1

var jMA = w((E_5, _MA) => {
  function OMA(A) {
    if (!A) return null;
    if (typeof A === "string") return A;
    return A.source
  }

  function zx(A) {
    return O8("(?=", A, ")")
  }

  function O8(...A) {
    return A.map((Q) => OMA(Q)).join("")
  }

  function rD(...A) {
    return "(" + A.map((Q) => OMA(Q)).join("|") + ")"
  }
  var FM1 = (A) => O8(/\b/, A, /\w$/.test(A) ? /\b/ : /\B/),
    MMA = ["Protocol", "Type"].map(FM1),
    DM1 = ["init", "self"].map(FM1),
    J_9 = ["Any", "Self"],
    ZM1 = ["associatedtype", "async", "await", /as\?/, /as!/, "as", "break", "case", "catch", "class", "continue",
      "convenience", "default", "defer", "deinit", "didSet", "do", "dynamic", "else", "enum", "extension",
      "fallthrough", /fileprivate\(set\)/, "fileprivate", "final", "for", "func", "get", "guard", "if", "import",
      "indirect", "infix", /init\?/, /init!/, "inout", /internal\(set\)/, "internal", "in", "is", "lazy", "let",
      "mutating", "nonmutating", /open\(set\)/, "open", "operator", "optional", "override", "postfix",
      "precedencegroup", "prefix", /private\(set\)/, "private", "protocol", /public\(set\)/, "public", "repeat",
      "required", "rethrows", "return", "set", "some", "static", "struct", "subscript", "super", "switch", "throws",
      "throw", /try\?/, /try!/, "try", "typealias", /unowned\(safe\)/, /unowned\(unsafe\)/, "unowned", "var",
      "weak", "where", "while", "willSet"
    ],
    LMA = ["false", "nil", "true"],
    C_9 = ["assignment", "associativity", "higherThan", "left", "lowerThan", "none", "right"],
    X_9 = ["#colorLiteral", "#column", "#dsohandle", "#else", "#elseif", "#endif", "#error", "#file", "#fileID",
      "#fileLiteral", "#filePath", "#function", "#if", "#imageLiteral", "#keyPath", "#line", "#selector",
      "#sourceLocation", "#warn_unqualified_access", "#warning"
    ],
    RMA = ["abs", "all", "any", "assert", "assertionFailure", "debugPrint", "dump", "fatalError", "getVaList",
      "isKnownUniquelyReferenced", "max", "min", "numericCast", "pointwiseMax", "pointwiseMin", "precondition",
      "preconditionFailure", "print", "readLine", "repeatElement", "sequence", "stride", "swap",
      "swift_unboxFromSwiftValueWithType", "transcode", "type", "unsafeBitCast", "unsafeDowncast",
      "withExtendedLifetime", "withUnsafeMutablePointer", "withUnsafePointer", "withVaList",
      "withoutActuallyEscaping", "zip"
    ],
    TMA = rD(/[/=\-+!*%<>&|^~?]/, /[\u00A1-\u00A7]/, /[\u00A9\u00AB]/, /[\u00AC\u00AE]/, /[\u00B0\u00B1]/,
      /[\u00B6\u00BB\u00BF\u00D7\u00F7]/, /[\u2016-\u2017]/, /[\u2020-\u2027]/, /[\u2030-\u203E]/,
      /[\u2041-\u2053]/, /[\u2055-\u205E]/, /[\u2190-\u23FF]/, /[\u2500-\u2775]/, /[\u2794-\u2BFF]/,
      /[\u2E00-\u2E7F]/, /[\u3001-\u3003]/, /[\u3008-\u3020]/, /[\u3030]/),
    PMA = rD(TMA, /[\u0300-\u036F]/, /[\u1DC0-\u1DFF]/, /[\u20D0-\u20FF]/, /[\uFE00-\uFE0F]/, /[\uFE20-\uFE2F]/),
    YM1 = O8(TMA, PMA, "*"),
    SMA = rD(/[a-zA-Z_]/, /[\u00A8\u00AA\u00AD\u00AF\u00B2-\u00B5\u00B7-\u00BA]/,
      /[\u00BC-\u00BE\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]/,
      /[\u0100-\u02FF\u0370-\u167F\u1681-\u180D\u180F-\u1DBF]/, /[\u1E00-\u1FFF]/,
      /[\u200B-\u200D\u202A-\u202E\u203F-\u2040\u2054\u2060-\u206F]/,
      /[\u2070-\u20CF\u2100-\u218F\u2460-\u24FF\u2776-\u2793]/, /[\u2C00-\u2DFF\u2E80-\u2FFF]/,
      /[\u3004-\u3007\u3021-\u302F\u3031-\u303F\u3040-\uD7FF]/,
      /[\uF900-\uFD3D\uFD40-\uFDCF\uFDF0-\uFE1F\uFE30-\uFE44]/, /[\uFE47-\uFEFE\uFF00-\uFFFD]/),
    C41 = rD(SMA, /\d/, /[\u0300-\u036F\u1DC0-\u1DFF\u20D0-\u20FF\uFE20-\uFE2F]/),
    lE = O8(SMA, C41, "*"),
    WM1 = O8(/[A-Z]/, C41, "*"),
    V_9 = ["autoclosure", O8(/convention\(/, rD("swift", "block", "c"), /\)/), "discardableResult",
      "dynamicCallable", "dynamicMemberLookup", "escaping", "frozen", "GKInspectable", "IBAction", "IBDesignable",
      "IBInspectable", "IBOutlet", "IBSegueAction", "inlinable", "main", "nonobjc", "NSApplicationMain",
      "NSCopying", "NSManaged", O8(/objc\(/, lE, /\)/), "objc", "objcMembers", "propertyWrapper",
      "requires_stored_property_inits", "testable", "UIApplicationMain", "unknown", "usableFromInline"
    ],
    K_9 = ["iOS", "iOSApplicationExtension", "macOS", "macOSApplicationExtension", "macCatalyst",
      "macCatalystApplicationExtension", "watchOS", "watchOSApplicationExtension", "tvOS",
      "tvOSApplicationExtension", "swift"
    ];

  function H_9(A) {
    let B = {
        match: /\s+/,
        relevance: 0
      },
      Q = A.COMMENT("/\\*", "\\*/", {
        contains: ["self"]
      }),
      I = [A.C_LINE_COMMENT_MODE, Q],
      G = {
        className: "keyword",
        begin: O8(/\./, zx(rD(...MMA, ...DM1))),
        end: rD(...MMA, ...DM1),
        excludeBegin: !0
      },
      D = {
        match: O8(/\./, rD(...ZM1)),
        relevance: 0
      },
      Z = ZM1.filter((A1) => typeof A1 === "string").concat(["_|0"]),
      Y = ZM1.filter((A1) => typeof A1 !== "string").concat(J_9).map(FM1),
      W = {
        variants: [{
          className: "keyword",
          match: rD(...Y, ...DM1)
        }]
      },
      F = {
        $pattern: rD(/\b\w+/, /#\w+/),
        keyword: Z.concat(X_9),
        literal: LMA
      },
      J = [G, D, W],
      C = {
        match: O8(/\./, rD(...RMA)),
        relevance: 0
      },
      X = {
        className: "built_in",
        match: O8(/\b/, rD(...RMA), /(?=\()/)
      },
      V = [C, X],
      K = {
        match: /->/,
        relevance: 0
      },
      U = {
        className: "operator",
        relevance: 0,
        variants: [{
          match: YM1
        }, {
          match: `\\.(\\.|${PMA})+`
        }]
      },
      N = [K, U],
      q = "([0-9]_*)+",
      M = "([0-9a-fA-F]_*)+",
      R = {
        className: "number",
        relevance: 0,
        variants: [{
          match: "\\b(([0-9]_*)+)(\\.(([0-9]_*)+))?([eE][+-]?(([0-9]_*)+))?\\b"
        }, {
          match: "\\b0x(([0-9a-fA-F]_*)+)(\\.(([0-9a-fA-F]_*)+))?([pP][+-]?(([0-9]_*)+))?\\b"
        }, {
          match: /\b0o([0-7]_*)+\b/
        }, {
          match: /\b0b([01]_*)+\b/
        }]
      },
      T = (A1 = "") => ({
        className: "subst",
        variants: [{
          match: O8(/\\/, A1, /[0\\tnr"']/)
        }, {
          match: O8(/\\/, A1, /u\{[0-9a-fA-F]{1,8}\}/)
        }]
      }),
      O = (A1 = "") => ({
        className: "subst",
        match: O8(/\\/, A1, /[\t ]*(?:[\r\n]|\r\n)/)
      }),
      S = (A1 = "") => ({
        className: "subst",
        label: "interpol",
        begin: O8(/\\/, A1, /\(/),
        end: /\)/
      }),
      f = (A1 = "") => ({
        begin: O8(A1, /"""/),
        end: O8(/"""/, A1),
        contains: [T(A1), O(A1), S(A1)]
      }),
      a = (A1 = "") => ({
        begin: O8(A1, /"/),
        end: O8(/"/, A1),
        contains: [T(A1), S(A1)]
      }),
      g = {
        className: "string",
        variants: [f(), f("#"), f("##"), f("###"), a(), a("#"), a("##"), a("###")]
      },
      Y1 = {
        match: O8(/`/, lE, /`/)
      },
      r = {
        className: "variable",
        match: /\$\d+/
      },
      w1 = {
        className: "variable",
        match: `\\$${C41}+`
      },
      H1 = [Y1, r, w1],
      x = {
        match: /(@|#)available/,
        className: "keyword",
        starts: {
          contains: [{
            begin: /\(/,
            end: /\)/,
            keywords: K_9,
            contains: [...N, R, g]
          }]
        }
      },
      F1 = {
        className: "keyword",
        match: O8(/@/, rD(...V_9))
      },
      x1 = {
        className: "meta",
        match: O8(/@/, lE)
      },
      o1 = [x, F1, x1],
      a1 = {
        match: zx(/\b[A-Z]/),
        relevance: 0,
        contains: [{
          className: "type",
          match: O8(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, C41, "+")
        }, {
          className: "type",
          match: WM1,
          relevance: 0
        }, {
          match: /[?!]+/,
          relevance: 0
        }, {
          match: /\.\.\./,
          relevance: 0
        }, {
          match: O8(/\s+&\s+/, zx(WM1)),
          relevance: 0
        }]
      },
      PA = {
        begin: /</,
        end: />/,
        keywords: F,
        contains: [...I, ...J, ...o1, K, a1]
      };
    a1.contains.push(PA);
    let cA = {
        match: O8(lE, /\s*:/),
        keywords: "_|0",
        relevance: 0
      },
      FA = {
        begin: /\(/,
        end: /\)/,
        relevance: 0,
        keywords: F,
        contains: ["self", cA, ...I, ...J, ...V, ...N, R, g, ...H1, ...o1, a1]
      },
      f1 = {
        beginKeywords: "func",
        contains: [{
          className: "title",
          match: rD(Y1.match, lE, YM1),
          endsParent: !0,
          relevance: 0
        }, B]
      },
      B1 = {
        begin: /</,
        end: />/,
        contains: [...I, a1]
      },
      v1 = {
        begin: rD(zx(O8(lE, /\s*:/)), zx(O8(lE, /\s+/, lE, /\s*:/))),
        end: /:/,
        relevance: 0,
        contains: [{
          className: "keyword",
          match: /\b_\b/
        }, {
          className: "params",
          match: lE
        }]
      },
      M1 = {
        begin: /\(/,
        end: /\)/,
        keywords: F,
        contains: [v1, ...I, ...J, ...N, R, g, ...o1, a1, FA],
        endsParent: !0,
        illegal: /["']/
      },
      AA = {
        className: "function",
        match: zx(/\bfunc\b/),
        contains: [f1, B1, M1, B],
        illegal: [/\[/, /%/]
      },
      NA = {
        className: "function",
        match: /\b(subscript|init[?!]?)\s*(?=[<(])/,
        keywords: {
          keyword: "subscript init init? init!",
          $pattern: /\w+[?!]?/
        },
        contains: [B1, M1, B],
        illegal: /\[|%/
      },
      OA = {
        beginKeywords: "operator",
        end: A.MATCH_NOTHING_RE,
        contains: [{
          className: "title",
          match: YM1,
          endsParent: !0,
          relevance: 0
        }]
      },
      o = {
        beginKeywords: "precedencegroup",
        end: A.MATCH_NOTHING_RE,
        contains: [{
          className: "title",
          match: WM1,
          relevance: 0
        }, {
          begin: /{/,
          end: /}/,
          relevance: 0,
          endsParent: !0,
          keywords: [...C_9, ...LMA],
          contains: [a1]
        }]
      };
    for (let A1 of g.variants) {
      let I1 = A1.contains.find((N1) => N1.label === "interpol");
      I1.keywords = F;
      let E1 = [...J, ...V, ...N, R, g, ...H1];
      I1.contains = [...E1, {
        begin: /\(/,
        end: /\)/,
        contains: ["self", ...E1]
      }]
    }
    return {
      name: "Swift",
      keywords: F,
      contains: [...I, AA, NA, {
        className: "class",
        beginKeywords: "struct protocol class extension enum",
        end: "\\{",
        excludeEnd: !0,
        keywords: F,
        contains: [A.inherit(A.TITLE_MODE, {
          begin: /[A-Za-z$_][\u00C0-\u02B80-9A-Za-z$_]*/
        }), ...J]
      }, OA, o, {
        beginKeywords: "import",
        end: /$/,
        contains: [...I],
        relevance: 0
      }, ...J, ...V, ...N, R, g, ...H1, ...o1, a1, FA]
    }
  }
  _MA.exports = H_9
});