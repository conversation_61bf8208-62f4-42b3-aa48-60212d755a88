// Module: tiA
// Lines: 75189-76066
// Purpose: error_tracking, file_operations, networking, command_line, version_control, ai_integration
// Dependencies: console, G, J, viA, FB4, Math, I, P1, XB4, Z, VB4, String, ZU, Object, C, Int8Array, A, Symbol, JSON, this, Date, ml, B, CP1, U, Array, CB4, Int16Array, oiA, K, Int32Array, Y, Number, YB4, VP1, b84, constructor, Q, NP

var tiA = w((mu5, oiA) => {
  var {
    defineProperty: XB1,
    getOwnPropertyDescriptor: f84,
    getOwnPropertyNames: v84
  } = Object, b84 = Object.prototype.hasOwnProperty, i0 = (A, B) => XB1(A, "name", {
    value: B,
    configurable: !0
  }), g84 = (A, B) => {
    for (var Q in B) XB1(A, Q, {
      get: B[Q],
      enumerable: !0
    })
  }, h84 = (A, B, Q, I) => {
    if (B && typeof B === "object" || typeof B === "function") {
      for (let G of v84(B))
        if (!b84.call(A, G) && G !== Q) XB1(A, G, {
          get: () => B[G],
          enumerable: !(I = f84(B, G)) || I.enumerable
        })
    }
    return A
  }, m84 = (A) => h84(XB1({}, "__esModule", {
    value: !0
  }), A), fiA = {};
  g84(fiA, {
    Client: () => d84,
    Command: () => biA,
    LazyJsonString: () => NP,
    NoOpLogger: () => bB4,
    SENSITIVE_STRING: () => p84,
    ServiceException: () => LB4,
    _json: () => wP1,
    collectBody: () => CP1.collectBody,
    convertMap: () => gB4,
    createAggregatedClient: () => c84,
    dateToUtcString: () => piA,
    decorateServiceException: () => ciA,
    emitWarningIfUnsupportedVersion: () => PB4,
    expectBoolean: () => i84,
    expectByte: () => zP1,
    expectFloat32: () => JB1,
    expectInt: () => a84,
    expectInt32: () => KP1,
    expectLong: () => hl,
    expectNonNull: () => r84,
    expectNumber: () => gl,
    expectObject: () => giA,
    expectShort: () => HP1,
    expectString: () => o84,
    expectUnion: () => t84,
    extendedEncodeURIComponent: () => CP1.extendedEncodeURIComponent,
    getArrayIfSingleItem: () => fB4,
    getDefaultClientConfiguration: () => kB4,
    getDefaultExtensionConfiguration: () => iiA,
    getValueFromTextNode: () => niA,
    handleFloat: () => BB4,
    isSerializableHeaderValue: () => vB4,
    limitedParseDouble: () => NP1,
    limitedParseFloat: () => QB4,
    limitedParseFloat32: () => IB4,
    loadConfigsForDefaultMode: () => TB4,
    logger: () => ml,
    map: () => qP1,
    parseBoolean: () => l84,
    parseEpochTimestamp: () => HB4,
    parseRfc3339DateTime: () => WB4,
    parseRfc3339DateTimeWithOffset: () => JB4,
    parseRfc7231DateTime: () => KB4,
    quoteHeader: () => siA,
    resolveDefaultRuntimeConfig: () => xB4,
    resolvedPath: () => CP1.resolvedPath,
    serializeDateTime: () => cB4,
    serializeFloat: () => pB4,
    splitEvery: () => riA,
    splitHeader: () => lB4,
    strictParseByte: () => uiA,
    strictParseDouble: () => UP1,
    strictParseFloat: () => e84,
    strictParseFloat32: () => hiA,
    strictParseInt: () => GB4,
    strictParseInt32: () => DB4,
    strictParseLong: () => diA,
    strictParseShort: () => gf,
    take: () => hB4,
    throwDefaultError: () => liA,
    withBaseException: () => RB4
  });
  oiA.exports = m84(fiA);
  var viA = ZU(),
    d84 = class {
      constructor(A) {
        this.config = A, this.middlewareStack = viA.constructStack()
      }
      static {
        i0(this, "Client")
      }
      send(A, B, Q) {
        let I = typeof B !== "function" ? B : void 0,
          G = typeof B === "function" ? B : Q,
          D = I === void 0 && this.config.cacheMiddleware === !0,
          Z;
        if (D) {
          if (!this.handlers) this.handlers = new WeakMap;
          let Y = this.handlers;
          if (Y.has(A.constructor)) Z = Y.get(A.constructor);
          else Z = A.resolveMiddleware(this.middlewareStack, this.config, I), Y.set(A.constructor, Z)
        } else delete this.handlers, Z = A.resolveMiddleware(this.middlewareStack, this.config, I);
        if (G) Z(A).then((Y) => G(null, Y.output), (Y) => G(Y)).catch(() => {});
        else return Z(A).then((Y) => Y.output)
      }
      destroy() {
        this.config?.requestHandler?.destroy?.(), delete this.handlers
      }
    },
    CP1 = bH(),
    VP1 = JP1(),
    biA = class {
      constructor() {
        this.middlewareStack = viA.constructStack()
      }
      static {
        i0(this, "Command")
      }
      static classBuilder() {
        return new u84
      }
      resolveMiddlewareWithContext(A, B, Q, {
        middlewareFn: I,
        clientName: G,
        commandName: D,
        inputFilterSensitiveLog: Z,
        outputFilterSensitiveLog: Y,
        smithyContext: W,
        additionalContext: F,
        CommandCtor: J
      }) {
        for (let U of I.bind(this)(J, A, B, Q)) this.middlewareStack.use(U);
        let C = A.concat(this.middlewareStack),
          {
            logger: X
          } = B,
          V = {
            logger: X,
            clientName: G,
            commandName: D,
            inputFilterSensitiveLog: Z,
            outputFilterSensitiveLog: Y,
            [VP1.SMITHY_CONTEXT_KEY]: {
              commandInstance: this,
              ...W
            },
            ...F
          },
          {
            requestHandler: K
          } = B;
        return C.resolve((U) => K.handle(U.request, Q || {}), V)
      }
    },
    u84 = class {
      constructor() {
        this._init = () => {}, this._ep = {}, this._middlewareFn = () => [], this._commandName = "", this
          ._clientName = "", this._additionalContext = {}, this._smithyContext = {}, this
          ._inputFilterSensitiveLog = (A) => A, this._outputFilterSensitiveLog = (A) => A, this._serializer =
          null, this._deserializer = null
      }
      static {
        i0(this, "ClassBuilder")
      }
      init(A) {
        this._init = A
      }
      ep(A) {
        return this._ep = A, this
      }
      m(A) {
        return this._middlewareFn = A, this
      }
      s(A, B, Q = {}) {
        return this._smithyContext = {
          service: A,
          operation: B,
          ...Q
        }, this
      }
      c(A = {}) {
        return this._additionalContext = A, this
      }
      n(A, B) {
        return this._clientName = A, this._commandName = B, this
      }
      f(A = (Q) => Q, B = (Q) => Q) {
        return this._inputFilterSensitiveLog = A, this._outputFilterSensitiveLog = B, this
      }
      ser(A) {
        return this._serializer = A, this
      }
      de(A) {
        return this._deserializer = A, this
      }
      build() {
        let A = this,
          B;
        return B = class extends biA {
          constructor(...[Q]) {
            super();
            this.serialize = A._serializer, this.deserialize = A._deserializer, this.input = Q ?? {}, A._init(
              this)
          }
          static {
            i0(this, "CommandRef")
          }
          static getEndpointParameterInstructions() {
            return A._ep
          }
          resolveMiddleware(Q, I, G) {
            return this.resolveMiddlewareWithContext(Q, I, G, {
              CommandCtor: B,
              middlewareFn: A._middlewareFn,
              clientName: A._clientName,
              commandName: A._commandName,
              inputFilterSensitiveLog: A._inputFilterSensitiveLog,
              outputFilterSensitiveLog: A._outputFilterSensitiveLog,
              smithyContext: A._smithyContext,
              additionalContext: A._additionalContext
            })
          }
        }
      }
    },
    p84 = "***SensitiveInformation***",
    c84 = i0((A, B) => {
      for (let Q of Object.keys(A)) {
        let I = A[Q],
          G = i0(async function(Z, Y, W) {
            let F = new I(Z);
            if (typeof Y === "function") this.send(F, Y);
            else if (typeof W === "function") {
              if (typeof Y !== "object") throw new Error(`Expected http options but got ${typeof Y}`);
              this.send(F, Y || {}, W)
            } else return this.send(F, Y)
          }, "methodImpl"),
          D = (Q[0].toLowerCase() + Q.slice(1)).replace(/Command$/, "");
        B.prototype[D] = G
      }
    }, "createAggregatedClient"),
    l84 = i0((A) => {
      switch (A) {
        case "true":
          return !0;
        case "false":
          return !1;
        default:
          throw new Error(`Unable to parse boolean value "${A}"`)
      }
    }, "parseBoolean"),
    i84 = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "number") {
        if (A === 0 || A === 1) ml.warn(CB1(`Expected boolean, got ${typeof A}: ${A}`));
        if (A === 0) return !1;
        if (A === 1) return !0
      }
      if (typeof A === "string") {
        let B = A.toLowerCase();
        if (B === "false" || B === "true") ml.warn(CB1(`Expected boolean, got ${typeof A}: ${A}`));
        if (B === "false") return !1;
        if (B === "true") return !0
      }
      if (typeof A === "boolean") return A;
      throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)
    }, "expectBoolean"),
    gl = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "string") {
        let B = parseFloat(A);
        if (!Number.isNaN(B)) {
          if (String(B) !== String(A)) ml.warn(CB1(`Expected number but observed string: ${A}`));
          return B
        }
      }
      if (typeof A === "number") return A;
      throw new TypeError(`Expected number, got ${typeof A}: ${A}`)
    }, "expectNumber"),
    n84 = Math.ceil(340282346638528860000000000000000000000),
    JB1 = i0((A) => {
      let B = gl(A);
      if (B !== void 0 && !Number.isNaN(B) && B !== 1 / 0 && B !== -1 / 0) {
        if (Math.abs(B) > n84) throw new TypeError(`Expected 32-bit float, got ${A}`)
      }
      return B
    }, "expectFloat32"),
    hl = i0((A) => {
      if (A === null || A === void 0) return;
      if (Number.isInteger(A) && !Number.isNaN(A)) return A;
      throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)
    }, "expectLong"),
    a84 = hl,
    KP1 = i0((A) => EP1(A, 32), "expectInt32"),
    HP1 = i0((A) => EP1(A, 16), "expectShort"),
    zP1 = i0((A) => EP1(A, 8), "expectByte"),
    EP1 = i0((A, B) => {
      let Q = hl(A);
      if (Q !== void 0 && s84(Q, B) !== Q) throw new TypeError(`Expected ${B}-bit integer, got ${A}`);
      return Q
    }, "expectSizedInt"),
    s84 = i0((A, B) => {
      switch (B) {
        case 32:
          return Int32Array.of(A)[0];
        case 16:
          return Int16Array.of(A)[0];
        case 8:
          return Int8Array.of(A)[0]
      }
    }, "castInt"),
    r84 = i0((A, B) => {
      if (A === null || A === void 0) {
        if (B) throw new TypeError(`Expected a non-null value for ${B}`);
        throw new TypeError("Expected a non-null value")
      }
      return A
    }, "expectNonNull"),
    giA = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "object" && !Array.isArray(A)) return A;
      let B = Array.isArray(A) ? "array" : typeof A;
      throw new TypeError(`Expected object, got ${B}: ${A}`)
    }, "expectObject"),
    o84 = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A === "string") return A;
      if (["boolean", "number", "bigint"].includes(typeof A)) return ml.warn(CB1(
        `Expected string, got ${typeof A}: ${A}`)), String(A);
      throw new TypeError(`Expected string, got ${typeof A}: ${A}`)
    }, "expectString"),
    t84 = i0((A) => {
      if (A === null || A === void 0) return;
      let B = giA(A),
        Q = Object.entries(B).filter(([, I]) => I != null).map(([I]) => I);
      if (Q.length === 0) throw new TypeError("Unions must have exactly one non-null member. None were found.");
      if (Q.length > 1) throw new TypeError(
        `Unions must have exactly one non-null member. Keys ${Q} were not null.`);
      return B
    }, "expectUnion"),
    UP1 = i0((A) => {
      if (typeof A == "string") return gl(mf(A));
      return gl(A)
    }, "strictParseDouble"),
    e84 = UP1,
    hiA = i0((A) => {
      if (typeof A == "string") return JB1(mf(A));
      return JB1(A)
    }, "strictParseFloat32"),
    AB4 = /(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,
    mf = i0((A) => {
      let B = A.match(AB4);
      if (B === null || B[0].length !== A.length) throw new TypeError("Expected real number, got implicit NaN");
      return parseFloat(A)
    }, "parseNumber"),
    NP1 = i0((A) => {
      if (typeof A == "string") return miA(A);
      return gl(A)
    }, "limitedParseDouble"),
    BB4 = NP1,
    QB4 = NP1,
    IB4 = i0((A) => {
      if (typeof A == "string") return miA(A);
      return JB1(A)
    }, "limitedParseFloat32"),
    miA = i0((A) => {
      switch (A) {
        case "NaN":
          return NaN;
        case "Infinity":
          return 1 / 0;
        case "-Infinity":
          return -1 / 0;
        default:
          throw new Error(`Unable to parse float value: ${A}`)
      }
    }, "parseFloatString"),
    diA = i0((A) => {
      if (typeof A === "string") return hl(mf(A));
      return hl(A)
    }, "strictParseLong"),
    GB4 = diA,
    DB4 = i0((A) => {
      if (typeof A === "string") return KP1(mf(A));
      return KP1(A)
    }, "strictParseInt32"),
    gf = i0((A) => {
      if (typeof A === "string") return HP1(mf(A));
      return HP1(A)
    }, "strictParseShort"),
    uiA = i0((A) => {
      if (typeof A === "string") return zP1(mf(A));
      return zP1(A)
    }, "strictParseByte"),
    CB1 = i0((A) => {
      return String(new TypeError(A).stack || A).split(`
`).slice(0, 5).filter((B) => !B.includes("stackTraceWarning")).join(`
`)
    }, "stackTraceWarning"),
    ml = {
      warn: console.warn
    },
    ZB4 = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
    $P1 = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

  function piA(A) {
    let B = A.getUTCFullYear(),
      Q = A.getUTCMonth(),
      I = A.getUTCDay(),
      G = A.getUTCDate(),
      D = A.getUTCHours(),
      Z = A.getUTCMinutes(),
      Y = A.getUTCSeconds(),
      W = G < 10 ? `0${G}` : `${G}`,
      F = D < 10 ? `0${D}` : `${D}`,
      J = Z < 10 ? `0${Z}` : `${Z}`,
      C = Y < 10 ? `0${Y}` : `${Y}`;
    return `${ZB4[I]}, ${W} ${$P1[Q]} ${B} ${F}:${J}:${C} GMT`
  }
  i0(piA, "dateToUtcString");
  var YB4 = new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),
    WB4 = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A !== "string") throw new TypeError("RFC-3339 date-times must be expressed as strings");
      let B = YB4.exec(A);
      if (!B) throw new TypeError("Invalid RFC-3339 date-time value");
      let [Q, I, G, D, Z, Y, W, F] = B, J = gf(hf(I)), C = pH(G, "month", 1, 12), X = pH(D, "day", 1, 31);
      return bl(J, C, X, {
        hours: Z,
        minutes: Y,
        seconds: W,
        fractionalMilliseconds: F
      })
    }, "parseRfc3339DateTime"),
    FB4 = new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),
    JB4 = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A !== "string") throw new TypeError("RFC-3339 date-times must be expressed as strings");
      let B = FB4.exec(A);
      if (!B) throw new TypeError("Invalid RFC-3339 date-time value");
      let [Q, I, G, D, Z, Y, W, F, J] = B, C = gf(hf(I)), X = pH(G, "month", 1, 12), V = pH(D, "day", 1, 31), K =
        bl(C, X, V, {
          hours: Z,
          minutes: Y,
          seconds: W,
          fractionalMilliseconds: F
        });
      if (J.toUpperCase() != "Z") K.setTime(K.getTime() - MB4(J));
      return K
    }, "parseRfc3339DateTimeWithOffset"),
    CB4 = new RegExp(
      /^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/
      ),
    XB4 = new RegExp(
      /^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/
      ),
    VB4 = new RegExp(
      /^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/
      ),
    KB4 = i0((A) => {
      if (A === null || A === void 0) return;
      if (typeof A !== "string") throw new TypeError("RFC-7231 date-times must be expressed as strings");
      let B = CB4.exec(A);
      if (B) {
        let [Q, I, G, D, Z, Y, W, F] = B;
        return bl(gf(hf(D)), XP1(G), pH(I, "day", 1, 31), {
          hours: Z,
          minutes: Y,
          seconds: W,
          fractionalMilliseconds: F
        })
      }
      if (B = XB4.exec(A), B) {
        let [Q, I, G, D, Z, Y, W, F] = B;
        return EB4(bl(zB4(D), XP1(G), pH(I, "day", 1, 31), {
          hours: Z,
          minutes: Y,
          seconds: W,
          fractionalMilliseconds: F
        }))
      }
      if (B = VB4.exec(A), B) {
        let [Q, I, G, D, Z, Y, W, F] = B;
        return bl(gf(hf(F)), XP1(I), pH(G.trimLeft(), "day", 1, 31), {
          hours: D,
          minutes: Z,
          seconds: Y,
          fractionalMilliseconds: W
        })
      }
      throw new TypeError("Invalid RFC-7231 date-time value")
    }, "parseRfc7231DateTime"),
    HB4 = i0((A) => {
      if (A === null || A === void 0) return;
      let B;
      if (typeof A === "number") B = A;
      else if (typeof A === "string") B = UP1(A);
      else if (typeof A === "object" && A.tag === 1) B = A.value;
      else throw new TypeError(
        "Epoch timestamps must be expressed as floating point numbers or their string representation");
      if (Number.isNaN(B) || B === 1 / 0 || B === -1 / 0) throw new TypeError(
        "Epoch timestamps must be valid, non-Infinite, non-NaN numerics");
      return new Date(Math.round(B * 1000))
    }, "parseEpochTimestamp"),
    bl = i0((A, B, Q, I) => {
      let G = B - 1;
      return NB4(A, G, Q), new Date(Date.UTC(A, G, Q, pH(I.hours, "hour", 0, 23), pH(I.minutes, "minute", 0, 59),
        pH(I.seconds, "seconds", 0, 60), qB4(I.fractionalMilliseconds)))
    }, "buildDate"),
    zB4 = i0((A) => {
      let B = new Date().getUTCFullYear(),
        Q = Math.floor(B / 100) * 100 + gf(hf(A));
      if (Q < B) return Q + 100;
      return Q
    }, "parseTwoDigitYear"),
    wB4 = 1576800000000,
    EB4 = i0((A) => {
      if (A.getTime() - new Date().getTime() > wB4) return new Date(Date.UTC(A.getUTCFullYear() - 100, A
        .getUTCMonth(), A.getUTCDate(), A.getUTCHours(), A.getUTCMinutes(), A.getUTCSeconds(), A
        .getUTCMilliseconds()));
      return A
    }, "adjustRfc850Year"),
    XP1 = i0((A) => {
      let B = $P1.indexOf(A);
      if (B < 0) throw new TypeError(`Invalid month: ${A}`);
      return B + 1
    }, "parseMonthByShortName"),
    UB4 = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
    NB4 = i0((A, B, Q) => {
      let I = UB4[B];
      if (B === 1 && $B4(A)) I = 29;
      if (Q > I) throw new TypeError(`Invalid day for ${$P1[B]} in ${A}: ${Q}`)
    }, "validateDayOfMonth"),
    $B4 = i0((A) => {
      return A % 4 === 0 && (A % 100 !== 0 || A % 400 === 0)
    }, "isLeapYear"),
    pH = i0((A, B, Q, I) => {
      let G = uiA(hf(A));
      if (G < Q || G > I) throw new TypeError(`${B} must be between ${Q} and ${I}, inclusive`);
      return G
    }, "parseDateValue"),
    qB4 = i0((A) => {
      if (A === null || A === void 0) return 0;
      return hiA("0." + A) * 1000
    }, "parseMilliseconds"),
    MB4 = i0((A) => {
      let B = A[0],
        Q = 1;
      if (B == "+") Q = 1;
      else if (B == "-") Q = -1;
      else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);
      let I = Number(A.substring(1, 3)),
        G = Number(A.substring(4, 6));
      return Q * (I * 60 + G) * 60 * 1000
    }, "parseOffsetToMilliseconds"),
    hf = i0((A) => {
      let B = 0;
      while (B < A.length - 1 && A.charAt(B) === "0") B++;
      if (B === 0) return A;
      return A.slice(B)
    }, "stripLeadingZeroes"),
    LB4 = class A extends Error {
      static {
        i0(this, "ServiceException")
      }
      constructor(B) {
        super(B.message);
        Object.setPrototypeOf(this, Object.getPrototypeOf(this).constructor.prototype), this.name = B.name, this
          .$fault = B.$fault, this.$metadata = B.$metadata
      }
      static isInstance(B) {
        if (!B) return !1;
        let Q = B;
        return A.prototype.isPrototypeOf(Q) || Boolean(Q.$fault) && Boolean(Q.$metadata) && (Q.$fault ===
          "client" || Q.$fault === "server")
      }
      static[Symbol.hasInstance](B) {
        if (!B) return !1;
        let Q = B;
        if (this === A) return A.isInstance(B);
        if (A.isInstance(B)) {
          if (Q.name && this.name) return this.prototype.isPrototypeOf(B) || Q.name === this.name;
          return this.prototype.isPrototypeOf(B)
        }
        return !1
      }
    },
    ciA = i0((A, B = {}) => {
      Object.entries(B).filter(([, I]) => I !== void 0).forEach(([I, G]) => {
        if (A[I] == null || A[I] === "") A[I] = G
      });
      let Q = A.message || A.Message || "UnknownError";
      return A.message = Q, delete A.Message, A
    }, "decorateServiceException"),
    liA = i0(({
      output: A,
      parsedBody: B,
      exceptionCtor: Q,
      errorCode: I
    }) => {
      let G = OB4(A),
        D = G.httpStatusCode ? G.httpStatusCode + "" : void 0,
        Z = new Q({
          name: B?.code || B?.Code || I || D || "UnknownError",
          $fault: "client",
          $metadata: G
        });
      throw ciA(Z, B)
    }, "throwDefaultError"),
    RB4 = i0((A) => {
      return ({
        output: B,
        parsedBody: Q,
        errorCode: I
      }) => {
        liA({
          output: B,
          parsedBody: Q,
          exceptionCtor: A,
          errorCode: I
        })
      }
    }, "withBaseException"),
    OB4 = i0((A) => ({
      httpStatusCode: A.statusCode,
      requestId: A.headers["x-amzn-requestid"] ?? A.headers["x-amzn-request-id"] ?? A.headers[
        "x-amz-request-id"],
      extendedRequestId: A.headers["x-amz-id-2"],
      cfId: A.headers["x-amz-cf-id"]
    }), "deserializeMetadata"),
    TB4 = i0((A) => {
      switch (A) {
        case "standard":
          return {
            retryMode: "standard", connectionTimeout: 3100
          };
        case "in-region":
          return {
            retryMode: "standard", connectionTimeout: 1100
          };
        case "cross-region":
          return {
            retryMode: "standard", connectionTimeout: 3100
          };
        case "mobile":
          return {
            retryMode: "standard", connectionTimeout: 30000
          };
        default:
          return {}
      }
    }, "loadConfigsForDefaultMode"),
    xiA = !1,
    PB4 = i0((A) => {
      if (A && !xiA && parseInt(A.substring(1, A.indexOf("."))) < 16) xiA = !0
    }, "emitWarningIfUnsupportedVersion"),
    SB4 = i0((A) => {
      let B = [];
      for (let Q in VP1.AlgorithmId) {
        let I = VP1.AlgorithmId[Q];
        if (A[I] === void 0) continue;
        B.push({
          algorithmId: () => I,
          checksumConstructor: () => A[I]
        })
      }
      return {
        addChecksumAlgorithm(Q) {
          B.push(Q)
        },
        checksumAlgorithms() {
          return B
        }
      }
    }, "getChecksumConfiguration"),
    _B4 = i0((A) => {
      let B = {};
      return A.checksumAlgorithms().forEach((Q) => {
        B[Q.algorithmId()] = Q.checksumConstructor()
      }), B
    }, "resolveChecksumRuntimeConfig"),
    jB4 = i0((A) => {
      return {
        setRetryStrategy(B) {
          A.retryStrategy = B
        },
        retryStrategy() {
          return A.retryStrategy
        }
      }
    }, "getRetryConfiguration"),
    yB4 = i0((A) => {
      let B = {};
      return B.retryStrategy = A.retryStrategy(), B
    }, "resolveRetryRuntimeConfig"),
    iiA = i0((A) => {
      return Object.assign(SB4(A), jB4(A))
    }, "getDefaultExtensionConfiguration"),
    kB4 = iiA,
    xB4 = i0((A) => {
      return Object.assign(_B4(A), yB4(A))
    }, "resolveDefaultRuntimeConfig"),
    fB4 = i0((A) => Array.isArray(A) ? A : [A], "getArrayIfSingleItem"),
    niA = i0((A) => {
      for (let Q in A)
        if (A.hasOwnProperty(Q) && A[Q]["#text"] !== void 0) A[Q] = A[Q]["#text"];
        else if (typeof A[Q] === "object" && A[Q] !== null) A[Q] = niA(A[Q]);
      return A
    }, "getValueFromTextNode"),
    vB4 = i0((A) => {
      return A != null
    }, "isSerializableHeaderValue"),
    NP = i0(function A(B) {
      return Object.assign(new String(B), {
        deserializeJSON() {
          return JSON.parse(String(B))
        },
        toString() {
          return String(B)
        },
        toJSON() {
          return String(B)
        }
      })
    }, "LazyJsonString");
  NP.from = (A) => {
    if (A && typeof A === "object" && (A instanceof NP || ("deserializeJSON" in A))) return A;
    else if (typeof A === "string" || Object.getPrototypeOf(A) === String.prototype) return NP(String(A));
    return NP(JSON.stringify(A))
  };
  NP.fromObject = NP.from;
  var bB4 = class {
    static {
      i0(this, "NoOpLogger")
    }
    trace() {}
    debug() {}
    info() {}
    warn() {}
    error() {}
  };

  function qP1(A, B, Q) {
    let I, G, D;
    if (typeof B === "undefined" && typeof Q === "undefined") I = {}, D = A;
    else if (I = A, typeof B === "function") return G = B, D = Q, mB4(I, G, D);
    else D = B;
    for (let Z of Object.keys(D)) {
      if (!Array.isArray(D[Z])) {
        I[Z] = D[Z];
        continue
      }
      aiA(I, null, D, Z)
    }
    return I
  }
  i0(qP1, "map");
  var gB4 = i0((A) => {
      let B = {};
      for (let [Q, I] of Object.entries(A || {})) B[Q] = [, I];
      return B
    }, "convertMap"),
    hB4 = i0((A, B) => {
      let Q = {};
      for (let I in B) aiA(Q, A, B, I);
      return Q
    }, "take"),
    mB4 = i0((A, B, Q) => {
      return qP1(A, Object.entries(Q).reduce((I, [G, D]) => {
        if (Array.isArray(D)) I[G] = D;
        else if (typeof D === "function") I[G] = [B, D()];
        else I[G] = [B, D];
        return I
      }, {}))
    }, "mapWithFilter"),
    aiA = i0((A, B, Q, I) => {
      if (B !== null) {
        let Z = Q[I];
        if (typeof Z === "function") Z = [, Z];
        let [Y = dB4, W = uB4, F = I] = Z;
        if (typeof Y === "function" && Y(B[F]) || typeof Y !== "function" && !!Y) A[I] = W(B[F]);
        return
      }
      let [G, D] = Q[I];
      if (typeof D === "function") {
        let Z, Y = G === void 0 && (Z = D()) != null,
          W = typeof G === "function" && !!G(void 0) || typeof G !== "function" && !!G;
        if (Y) A[I] = Z;
        else if (W) A[I] = D()
      } else {
        let Z = G === void 0 && D != null,
          Y = typeof G === "function" && !!G(D) || typeof G !== "function" && !!G;
        if (Z || Y) A[I] = D
      }
    }, "applyInstruction"),
    dB4 = i0((A) => A != null, "nonNullish"),
    uB4 = i0((A) => A, "pass");

  function siA(A) {
    if (A.includes(",") || A.includes('"')) A = `"${A.replace(/"/g,"\\\"")}"`;
    return A
  }
  i0(siA, "quoteHeader");
  var pB4 = i0((A) => {
      if (A !== A) return "NaN";
      switch (A) {
        case 1 / 0:
          return "Infinity";
        case -1 / 0:
          return "-Infinity";
        default:
          return A
      }
    }, "serializeFloat"),
    cB4 = i0((A) => A.toISOString().replace(".000Z", "Z"), "serializeDateTime"),
    wP1 = i0((A) => {
      if (A == null) return {};
      if (Array.isArray(A)) return A.filter((B) => B != null).map(wP1);
      if (typeof A === "object") {
        let B = {};
        for (let Q of Object.keys(A)) {
          if (A[Q] == null) continue;
          B[Q] = wP1(A[Q])
        }
        return B
      }
      return A
    }, "_json");

  function riA(A, B, Q) {
    if (Q <= 0 || !Number.isInteger(Q)) throw new Error("Invalid number of delimiters (" + Q + ") for splitEvery.");
    let I = A.split(B);
    if (Q === 1) return I;
    let G = [],
      D = "";
    for (let Z = 0; Z < I.length; Z++) {
      if (D === "") D = I[Z];
      else D += B + I[Z];
      if ((Z + 1) % Q === 0) G.push(D), D = ""
    }
    if (D !== "") G.push(D);
    return G
  }
  i0(riA, "splitEvery");
  var lB4 = i0((A) => {
    let B = A.length,
      Q = [],
      I = !1,
      G = void 0,
      D = 0;
    for (let Z = 0; Z < B; ++Z) {
      let Y = A[Z];
      switch (Y) {
        case '"':
          if (G !== "\\") I = !I;
          break;
        case ",":
          if (!I) Q.push(A.slice(D, Z)), D = Z + 1;
          break;
        default:
      }
      G = Y
    }
    return Q.push(A.slice(D)), Q.map((Z) => {
      Z = Z.trim();
      let Y = Z.length;
      if (Y < 2) return Z;
      if (Z[0] === '"' && Z[Y - 1] === '"') Z = Z.slice(1, Y - 1);
      return Z.replace(/\\"/g, '"')
    })
  }, "splitHeader")
});