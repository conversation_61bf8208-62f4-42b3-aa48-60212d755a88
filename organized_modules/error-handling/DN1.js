// Module: dN1
// Lines: 19803-19814
// Purpose: error_tracking
// Dependencies: this, XY9, Object, Dq, SFA

var dN1 = w((SFA) => {
  Object.defineProperty(SFA, "__esModule", {
    value: !0
  });
  SFA.NotFoundError = void 0;
  var XY9 = Dq();
  SFA.NotFoundError = XY9.createErrorClass(function(A) {
    return function B(Q) {
      A(this), this.name = "NotFoundError", this.message = Q
    }
  })
});