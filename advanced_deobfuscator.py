#!/usr/bin/env python3
"""
高级反混淆工具 - 基于语义分析和上下文推断的变量重命名
"""

import re
import json
import ast
from pathlib import Path
from collections import defaultdict, Counter

class AdvancedDeobfuscator:
    def __init__(self):
        # 基于实际代码分析的映射
        self.semantic_mappings = {
            # 模块包装器和工具函数
            'w': 'moduleWrapper',
            'J1': 'importHelper', 
            'Nu': 'defineGetters',
            'Uz1': 'lazyEvaluator',
            'D1': 'requireFunction',
            
            # Object 方法的常见混淆名
            'H51': 'defineProperty',
            'Mp9': 'getOwnPropertyDescriptor',
            'Lp9': 'getOwnPropertyNames',
            'Rp9': 'hasOwnProperty',
            'Op9': 'defineGetters',
            'Tp9': 'copyProperties',
            'Pp9': 'createESModule',
            
            # 常见的工具函数
            'mkA': 'setFunctionName',
            'dkA': 'moduleExports',
            'ukA': 'exportsObject',
            'hkA': 'contextProvider',
            'gkA': 'getContext',
            
            # Smithy/AWS SDK 相关
            'Sp9': 'getSmithyContext',
            '_p9': 'normalizeProvider',
            'SMITHY_CONTEXT_KEY': 'smithyContextKey',
            
            # Sentry 相关函数
            'NS2': 'applyAggregateErrors',
            'zA1': 'domUtils',
            'Nw1': 'dsnUtils',
            '$S2': 'sentryError',
            '$w1': 'globalObject',
            'qS2': 'instrumentationHandler',
            'vG': 'typeCheckers',
            'MS2': 'browserDetection',
            'wA1': 'consoleUtils',
            'LS2': 'memoBuilder',
            'x$': 'eventUtils',
            'qw1': 'nodeUtils',
            'EA1': 'normalizeUtils',
            'UE': 'objectUtils',
            'iO': 'pathUtils',
            'RS2': 'promiseBuffer',
            'nO': 'requestUtils',
            'Mw1': 'severityUtils',
            'Su': 'stackParser',
            '_u': 'stringUtils',
            'f$': 'supportUtils',
            'Lw1': 'syncPromise',
            'ju': 'timeUtils',
            'yu': 'tracingUtils',
            'B0A': 'sdkUtils',
            'rK': 'envelopeUtils',
            'OS2': 'clientReport',
            'ku': 'rateLimiting',
            'Uy': 'baggageUtils',
            'UA1': 'urlUtils',
            'TS2': 'integrationUtils',
            'PS2': 'cacheUtils',
            'NA1': 'eventFactory',
            'Q0A': 'debugUtils',
            'SS2': 'lruMap',
        }
        
        # 函数参数的语义映射
        self.param_mappings = {
            'A': 'target',
            'B': 'source',
            'Q': 'options', 
            'I': 'index',
            'G': 'key',
            'D': 'data',
            'Z': 'result',
            'Y': 'value',
            'W': 'wrapper',
            'F': 'func',
            'J': 'item',
            'C': 'context',
            'X': 'extra',
            'V': 'variable',
            'N': 'name',
            'E': 'element',
            'R': 'reference',
            'S': 'state',
            'T': 'type',
            'U': 'utility',
            'P': 'param',
            'L': 'length',
            'M': 'method',
            'K': 'constant',
            'H': 'handler',
        }
        
        # 基于上下文的重命名规则
        self.context_rules = [
            (r'error|exception|catch', 'error'),
            (r'file|path|fs', 'file'),
            (r'fetch|xhr|http|request', 'network'),
            (r'dom|element|node', 'dom'),
            (r'promise|async|await', 'async'),
            (r'console|log|debug', 'log'),
            (r'sentry|track|monitor', 'sentry'),
            (r'validate|check|test', 'validate'),
            (r'parse|format|serialize', 'format'),
            (r'normalize|clean|sanitize', 'normalize'),
            (r'create|make|build|factory', 'factory'),
            (r'get|retrieve|fetch', 'getter'),
            (r'set|assign|update', 'setter'),
            (r'add|append|insert', 'adder'),
            (r'remove|delete|clear', 'remover'),
        ]

    def analyze_function_semantics(self, func_content):
        """分析函数语义，推断其用途"""
        content_lower = func_content.lower()
        
        # 检查关键词
        for pattern, category in self.context_rules:
            if re.search(pattern, content_lower):
                return category
        
        # 检查返回值类型
        if 'return true' in content_lower or 'return false' in content_lower:
            return 'predicate'
        elif 'return {}' in content_lower or 'return []' in content_lower:
            return 'factory'
        elif 'throw' in content_lower:
            return 'validator'
        
        return 'utility'

    def suggest_meaningful_name(self, var_name, context, usage_pattern=''):
        """基于上下文和使用模式建议有意义的名称"""
        
        # 检查已知映射
        if var_name in self.semantic_mappings:
            return self.semantic_mappings[var_name]
        
        # 单字母参数映射
        if len(var_name) == 1 and var_name in self.param_mappings:
            return self.param_mappings[var_name]
        
        # 基于上下文推断
        context_lower = context.lower()
        
        # 检查是否是常见的混淆模式
        if re.match(r'^[a-zA-Z]\d+$', var_name):  # 如 A1, b2
            base = var_name[0].upper()
            if base in self.param_mappings:
                return f"{self.param_mappings[base]}{var_name[1:]}"
        
        # 基于上下文分类
        for pattern, category in self.context_rules:
            if re.search(pattern, context_lower):
                if len(var_name) <= 3:
                    return f"{category}_{var_name}"
                else:
                    return f"{category}_{var_name.lower()}"
        
        # 基于使用模式
        if 'function' in usage_pattern:
            return f"func_{var_name}"
        elif 'object' in usage_pattern:
            return f"obj_{var_name}"
        elif 'array' in usage_pattern:
            return f"arr_{var_name}"
        
        return var_name

    def deobfuscate_module(self, content):
        """反混淆整个模块"""
        lines = content.split('\n')
        deobfuscated_lines = []
        
        for line in lines:
            deobfuscated_line = self.deobfuscate_line(line, content)
            deobfuscated_lines.append(deobfuscated_line)
        
        return '\n'.join(deobfuscated_lines)

    def deobfuscate_line(self, line, full_context):
        """反混淆单行代码"""
        original_line = line
        
        # 处理模块包装器
        line = re.sub(r'var\s+(\w+)\s*=\s*w\(', 
                     lambda m: f"var {self.suggest_meaningful_name(m.group(1), full_context)} = moduleWrapper(",
                     line)
        
        # 处理Object方法解构
        if 'defineProperty:' in line:
            line = re.sub(r'(\w+):\s*defineProperty', r'defineProperty: defineProperty', line)
        if 'getOwnPropertyDescriptor:' in line:
            line = re.sub(r'(\w+):\s*getOwnPropertyDescriptor', r'getOwnPropertyDescriptor: getOwnPropertyDescriptor', line)
        if 'getOwnPropertyNames:' in line:
            line = re.sub(r'(\w+):\s*getOwnPropertyNames', r'getOwnPropertyNames: getOwnPropertyNames', line)
        
        # 处理已知的函数映射
        for obfuscated, readable in self.semantic_mappings.items():
            if obfuscated in line and len(obfuscated) > 1:  # 避免替换单字母变量
                line = line.replace(obfuscated, readable)
        
        # 处理函数参数（在函数定义中）
        if '=>' in line or 'function' in line:
            # 替换常见的单字母参数
            for param, meaning in self.param_mappings.items():
                pattern = rf'\b{param}\b(?=\s*[,\)])'  # 参数位置的单字母
                line = re.sub(pattern, meaning, line)
        
        return line

    def create_readable_version(self, file_path):
        """创建可读版本的文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加更多注释
        readable_content = self.add_explanatory_comments(content)
        
        # 反混淆
        readable_content = self.deobfuscate_module(readable_content)
        
        return readable_content

    def add_explanatory_comments(self, content):
        """添加解释性注释"""
        lines = content.split('\n')
        commented_lines = []
        
        for line in lines:
            commented_lines.append(line)
            
            # 为关键模式添加注释
            if 'moduleWrapper(' in line:
                commented_lines.append('  // Module wrapper function - bundles code into a module')
            elif 'defineProperty' in line and 'Object' in line:
                commented_lines.append('  // Object property definition utilities')
            elif 'getSmithyContext' in line:
                commented_lines.append('  // AWS SDK Smithy context management')
            elif 'normalizeProvider' in line:
                commented_lines.append('  // Provider normalization for async/sync values')
            elif '__esModule' in line:
                commented_lines.append('  // ES6 module compatibility marker')
        
        return '\n'.join(commented_lines)

def main():
    deobfuscator = AdvancedDeobfuscator()
    
    # 创建输出目录
    output_dir = Path('readable_modules')
    output_dir.mkdir(exist_ok=True)
    
    print("开始高级反混淆处理...")
    
    # 处理示例模块
    sample_modules = ['QJ.js', 'tA.js', 'Mz1.js', 'CX.js', 'Tz1.js']
    
    for module_name in sample_modules:
        module_path = f'extracted_modules/{module_name}'
        if Path(module_path).exists():
            print(f"处理模块: {module_name}")
            
            readable_content = deobfuscator.create_readable_version(module_path)
            
            output_file = output_dir / f'{module_name.replace(".js", "_readable.js")}'
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(readable_content)
    
    # 创建映射文档
    create_mapping_documentation(output_dir, deobfuscator)
    
    print(f"高级反混淆完成！输出目录: {output_dir}")

def create_mapping_documentation(output_dir, deobfuscator):
    """创建详细的映射文档"""
    doc_path = output_dir / 'DEOBFUSCATION_GUIDE.md'
    
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write("# Claude Code CLI 反混淆指南\n\n")
        
        f.write("## 语义映射表\n\n")
        f.write("### 核心函数和工具\n\n")
        f.write("| 混淆名 | 实际含义 | 用途说明 |\n")
        f.write("|--------|----------|----------|\n")
        
        core_mappings = {
            'w': ('moduleWrapper', '模块包装器函数'),
            'J1': ('importHelper', 'ES6导入辅助函数'),
            'Nu': ('defineGetters', '定义getter属性'),
            'H51': ('defineProperty', 'Object.defineProperty'),
            'Mp9': ('getOwnPropertyDescriptor', 'Object.getOwnPropertyDescriptor'),
            'Lp9': ('getOwnPropertyNames', 'Object.getOwnPropertyNames'),
            'mkA': ('setFunctionName', '设置函数名称'),
            'Op9': ('defineGetters', '批量定义getter'),
            'Tp9': ('copyProperties', '复制对象属性'),
            'Pp9': ('createESModule', '创建ES模块对象'),
        }
        
        for obf, (real, desc) in core_mappings.items():
            f.write(f"| `{obf}` | `{real}` | {desc} |\n")
        
        f.write("\n### Sentry 错误跟踪模块\n\n")
        f.write("| 混淆名 | 实际含义 | 功能 |\n")
        f.write("|--------|----------|------|\n")
        
        sentry_mappings = {
            'NS2': ('applyAggregateErrors', '应用聚合错误'),
            'zA1': ('domUtils', 'DOM工具函数'),
            'vG': ('typeCheckers', '类型检查函数'),
            'wA1': ('consoleUtils', '控制台工具'),
            'UE': ('objectUtils', '对象操作工具'),
            'Su': ('stackParser', '堆栈解析器'),
            'ju': ('timeUtils', '时间工具'),
            'yu': ('tracingUtils', '追踪工具'),
        }
        
        for obf, (real, desc) in sentry_mappings.items():
            f.write(f"| `{obf}` | `{real}` | {desc} |\n")
        
        f.write("\n### 参数命名约定\n\n")
        f.write("| 参数名 | 通常含义 | 使用场景 |\n")
        f.write("|--------|----------|----------|\n")
        
        for param, meaning in deobfuscator.param_mappings.items():
            f.write(f"| `{param}` | `{meaning}` | 函数参数 |\n")
        
        f.write("\n## 反混淆策略\n\n")
        f.write("### 1. 基于语义的重命名\n")
        f.write("- 分析函数体内容，推断函数用途\n")
        f.write("- 根据上下文关键词进行分类\n")
        f.write("- 使用有意义的名称替换混淆标识符\n\n")
        
        f.write("### 2. 模式识别\n")
        f.write("- 识别模块包装器模式\n")
        f.write("- 检测Object方法的解构赋值\n")
        f.write("- 识别ES6模块导出模式\n\n")
        
        f.write("### 3. 上下文推断\n")
        f.write("- 错误处理相关：包含error、exception、catch\n")
        f.write("- 文件操作相关：包含file、path、fs\n")
        f.write("- 网络请求相关：包含fetch、xhr、http\n")
        f.write("- DOM操作相关：包含dom、element、node\n")
        f.write("- 异步操作相关：包含promise、async、await\n\n")
        
        f.write("## 使用建议\n\n")
        f.write("1. **渐进式反混淆**：先处理核心模块，再扩展到具体功能模块\n")
        f.write("2. **保持一致性**：在整个项目中使用统一的命名约定\n")
        f.write("3. **添加注释**：为复杂的逻辑添加解释性注释\n")
        f.write("4. **验证功能**：反混淆后测试功能是否正常\n")

if __name__ == "__main__":
    main()
