# Claude Code CLI 反混淆指南

## 语义映射表

### 核心函数和工具

| 混淆名 | 实际含义 | 用途说明 |
|--------|----------|----------|
| `w` | `moduleWrapper` | 模块包装器函数 |
| `J1` | `importHelper` | ES6导入辅助函数 |
| `Nu` | `defineGetters` | 定义getter属性 |
| `H51` | `defineProperty` | Object.defineProperty |
| `Mp9` | `getOwnPropertyDescriptor` | Object.getOwnPropertyDescriptor |
| `Lp9` | `getOwnPropertyNames` | Object.getOwnPropertyNames |
| `mkA` | `setFunctionName` | 设置函数名称 |
| `Op9` | `defineGetters` | 批量定义getter |
| `Tp9` | `copyProperties` | 复制对象属性 |
| `Pp9` | `createESModule` | 创建ES模块对象 |

### Sentry 错误跟踪模块

| 混淆名 | 实际含义 | 功能 |
|--------|----------|------|
| `NS2` | `applyAggregateErrors` | 应用聚合错误 |
| `zA1` | `domUtils` | DOM工具函数 |
| `vG` | `typeCheckers` | 类型检查函数 |
| `wA1` | `consoleUtils` | 控制台工具 |
| `UE` | `objectUtils` | 对象操作工具 |
| `Su` | `stackParser` | 堆栈解析器 |
| `ju` | `timeUtils` | 时间工具 |
| `yu` | `tracingUtils` | 追踪工具 |

### 参数命名约定

| 参数名 | 通常含义 | 使用场景 |
|--------|----------|----------|
| `A` | `target` | 函数参数 |
| `B` | `source` | 函数参数 |
| `Q` | `options` | 函数参数 |
| `I` | `index` | 函数参数 |
| `G` | `key` | 函数参数 |
| `D` | `data` | 函数参数 |
| `Z` | `result` | 函数参数 |
| `Y` | `value` | 函数参数 |
| `W` | `wrapper` | 函数参数 |
| `F` | `func` | 函数参数 |
| `J` | `item` | 函数参数 |
| `C` | `context` | 函数参数 |
| `X` | `extra` | 函数参数 |
| `V` | `variable` | 函数参数 |
| `N` | `name` | 函数参数 |
| `E` | `element` | 函数参数 |
| `R` | `reference` | 函数参数 |
| `S` | `state` | 函数参数 |
| `T` | `type` | 函数参数 |
| `U` | `utility` | 函数参数 |
| `P` | `param` | 函数参数 |
| `L` | `length` | 函数参数 |
| `M` | `method` | 函数参数 |
| `K` | `constant` | 函数参数 |
| `H` | `handler` | 函数参数 |

## 反混淆策略

### 1. 基于语义的重命名
- 分析函数体内容，推断函数用途
- 根据上下文关键词进行分类
- 使用有意义的名称替换混淆标识符

### 2. 模式识别
- 识别模块包装器模式
- 检测Object方法的解构赋值
- 识别ES6模块导出模式

### 3. 上下文推断
- 错误处理相关：包含error、exception、catch
- 文件操作相关：包含file、path、fs
- 网络请求相关：包含fetch、xhr、http
- DOM操作相关：包含dom、element、node
- 异步操作相关：包含promise、async、await

## 使用建议

1. **渐进式反混淆**：先处理核心模块，再扩展到具体功能模块
2. **保持一致性**：在整个项目中使用统一的命名约定
3. **添加注释**：为复杂的逻辑添加解释性注释
4. **验证功能**：反混淆后测试功能是否正常
