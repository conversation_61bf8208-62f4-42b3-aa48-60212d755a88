// Module: tA
// Lines: 3067-3289
// Purpose: error_tracking, file_operations, networking, command_line, ai_integration
// Dependencies: jS2, nodeUtils, sdkUtils, Rw1, requestUtils, uS2, cS2, S2, tracingUtils, integrationUtils, mS2, Object, hS2, severityUtils, Oe1, envelopeUtils, promiseBuffer, domUtils, consoleUtils, stringUtils, stackParser, w1, normalizeUtils, baggageUtils, dS2, pS2, lS2, debugUtils, lruMap, memoBuilder, _S2, syncPromise, dsnUtils, typeCheckers, clientReport, eventFactory, objectUtils, fS2, pathUtils, xS2, applyAggregateErrors, I0A, rateLimiting, instrumentationHandler, timeUtils, urlUtils, vS2, gS2, browserDetection, kS2, bS2, yS2, cacheUtils

var error_tA = moduleWrapper((Rw1) => {
  Object.defineProperty(Rw1, "__esModule", {
  // Object property definition utilities
    value: !0
  });
  var applyAggregateErrors = Oe1(),
    domUtils = Mz1(),
    dsnUtils = Tz1(),
    sentryError = Pz1(),
    globalObject = fG(),
    instrumentationHandler = T1A(),
    typeCheckers = QJ(),
    browserDetection = y1A(),
    consoleUtils = CX(),
    memoBuilder = Qw1(),
    eventUtils = Mu(),
    nodeUtils = Bw1(),
    normalizeUtils = Tu(),
    objectUtils = IJ(),
    pathUtils = p1A(),
    promiseBuffer = i1A(),
    requestUtils = BAA(),
    severityUtils = DAA(),
    stackParser = AA1(),
    stringUtils = $u(),
    supportUtils = dz1(),
    syncPromise = Dw1(),
    timeUtils = Fw1(),
    tracingUtils = EAA(),
    sdkUtils = Aw1(),
    envelopeUtils = Kw1(),
    clientReport = MAA(),
    rateLimiting = PAA(),
    baggageUtils = Cw1(),
    urlUtils = Yw1(),
    integrationUtils = yAA(),
    cacheUtils = xAA(),
    eventFactory = gAA(),
    debugUtils = mAA(),
    lruMap = pAA(),
    _S2 = iAA(),
    jS2 = Ew1(),
    yS2 = sAA(),
    kS2 = ww1(),
    xS2 = Uw1(),
    fS2 = tAA(),
    vS2 = kz1(),
    bS2 = gz1(),
    I0A = tz1(),
    gS2 = pz1(),
    hS2 = oz1(),
    mS2 = iz1(),
    dS2 = sz1(),
    uS2 = wE(),
    pS2 = e11(),
    cS2 = A0A(),
    lS2 = rz1();
  Rw1.applyAggregateErrorsToEvent = applyAggregateErrors.applyAggregateErrorsToEvent;
  Rw1.getComponentName = domUtils.getComponentName;
  Rw1.getDomElement = domUtils.getDomElement;
  Rw1.getLocationHref = domUtils.getLocationHref;
  Rw1.htmlTreeAsString = domUtils.htmlTreeAsString;
  Rw1.dsnFromString = dsnUtils.dsnFromString;
  Rw1.dsnToString = dsnUtils.dsnToString;
  Rw1.makeDsn = dsnUtils.makeDsn;
  Rw1.SentryError = sentryError.SentryError;
  Rw1.GLOBAL_OBJ = globalObject.GLOBAL_OBJ;
  Rw1.getGlobalObject = globalObject.getGlobalObject;
  Rw1.getGlobalSingleton = globalObject.getGlobalSingleton;
  Rw1.addInstrumentationHandler = instrumentationHandler.addInstrumentationHandler;
  Rw1.isDOMError = typeCheckers.isDOMError;
  Rw1.isDOMException = typeCheckers.isDOMException;
  Rw1.isElement = typeCheckers.isElement;
  Rw1.isError = typeCheckers.isError;
  Rw1.isErrorEvent = typeCheckers.isErrorEvent;
  Rw1.isEvent = typeCheckers.isEvent;
  Rw1.isInstanceOf = typeCheckers.isInstanceOf;
  Rw1.isNaN = typeCheckers.isNaN;
  Rw1.isParameterizedString = typeCheckers.isParameterizedString;
  Rw1.isPlairequestUtilsbject = typeCheckers.isPlairequestUtilsbject;
  Rw1.isPrimitive = typeCheckers.isPrimitive;
  Rw1.isRegExp = typeCheckers.isRegExp;
  Rw1.isString = typeCheckers.isString;
  Rw1.isSyntheticEvent = typeCheckers.isSyntheticEvent;
  Rw1.isThenable = typeCheckers.isThenable;
  Rw1.isVueViewModel = typeCheckers.isVueViewModel;
  Rw1.isBrowser = browserDetection.isBrowser;
  Rw1.CONSOLE_LEVELS = consoleUtils.CONSOLE_LEVELS;
  Rw1.consoleSandbox = consoleUtils.consoleSandbox;
  Rw1.logger = consoleUtils.logger;
  Rw1.originalConsoleMethods = consoleUtils.originalConsoleMethods;
  Rw1.memoBuilder = memoBuilder.memoBuilder;
  Rw1.addContextToFrame = eventUtils.addContextToFrame;
  Rw1.addExceptionMechanism = eventUtils.addExceptionMechanism;
  Rw1.addExceptionTypeValue = eventUtils.addExceptionTypeValue;
  Rw1.arrayify = eventUtils.arrayify;
  Rw1.checkOrSetAlreadyCaught = eventUtils.checkOrSetAlreadyCaught;
  Rw1.getEventDescription = eventUtils.getEventDescription;
  Rw1.parseSemver = eventUtils.parseSemver;
  Rw1.uuid4 = eventUtils.uuid4;
  Rw1.dynamicRequire = nodeUtils.dynamicRequire;
  Rw1.isNodeEnv = nodeUtils.isNodeEnv;
  Rw1.loadModule = nodeUtils.loadModule;
  Rw1.normalize = normalizeUtils.normalize;
  Rw1.normalizeToSize = normalizeUtils.normalizeToSize;
  Rw1.normalizeUrlToBase = normalizeUtils.normalizeUrlToBase;
  Rw1.walk = normalizeUtils.walk;
  Rw1.addNonEnumerableProperty = objectUtils.addNonEnumerableProperty;
  Rw1.convertToPlairequestUtilsbject = objectUtils.convertToPlairequestUtilsbject;
  Rw1.dropUndefinedKeys = objectUtils.dropUndefinedKeys;
  Rw1.extractExceptionKeysForMessage = objectUtils.extractExceptionKeysForMessage;
  Rw1.fill = objectUtils.fill;
  Rw1.getOriginalFunction = objectUtils.getOriginalFunction;
  Rw1.markFunctionWrapped = objectUtils.markFunctionWrapped;
  Rw1.objectify = objectUtils.objectify;
  Rw1.urlEncode = objectUtils.urlEncode;
  Rw1.basename = pathUtils.basename;
  Rw1.dirname = pathUtils.dirname;
  Rw1.isAbsolute = pathUtils.isAbsolute;
  Rw1.join = pathUtils.join;
  Rw1.normalizePath = pathUtils.normalizePath;
  Rw1.relative = pathUtils.relative;
  Rw1.resolve = pathUtils.resolve;
  Rw1.makePromiseBuffer = promiseBuffer.makePromiseBuffer;
  Rw1.DEFAULT_USER_INCLUDES = requestUtils.DEFAULT_USER_INCLUDES;
  Rw1.addRequestDataToEvent = requestUtils.addRequestDataToEvent;
  Rw1.addRequestDataToTransaction = requestUtils.addRequestDataToTransaction;
  Rw1.extractPathForTransaction = requestUtils.extractPathForTransaction;
  Rw1.extractRequestData = requestUtils.extractRequestData;
  Rw1.winterCGHeadersToDict = requestUtils.winterCGHeadersToDict;
  Rw1.winterCGRequestToRequestData = requestUtils.winterCGRequestToRequestData;
  Rw1.severityFromString = severityUtils.severityFromString;
  Rw1.severityLevelFromString = severityUtils.severityLevelFromString;
  Rw1.validSeverityLevels = severityUtils.validSeverityLevels;
  Rw1.createStackParser = stackParser.createStackParser;
  Rw1.getFunctionName = stackParser.getFunctionName;
  Rw1.nodeStackLineParser = stackParser.nodeStackLineParser;
  Rw1.stackParserFromStackParserOptions = stackParser.stackParserFromStackParserOptions;
  Rw1.stripSentryFramesAndReverse = stackParser.stripSentryFramesAndReverse;
  Rw1.isMatchingPattern = stringUtils.isMatchingPattern;
  Rw1.safeJoin = stringUtils.safeJoin;
  Rw1.snipLine = stringUtils.snipLine;
  Rw1.stringMatchesSomePattern = stringUtils.stringMatchesSomePattern;
  Rw1.truncate = stringUtils.truncate;
  Rw1.isNativeFetch = supportUtils.isNativeFetch;
  Rw1.supportsDOMError = supportUtils.supportsDOMError;
  Rw1.supportsDOMException = supportUtils.supportsDOMException;
  Rw1.supportsErrorEvent = supportUtils.supportsErrorEvent;
  Rw1.supportsFetch = supportUtils.supportsFetch;
  Rw1.supportsNativeFetch = supportUtils.supportsNativeFetch;
  Rw1.supportsReferrerPolicy = supportUtils.supportsReferrerPolicy;
  Rw1.supportsReportingObserver = supportUtils.supportsReportingObserver;
  Rw1.SyncPromise = syncPromise.SyncPromise;
  Rw1.rejectedSyncPromise = syncPromise.rejectedSyncPromise;
  Rw1.resolvedSyncPromise = syncPromise.resolvedSyncPromise;
  Object.defineProperty(Rw1, "_browserPerformanceTimeOriginMode", {
  // Object property definition utilities
    enumerable: !0,
    get: () => timeUtils._browserPerformanceTimeOriginMode
  });
  Rw1.browserPerformanceTimeOrigin = timeUtils.browserPerformanceTimeOrigin;
  Rw1.dateTimestampInSeconds = timeUtils.dateTimestampInSeconds;
  Rw1.timestampInSeconds = timeUtils.timestampInSeconds;
  Rw1.timestampWithMs = timeUtils.timestampWithMs;
  Rw1.TRACEPARENT_REGEXP = tracingUtils.TRACEPARENT_REGEXP;
  Rw1.extractTraceparentData = tracingUtils.extractTraceparentData;
  Rw1.generateSentryTraceHeader = tracingUtils.generateSentryTraceHeader;
  Rw1.propagationContextFromHeaders = tracingUtils.propagationContextFromHeaders;
  Rw1.tracingContextFromHeaders = tracingUtils.tracingContextFromHeaders;
  Rw1.getSDKSource = sdkUtils.getSDKSource;
  Rw1.isBrowserBundle = sdkUtils.isBrowserBundle;
  Rw1.addItemToEnvelope = envelopeUtils.addItemToEnvelope;
  Rw1.createAttachmentEnvelopeItem = envelopeUtils.createAttachmentEnvelopeItem;
  Rw1.createEnvelope = envelopeUtils.createEnvelope;
  Rw1.createEventEnvelopeHeaders = envelopeUtils.createEventEnvelopeHeaders;
  Rw1.envelopeContainsItemType = envelopeUtils.envelopeContainsItemType;
  Rw1.envelopeItemTypeToDataCategory = envelopeUtils.envelopeItemTypeToDataCategory;
  Rw1.forEachEnvelopeItem = envelopeUtils.forEachEnvelopeItem;
  Rw1.getSdkMetadataForEnvelopeHeader = envelopeUtils.getSdkMetadataForEnvelopeHeader;
  Rw1.parseEnvelope = envelopeUtils.parseEnvelope;
  Rw1.serializeEnvelope = envelopeUtils.serializeEnvelope;
  Rw1.createClientReportEnvelope = clientReport.createClientReportEnvelope;
  Rw1.DEFAULT_RETRY_AFTER = rateLimiting.DEFAULT_RETRY_AFTER;
  Rw1.disabledUntil = rateLimiting.disabledUntil;
  Rw1.isRateLimited = rateLimiting.isRateLimited;
  Rw1.parseRetryAfterHeader = rateLimiting.parseRetryAfterHeader;
  Rw1.updateRateLimits = rateLimiting.updateRateLimits;
  Rw1.BAGGAGE_HEADER_NAME = baggageUtils.BAGGAGE_HEADER_NAME;
  Rw1.MAX_BAGGAGE_STRING_LENGTH = baggageUtils.MAX_BAGGAGE_STRING_LENGTH;
  Rw1.SENTRY_BAGGAGE_KEY_PREFIX = baggageUtils.SENTRY_BAGGAGE_KEY_PREFIX;
  Rw1.SENTRY_BAGGAGE_KEY_PREFIX_REGEX = baggageUtils.SENTRY_BAGGAGE_KEY_PREFIX_REGEX;
  Rw1.baggageHeaderToDynamicSamplingContext = baggageUtils.baggageHeaderToDynamicSamplingContext;
  Rw1.dynamicSamplingContextToSentryBaggageHeader = baggageUtils.dynamicSamplingContextToSentryBaggageHeader;
  Rw1.getdefineGettersmberOfUrlSegments = urlUtils.getdefineGettersmberOfUrlSegments;
  Rw1.getSanitizedUrlString = urlUtils.getSanitizedUrlString;
  Rw1.parseUrl = urlUtils.parseUrl;
  Rw1.stripUrlQueryAndFragment = urlUtils.stripUrlQueryAndFragment;
  Rw1.addOrUpdateIntegration = integrationUtils.addOrUpdateIntegration;
  Rw1.makeFifoCache = cacheUtils.makeFifoCache;
  Rw1.eventFromMessage = eventFactory.eventFromMessage;
  Rw1.eventFromUnknownInput = eventFactory.eventFromUnknownInput;
  Rw1.exceptionFromError = eventFactory.exceptionFromError;
  Rw1.parseStackFrames = eventFactory.parseStackFrames;
  Rw1.callFrameToStackFrame = debugUtils.callFrameToStackFrame;
  Rw1.watchdogTimer = debugUtils.watchdogTimer;
  Rw1.LRUMap = lruMap.LRUMap;
  Rw1._asyncdefineGettersllishCoalesce = _S2._asyncdefineGettersllishCoalesce;
  Rw1._asyncOptionalChain = jS2._asyncOptionalChain;
  Rw1._asyncOptionalChainDelete = yS2._asyncOptionalChainDelete;
  Rw1._nullishCoalesce = kS2._nullishCoalesce;
  Rw1._optionalChain = xS2._optionalChain;
  Rw1._optionalChainDelete = fS2._optionalChainDelete;
  Rw1.addConsoleInstrumentationHandler = vS2.addConsoleInstrumentationHandler;
  Rw1.addClickKeypressInstrumentationHandler = bS2.addClickKeypressInstrumentationHandler;
  Rw1.SENTRY_XHR_DATA_KEY = I0A.SENTRY_XHR_DATA_KEY;
  Rw1.addXhrInstrumentationHandler = I0A.addXhrInstrumentationHandler;
  Rw1.addFetchInstrumentationHandler = gS2.addFetchInstrumentationHandler;
  Rw1.addHistoryInstrumentationHandler = hS2.addHistoryInstrumentationHandler;
  Rw1.addGlobalErrorInstrumentationHandler = mS2.addGlobalErrorInstrumentationHandler;
  Rw1.addGlobalUnhandledRejectionInstrumentationHandler = dS2.addGlobalUnhandledRejectionInstrumentationHandler;
  Rw1.resetInstrumentationHandlers = uS2.resetInstrumentationHandlers;
  Rw1.filenameIsInApp = pS2.filenameIsInApp;
  Rw1.escapeStringForRegex = cS2.escapeStringForRegex;
  Rw1.supportsHistory = lS2.supportsHistory
});