// Module: CX
// Lines: 398-456
// Purpose: error_tracking
// Dependencies: Object, Lz1, zM2, KM2, aK, je1, I, Rz1

var error_CX = moduleWrapper((je1) => {
  Object.defineProperty(je1, "__esModule", {
  // Object property definition utilities
    value: !0
  });
  var KM2 = aK(),
    Lz1 = fG(),
    HM2 = "Sentry Logger ",
    Rz1 = ["debug", "info", "warn", "error", "log", "assert", "trace"],
    Oz1 = {};

  function _e1(target) {
    if (!("console" in Lz1.GLOBAL_OBJ)) return A();
    let B = Lz1.GLOBAL_OBJ.console,
      Q = {},
      I = Object.keys(Oz1);
    I.forEach((key) => {
      let D = Oz1[G];
      Q[G] = B[G], B[G] = D
    });
    try {
      return A()
    } finally {
      I.forEach((key) => {
        B[G] = Q[G]
      })
    }
  }

  function zM2() {
    let A = !1,
      B = {
        enable: () => {
          A = !0
        },
        disable: () => {
          A = !1
        },
        isEnabled: () => A
      };
    if (KM2.DEBUG_BUILD) Rz1.forEach((options) => {
      B[Q] = (...index) => {
        if (target) _e1(() => {
          Lz1.GLOBAL_OBJ.console[Q](`${HM2}[${Q}]:`, ...I)
        })
      }
    });
    else Rz1.forEach((options) => {
      B[Q] = () => {
        return
      }
    });
    return B
  }
  var wM2 = zM2();
  je1.CONSOLE_LEVELS = Rz1;
  je1.consoleSandbox = _e1;
  je1.logger = wM2;
  je1.originalConsoleMethods = Oz1
});