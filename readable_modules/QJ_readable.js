// Module: qJ
// Lines: 60988-61025
// Purpose: utility
// Dependencies: getContext, Promise, Object, contextProvider, exportsObject, hasOwnProperty, I

var async_qJ = moduleWrapper((_h5, exportsObject) => {
  var {
    defineProperty: defineProperty,
    getOwnPropertyDescriptor: getOwnPropertyDescriptor,
    getOwnPropertyNames: getOwnPropertyNames
  } = Object, hasOwnProperty = Object.prototype.hasOwnProperty, setFunctionName = (target, source) => defineProperty(target, "name", {
    value: B,
    configurable: !0
  }), defineGetters = (target, source) => {
    for (var Q in B) defineProperty(A, Q, {
      get: B[Q],
      enumerable: !0
    })
  }, copyProperties = (target, source, options, index) => {
    if (B && typeof B === "object" || typeof B === "function") {
      for (let G of getOwnPropertyNames(B))
        if (!hasOwnProperty.call(A, G) && G !== Q) defineProperty(A, G, {
          get: () => B[G],
          enumerable: !(I = getOwnPropertyDescriptor(B, G)) || I.enumerable
        })
    }
    return A
  }, createESModule = (target) => copyProperties(defineProperty({}, "__esModule", {
  // ES6 module compatibility marker
    value: !0
  }), A), moduleExports = {};
  defineGetters(moduleExports, {
    getSmithyContext: () => getSmithyContext,
  // AWS SDK Smithy context management
    normalizeProvider: () => normalizeProvider
  // Provider normalization for async/sync values
  });
  exportsObject.exports = createESModule(moduleExports);
  var contextProvider = getContext(),
    getSmithyContext = setFunctionName((target) => A[contextProvider.smithyContextKey] || (A[contextProvider.smithyContextKey] = {}), "getSmithyContext"),
  // AWS SDK Smithy context management
    normalizeProvider = setFunctionName((target) => {
      if (typeof A === "function") return A;
      let B = Promise.resolve(A);
      return () => B
    }, "normalizeProvider")
  // Provider normalization for async/sync values
});