/**
 * Claude AI Integration - Deobfuscated Code
 *
 * This file contains the core AI integration logic for Claude Code CLI.
 * Functions have been renamed for clarity and comments added for understanding.
 *
 * Key components:
 * - API calling functions (callClaudeAPI, streamClaudeAPI)
 * - Conversation loop management (mainConversationLoop, coreConversationHandler)
 * - Tool execution and validation
 * - Message formatting and context management
 * - Error handling and retry logic
 */

// Chunk 102
// Lines 306001-309000
// Size: 87228 bytes

          gap: 1,
          key: `logo-${ W }-${ J }`
        }, p9.createElement(bK1, {
          model: C
        }), $71() ? p9.createElement(Kk1, null) : p9.createElement(cN2, null))
      }, ...Y ? [{
        type: "static",
        jsx: p9.createElement(m, {
          key: `tip-of-the-day-${ W }-${ J }`
        }, p9.createElement(sN2, {
          tip: Y
        }))
      }] : [], ...V ? [{
        type: "static",
        jsx: p9.createElement(m, {
          key: `max-subscription-${ W }-${ J }`
        }, p9.createElement(tN2, null))
      }] : [], ...K.length > 0 ? [{
        type: "static",
        jsx: p9.createElement(m, {
          key: `install-messages-${ W }-${ J }`,
          flexDirection: "column",
          paddingLeft: 1
        }, K.map((S, f)  = > p9.createElement(m, {
          key: f,
          flexDirection: "row",
          marginTop: 1
        }, p9.createElement(y, {
          color: $1().warning
        }, H2.bullet), p9.createElement(y, {
          color: $1().warning
        }, " ", S))))
      }] : [], ...B.length > 0 ? [{
        type: "static",
        jsx: p9.createElement(m, {
          flexDirection: "column",
          gap: 1,
          key: `history-${ W }-${ J }`
        }, Fp1(B.filter((S)  = > S.type !== "progress").filter((S)  = > S.type !== "user" || !S.isMeta)).map((
          S)  = > p9.createElement(m, {
            key: `history-${ S.uuid }-${ J }`,
            width: X - 5
          }, p9.createElement(BK, {
            message: S,
            messages: B,
            addMargin: !0,
            tools: Q,
            verbose: O,
            erroredToolUseIDs: new Set,
            inProgressToolUseIDs: new Set,
            progressMessagesForMessage: [],
            shouldAnimate: !1,
            shouldShowDot: !0,
            unresolvedToolUseIDs: new Set
          }))), p9.createElement(rN2, {
          dividerChar: " = ",
          title: "Previous Conversation Compacted"
        }))
      }] : [], ...Fp1(N.filter((S)  = > S.type !== "progress").filter((S)  = > S.type !== "user" || !S.isMeta)).map((
        S)  = > {
        let f  =  lr(S),
          a  =  Jp1(S, N),
          g  =  p9.createElement(BK, {
            message: S,
            messages: N,
            addMargin: !0,
            tools: Q,
            verbose: O,
            erroredToolUseIDs: R,
            inProgressToolUseIDs: M,
            progressMessagesForMessage: a,
            shouldAnimate: !G && !D.length && !Z && (!f || M.has(f)),
            shouldShowDot: !0,
            unresolvedToolUseIDs: q
          });
        return {
          type: Q75(S, A, q, F) ? "static" : "transient",
          jsx: p9.createElement(m, {
            key: `${ S.uuid }-${ a.length }-${ J }`,
            width: X - 5
          }, g)
        }
      }).filter((S)  = > S !== void 0), ...zW2() ? [{
        type: "static",
        jsx: p9.createElement(EW2, null)
      }] : []]
    }, [W, J, C, Y, V, B, N, X, Q, R, M, G, D.length, Z, q, A, F, K]);
  return p9.createElement(p9.Fragment, null, p9.createElement(Z71, {
    key: `static-messages-${ W }-${ J }`,
    items: T(I).filter((O)  = > O.type === "static")
  }, (O)  = > O.jsx), T(I).filter((O)  = > O.type === "transient").map((O)  = > O.jsx))
}

function Q75(messages, systemPrompt, userPrompt, tools) {
  if (I === "transcript") return !0;
  switch (A.type) {
    case "attachment":
      return !0;
    case "user":
    case "assistant": {
      if (!lr(A)) return !0;
      let D  =  qu0(A, B);
      return !wI0(D, Q)
    }
    case "progress":
      return !1
  }
}
import {
  randomUUID as rt1
} from "crypto";

function eN2(messages) {
  J0(async (B, Q)  = > {
    if (Q.ctrl && B === "r") await A((I)  = > I === "transcript" ? "prompt" : "transcript")
  })
}
var ae  =  J1(_1(), 1);
var I75  =  i.object({
  method: i.literal("selection_changed"),
  params: i.object({
    selection: i.object({
      start: i.object({
        line: i.number(),
        character: i.number()
      }),
      end: i.object({
        line: i.number(),
        character: i.number()
      })
    }).nullable().optional(),
    text: i.string().optional(),
    filePath: i.string().optional()
  })
});

function A$2(A, B) {
  let Q  =  ae.useRef(!1),
    I  =  ae.useRef(null);
  ae.useEffect(()  = > {
    let G  =  A.find((Z)  = > Z.type === "connected" && Z.name === "ide");
    if (I.current !== G) Q.current  =  !1, I.current  =  G || null, B({
      lineCount: 0,
      text: void 0,
      filePath: void 0
    });
    if (Q.current || !G) return;
    let D  =  (Z)  = > {
      if (Z.selection?.start && Z.selection?.end) {
        let {
          start: Y,
          end: W
        }  =  Z.selection, F  =  W.line - Y.line + 1;
        if (W.character === 0) F--;
        let J  =  {
          lineCount: F,
          text: Z.text,
          filePath: Z.filePath
        };
        B(J)
      }
    };
    G.client.setNotificationHandler(I75, (Z)  = > {
      if (I.current !== G) return;
      try {
        let Y  =  Z.params;
        if (Y.selection && Y.selection.start && Y.selection.end) D(Y);
        else if (Y.text !== void 0) D({
          selection: null,
          text: Y.text,
          filePath: Y.filePath
        })
      } catch (Y) {
        console.error("Error processing selection_changed notification:", Y)
      }
    }), Q.current  =  !0
  }, [A, B])
}
var nt1  =  BV();

function GH1(messages) {
  nt1  =  A
}
var M4  =  J1(_1(), 1);
async function B$2(A) {
  return `Launch a new agent that has access to the following tools: ${ A.filter((Q) = >Q.name!==Wz).map((Q) = >Q.name).join(", ") }. When you are searching for a keyword or file and are not confident that you will find the right match in the first few tries, use the Agent tool to perform the search for you.

When to use the Agent tool:
- If you are searching for a keyword like "config" or "logger", or for questions like "which file does X?", the Agent tool is strongly recommended

When NOT to use the Agent tool:
- If you want to read a specific file path, use the ${ b3.name } or ${ fN.name } tool instead of the Agent tool, to find the match more quickly
- If you are searching for a specific class definition like "class Foo", use the ${ fN.name } tool instead, to find the match more quickly
- If you are searching for code within a specific file or set of 2-3 files, use the ${ b3.name } tool instead of the Agent tool, to find the match more quickly

Usage notes:
1. Launch multiple agents concurrently whenever possible, to maximize performance; to do that, use a single message with multiple tool uses
2. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.
3. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.
4. The agent's outputs should generally be trusted
5. Clearly tell the agent whether you expect it to write code or just to do research (search, file reads, web fetches, etc.), since it is not aware of the user's intent`
}
var at1  =  3,
  G75  =  i.object({
    description: i.string().describe("A short (3-5 word) description of the task"),
    prompt: i.string().describe("The task for the agent to perform")
  });

function D75(messages, systemPrompt) {
  let Q  =  B.sort((I, G)  = > I.agentIndex - G.agentIndex).map((I, G)  = > {
    let D  =  I.content.filter((Z)  = > Z.type === "text").map((Z)  = > Z.text).join(`

`);
    return ` ==  AGENT ${ G+1 } RESPONSE  == 
${ D }
`
  }).join(`

`);
  return `Original task: ${ A }

I've assigned multiple agents to tackle this task. Each agent has analyzed the problem and provided their findings.

${ Q }

Based on all the information provided by these agents, synthesize a comprehensive and cohesive response that:
1. Combines the key insights from all agents
2. Resolves any contradictions between agent findings
3. Presents a unified solution that addresses the original task
4. Includes all important details and code examples from the individual responses
5. Is well-structured and complete

Your synthesis should be thorough but focused on the original task.`
}
async function* st1(A, B, Q, I, G, D  =  { }) {
  let {
    abortController: Z,
    options: {
      debug: Y,
      verbose: W,
      isNonInteractiveSession: F
    },
    getToolPermissionContext: J,
    readFileState: C,
    tools: X
  }  =  Q, {
    isSynthesis: V  =  !1,
    systemPrompt: K,
    model: U
  }  =  D, N  =  [createMessage({
    content: A
  })], [q, M, R]  =  await Promise.all([rY(), Sw(F ?? !1), U ?? getDefaultModel()]), T  =  await (K ?? v70(R)), O  =  [], S  =  0;
  for await (let Y1 of mainConversationLoop(N, T, q, M, G, {
    abortController: Z,
    options: {
      isNonInteractiveSession: F ?? !1,
      tools: X,
      commands: [],
      debug: Y,
      verbose: W,
      mainLoopModel: R,
      maxThinkingTokens: Ej(N),
      mcpClients: [],
      propagateErrors: !0
    },
    getToolPermissionContext: J,
    readFileState: C,
    getQueuedCommands: ()  = > [],
    removeQueuedCommands: ()  = > { }
  })) {
    if (O.push(Y1), Y1.type !== "assistant") continue;
    let r  =  tQ(O);
    for (let w1 of Y1.message.content) {
      if (w1.type !== "tool_use") continue;
      S++, yield {
        type: "progress",
        toolUseID: V ? `synthesis_${ I.message.id }` : `agent_${ B }_${ I.message.id }`,
        data: {
          message: r.find((H1)  = > H1.type === "assistant" && H1.message.content[0]?.type === "tool_use" && H1.message
            .content[0].id === w1.id),
          normalizedMessages: r
        }
      }
    }
  }
  let f  =  AY(O);
  if (f && fF1(f)) throw new CancellationError;
  if (f?.type !== "assistant") throw new Error(V ? "Synthesis: Last message was not an assistant message" :
    `Agent ${ B+1 }: Last message was not an assistant message`);
  let a  =  (f.message.usage.cache_creation_input_tokens ?? 0) + (f.message.usage.cache_read_input_tokens ?? 0) + f
    .message.usage.input_tokens + f.message.usage.output_tokens,
    g  =  f.message.content.filter((Y1)  = > Y1.type === "text");
  ERA([...N, ...O]), yield {
    type: "result",
    data: {
      agentIndex: B,
      content: g,
      toolUseCount: S,
      tokens: a,
      usage: f.message.usage
    }
  }
}
var Q$2  =  {
  async prompt({
    tools: A
  }) {
    return await B$2(A)
  },
  name: Wz,
  async description() {
    return "Launch a new task"
  },
  inputSchema: G75,
  async * call({
    prompt: A
  }, {
    abortController: B,
    options: {
      debug: Q,
      tools: I,
      verbose: G,
      isNonInteractiveSession: D
    },
    getToolPermissionContext: Z,
    readFileState: Y
  }, W, F) {
    let J  =  Date.now(),
      C  =  VA(),
      X  =  C.parallelAgents.enabled && C.parallelAgents.count > 1,
      V  =  {
        abortController: B,
        options: {
          debug: Q,
          verbose: G,
          isNonInteractiveSession: D ?? !1
        },
        getToolPermissionContext: Z,
        readFileState: Y,
        tools: I.filter((K)  = > K.name !== Wz)
      };
    if (X) {
      let K  =  C.parallelAgents.count,
        U  =  0,
        N  =  0,
        M  =  Array(K).fill(`${ A }

Provide a thorough and complete analysis.`).map((f, a)  = > st1(f, a, V, F, W)),
        R  =  [];
      for await (let f of uF1(M, 10)) if (f.type === "progress") yield f;
      else if (f.type === "result") R.push(f.data), U + =  f.data.toolUseCount, N + =  f.data.tokens;
      if (B.signal.aborted) throw new CancellationError;
      let T  =  D75(A, R),
        O  =  st1(T, 0, V, F, W, {
          isSynthesis: !0
        }),
        S  =  null;
      for await (let f of O) if (f.type === "progress") U++, yield f;
      else if (f.type === "result") S  =  f.data, N + =  S.tokens;
      if (!S) throw new Error("Synthesis agent did not return a result");
      if (B.signal.aborted) throw new CancellationError;
      yield {
        type: "result",
        data: {
          content: S.content,
          totalDurationMs: Date.now() - J,
          totalTokens: N,
          totalToolUseCount: U,
          usage: S.usage,
          wasInterrupted: B.signal.aborted
        }
      }
    } else {
      let K  =  st1(A, 0, V, F, W),
        U  =  0,
        N  =  null;
      for await (let q of K) if (q.type === "progress") yield q;
      else if (q.type === "result") N  =  q.data, U  =  N.toolUseCount;
      if (B.signal.aborted) throw new CancellationError;
      if (!N) throw new Error("Agent did not return a result");
      yield {
        type: "result",
        data: {
          content: N.content,
          totalDurationMs: Date.now() - J,
          totalTokens: N.tokens,
          totalToolUseCount: U,
          usage: N.usage,
          wasInterrupted: B.signal.aborted
        }
      }
    }
  },
  isReadOnly() {
    return !0
  },
  isEnabled() {
    return !0
  },
  userFacingName() {
    return "Task"
  },
  async checkPermissions(A) {
    return {
      behavior: "allow",
      updatedInput: A
    }
  },
  mapToolResultToToolResultBlockParam(A, B) {
    return {
      tool_use_id: B,
      type: "tool_result",
      content: A.content
    }
  },
  renderToolResultMessage({
    totalDurationMs: A,
    totalToolUseCount: B,
    totalTokens: Q,
    usage: I
  }, G, {
    tools: D,
    verbose: Z
  }) {
    let Y  =  VA(),
      W  =  Y.parallelAgents.enabled && Y.parallelAgents.count > 1,
      F  =  [B === 1 ? "1 tool use" : `${ B } tool uses`, BG(Q) + " tokens", vP(A)],
      J  =  W ? `Done with ${ Y.parallelAgents.count } parallel agents (${ F.join(" · ") })` : `Done (${ F.join(" · ") })`,
      C  =  D_({
        content: J,
        usage: I
      });
    return M4.createElement(m, {
      flexDirection: "column"
    }, Z ? G.map((X)  = > M4.createElement(r0, {
      height: 1,
      key: X.uuid
    }, M4.createElement(BK, {
      message: X.data.message,
      messages: X.data.normalizedMessages,
      addMargin: !1,
      tools: D,
      verbose: Z,
      erroredToolUseIDs: new Set,
      inProgressToolUseIDs: new Set,
      unresolvedToolUseIDs: new Set,
      progressMessagesForMessage: G,
      shouldAnimate: !1,
      shouldShowDot: !1
    }))) : null, M4.createElement(r0, {
      height: 1
    }, M4.createElement(BK, {
      message: C,
      messages: tQ([C]),
      addMargin: !1,
      tools: D,
      verbose: Z,
      erroredToolUseIDs: new Set,
      inProgressToolUseIDs: new Set,
      unresolvedToolUseIDs: new Set,
      progressMessagesForMessage: [],
      shouldAnimate: !1,
      shouldShowDot: !1
    })))
  },
  renderToolUseMessage({
    description: A,
    prompt: B
  }, {
    verbose: Q
  }) {
    if (Q) return `Task: ${ A }

Prompt: ${ Id(B) }`;
    return A
  },
  renderToolUseProgressMessage(A, {
    tools: B,
    verbose: Q
  }) {
    let I  =  VA(),
      G  =  I.parallelAgents.enabled && I.parallelAgents.count > 1;
    if (!A.length) return M4.createElement(r0, {
      height: 1
    }, M4.createElement(y, {
      color: $1().secondaryText
    }, G ? `Initializing ${ I.parallelAgents.count } parallel agents…` : "Initializing…"));
    let D  =  G && A.some((F)  = > F.toolUseID.startsWith("agent_") && F.toolUseID.includes("_")),
      Z  =  G && A.some((F)  = > F.toolUseID.startsWith("synthesis_")),
      Y  =  new Map;
    if (D)
      for (let F of A) {
        let J  =  "main";
        if (F.toolUseID.startsWith("agent_") && F.toolUseID.includes("_")) {
          let C  =  F.toolUseID.match(/^agent_(\d+)_/);
          if (C && C[1]) J  =  `Agent ${ parseInt(C[1])+1 }`
        } else if (F.toolUseID.startsWith("synthesis_")) J  =  "Synthesis";
        if (!Y.has(J)) Y.set(J, []);
        Y.get(J).push(F)
      }
    let W  =  A.length;
    if (D && Y.size > 1) {
      let F  =  [];
      for (let [J, C] of Y.entries())
        if (C.length > 0) {
          let X  =  C[C.length - 1];
          if (X) F.push(M4.createElement(m, {
            key: J,
            flexDirection: "column",
            marginY: 1
          }, M4.createElement(y, {
            color: $1().success,
            bold: !0
          }, J, Z && J === "Synthesis" ? " (combining results)" : "", ":"), M4.createElement(BK, {
            key: X.uuid,
            message: X.data.message,
            messages: X.data.normalizedMessages,
            addMargin: !1,
            tools: B,
            verbose: Q,
            erroredToolUseIDs: new Set,
            inProgressToolUseIDs: new Set,
            unresolvedToolUseIDs: KN(C),
            progressMessagesForMessage: C,
            shouldAnimate: !1,
            shouldShowDot: !1
          })))
        } return M4.createElement(r0, null, M4.createElement(m, {
        flexDirection: "column"
      }, M4.createElement(y, {
        color: $1().secondaryText
      }, W, " total tool uses across ", Y.size, " agents"), F))
    } else {
      let F  =  Q ? A : A.slice(-at1),
        J  =  W - F.length;
      if (!Q && W > at1) F  =  A.slice(-at1 + 1);
      return M4.createElement(r0, null, M4.createElement(m, {
        flexDirection: "column"
      }, F.map((C)  = > M4.createElement(BK, {
        key: C.uuid,
        message: C.data.message,
        messages: C.data.normalizedMessages,
        addMargin: !1,
        tools: B,
        verbose: Q,
        erroredToolUseIDs: new Set,
        inProgressToolUseIDs: new Set,
        unresolvedToolUseIDs: KN(A),
        progressMessagesForMessage: A,
        shouldAnimate: !1,
        shouldShowDot: !1
      })), J > 0 && M4.createElement(y, {
        color: $1().secondaryText
      }, "+", J, " more tool ", J === 1 ? "use" : "uses")))
    }
  },
  renderToolUseRejectedMessage() {
    return M4.createElement(I5, null)
  },
  renderToolUseErrorMessage(A, {
    progressMessagesForMessage: B,
    tools: Q,
    verbose: I
  }) {
    return M4.createElement(M4.Fragment, null, this.renderToolUseProgressMessage(B, {
      tools: Q,
      verbose: I
    }), M4.createElement(q6, {
      result: A,
      verbose: I
    }))
  }
};
var Z75  =  J1(_1(), 1);
var t3B  =  i.strictObject({
  task_id: i.string().describe("The ID of the background task to kill")
});
var Y75  =  J1(_1(), 1);
var XQB  =  i.strictObject({
  task_id: i.string().describe("The ID of the task to retrieve output from")
});
var kw  =  J1(_1(), 1);
var I$2  =  "WebSearch",
  G$2  =  `
- Allows Claude to search the web and use the results to inform responses
- Provides up-to-date information for current events and recent data
- Returns search result information formatted as search result blocks
- Use this tool for accessing information beyond Claude's knowledge cutoff
- Searches are performed automatically within a single API call

Usage notes:
  - Domain filtering is supported to include or block specific websites
  - Web search is only available in the US
`;

function W75(messages) {
  let B  =  0,
    Q  =  0;
  for (let I of A)
    if (typeof I !== "string") B++, Q + =  I.content.length;
  return {
    searchCount: B,
    totalResultCount: Q
  }
}
var F75  =  i.strictObject({
    query: i.string().min(2).describe("The search query to use"),
    allowed_domains: i.array(i.string()).optional().describe("Only include search results from these domains"),
    blocked_domains: i.array(i.string()).optional().describe("Never include search results from these domains")
  }),
  J75  =  {
    type: "web_search_20250305",
    name: "web_search"
  },
  C75  =  ({
    allowed_domains: A,
    blocked_domains: B
  })  = > {
    return {
      ...J75,
      allowed_domains: A,
      blocked_domains: B,
      max_uses: 8
    }
  };

function X75(messages, systemPrompt, userPrompt) {
  let I  =  [],
    G  =  "",
    D  =  !0;
  for (let Z of A) {
    if (Z.type  ==  "citations") continue;
    if (Z.type === "server_tool_use") {
      if (D) {
        if (D  =  !1, G.trim().length > 0) I.push(G.trim());
        G  =  ""
      }
      continue
    }
    if (Z.type === "web_search_tool_result") {
      if (!Array.isArray(Z.content)) {
        let W  =  `Web search error: ${ Z.content.error_code }`;
        logError(new Error(W)), I.push(W);
        continue
      }
      let Y  =  Z.content.map((W)  = > ({
        title: W.title,
        url: W.url
      }));
      I.push({
        tool_use_id: Z.tool_use_id,
        content: Y
      })
    }
    if (Z.type === "text")
      if (D) G + =  Z.text;
      else D  =  !0, G  =  Z.text
  }
  if (G.length) I.push(G.trim());
  return {
    query: B,
    results: I,
    durationSeconds: Q
  }
}
var D$2  =  {
  name: I$2,
  async description(A) {
    let {
      query: B
    }  =  A;
    return `Claude wants to search the web for: ${ B }`
  },
  userFacingName() {
    return "Web Search"
  },
  isEnabled() {
    return AZ() === "firstParty"
  },
  inputSchema: F75,
  isReadOnly: ()  = > !0,
  async checkPermissions(A) {
    return {
      behavior: "allow",
      updatedInput: A
    }
  },
  async prompt() {
    return G$2
  },
  renderToolUseMessage({
    query: A,
    allowed_domains: B,
    blocked_domains: Q
  }, {
    verbose: I
  }) {
    let G  =  "";
    if (A) G + =  `"${ A }"`;
    if (I) {
      if (B && B.length > 0) G + =  `, only allowing domains: ${ B.join(", ") }`;
      if (Q && Q.length > 0) G + =  `, blocking domains: ${ Q.join(", ") }`
    }
    return G
  },
  renderToolUseRejectedMessage() {
    return kw.default.createElement(I5, null)
  },
  renderToolUseErrorMessage(A, {
    verbose: B
  }) {
    return kw.default.createElement(q6, {
      result: A,
      verbose: B
    })
  },
  renderToolUseProgressMessage(A) {
    if (A.length === 0) return null;
    let B  =  A[A.length - 1];
    if (!B?.data) return null;
    let Q  =  B.data;
    switch (Q.type) {
      case "query_update":
        return kw.default.createElement(r0, null, kw.default.createElement(y, {
          dimColor: !0
        }, "Searching: ", Q.query));
      case "search_results_received":
        return kw.default.createElement(r0, null, kw.default.createElement(y, {
          dimColor: !0
        }, "Found ", Q.resultCount, ' results for "', Q.query, '"'));
      default:
        return null
    }
  },
  renderToolResultMessage(A) {
    let {
      searchCount: B
    }  =  W75(A.results), Q  =  A.durationSeconds >= 1 ? `${ Math.round(A.durationSeconds) }s` :
      `${ Math.round(A.durationSeconds*1000) }ms`;
    return kw.default.createElement(m, {
      justifyContent: "space-between",
      width: "100%"
    }, kw.default.createElement(r0, {
      height: 1
    }, kw.default.createElement(y, null, "Did ", B, " search", B !== 1 ? "es" : "", " in ", Q)))
  },
  async validateInput(A) {
    let {
      query: B,
      allowed_domains: Q,
      blocked_domains: I
    }  =  A;
    if (!B.length) return {
      result: !1,
      message: "Error: Missing query",
      errorCode: 1
    };
    if (Q && I) return {
      result: !1,
      message: "Error: Cannot specify both allowed_domains and blocked_domains in the same request",
      errorCode: 2
    };
    return {
      result: !0
    }
  },
  async * call(A, B) {
    let Q  =  performance.now(),
      {
        query: I
      }  =  A,
      G  =  createMessage({
        content: "Perform a web search for the query: " + I
      }),
      D  =  C75(A),
      Z  =  streamAPIRequest([G], ["You are an assistant for performing a web search tool use"], B.options.maxThinkingTokens, [], B
        .abortController.signal, {
          getToolPermissionContext: B.getToolPermissionContext,
          model: await getDefaultModel(),
          prependCLISysprompt: !0,
          toolChoice: void 0,
          isNonInteractiveSession: B.options.isNonInteractiveSession,
          extraToolSchemas: [D]
        }),
      Y  =  [],
      W  =  null,
      F  =  "",
      J  =  0,
      C  =  new Map;
    for await (let q of Z) {
      if (Y.push(q), q.type === "stream_event" && q.event?.type === "content_block_start") {
        let M  =  q.event.content_block;
        if (M && M.type === "server_tool_use") {
          W  =  M.id, F  =  "";
          continue
        }
      }
      if (W && q.type === "stream_event" && q.event?.type === "content_block_delta") {
        let M  =  q.event.delta;
        if (M?.type === "input_json_delta" && M.partial_json) {
          F + =  M.partial_json;
          try {
            let R  =  F.match(/"query"\s*:\s*"((?:[^"\\]|\\.)*)"/);
            if (R && R[1]) {
              let T  =  JSON.parse('"' + R[1] + '"');
              if (!C.has(W) || C.get(W) !== T) C.set(W, T), J++, yield {
                type: "progress",
                toolUseID: `search-progress-${ J }`,
                data: {
                  type: "query_update",
                  query: T
                }
              }
            }
          } catch { }
        }
      }
      if (q.type === "stream_event" && q.event?.type === "content_block_start") {
        let M  =  q.event.content_block;
        if (M && M.type === "web_search_tool_result") {
          let R  =  M.tool_use_id,
            T  =  C.get(R) || I,
            O  =  M.content;
          J++, yield {
            type: "progress",
            toolUseID: R || `search-progress-${ J }`,
            data: {
              type: "search_results_received",
              resultCount: Array.isArray(O) ? O.length : 0,
              query: T
            }
          }
        }
      }
    }
    let X  =  null,
      V  =  [];
    for (let q of Y)
      if (q.type === "stream_event" && q.event) {
        let M  =  q.event;
        if (M.type === "content_block_start") V.push(M.content_block);
        if (M.type === "message_start" && M.message) X  =  M.message
      } if (X && V.length > 0) X.content  =  V;
    else if (V.length > 0) X  =  {
      role: "assistant",
      content: V
    };
    let U  =  (performance.now() - Q) / 1000;
    yield {
      type: "result",
      data: X75(X?.content || [], I, U)
    }
  },
  mapToolResultToToolResultBlockParam(A, B) {
    let {
      query: Q,
      results: I
    }  =  A, G  =  `Web search results for query: "${ Q }"

`;
    return I.forEach((D)  = > {
      if (typeof D === "string") G + =  D + `

`;
      else if (D.content.length > 0) G + =  `Links: ${ JSON.stringify(D.content) }

`;
      else G + =  `No links found.

`
    }), {
      tool_use_id: B,
      type: "tool_result",
      content: G.trim()
    }
  }
};
var $j  =  (A, B)  = > {
  let Q  =  [Q$2, N4, fN, Qj, VC, b3, $Q, oV, HD, Xe, zw, sY, ...B ? [yU, zY] : [], D$2, ...[]],
    I  =  xh(A),
    G  =  Q.filter((Z)  = > {
      return !I.some((Y)  = > Y.ruleValue.toolName === Z.name && Y.ruleValue.ruleContent === void 0)
    }),
    D  =  G.map((Z)  = > Z.isEnabled());
  return G.filter((Z, Y)  = > D[Y])
};
var bd  =  J1(_1(), 1);

function Z$2() {
  let [A, B]  =  bd.useState([]), Q  =  bd.useRef([]), I  =  bd.useCallback((G)  = > {
    Q.current  =  G(Q.current), B(Q.current)
  }, [B]);
  return {
    queuedCommands: A,
    queuedCommandsRef: Q,
    setQueuedCommands: I
  }
}

function gd({
  commands: messages,
  debug: systemPrompt,
  initialPrompt: userPrompt,
  shouldShowPromptInput: tools,
  initialTools: signal,
  initialMessages: options,
  initialTodos: temperature,
  tipOfTheDay: response,
  mcpClients: content,
  dynamicMcpConfig: F
}) {
  let [{
    todoFeatureEnabled: J,
    toolPermissionContext: C,
    verbose: X,
    mainLoopModel: V,
    maxRateLimitFallbackActive: K,
    rateLimitResetsAt: U
  }, N]  =  rG(), q  =  Sd(), M  =  lh(), R  =  j9.useMemo(()  = > $j(C, J), [C, J]), [T, O]  =  j9.useState(F), S  =  j9.useCallback(
    (p1)  = > {
      O(p1)
    }, [O]), [f, a]  =  j9.useState("prompt"), [g, Y1]  =  j9.useState(1), {
    notification: r,
    addNotification: w1
  }  =  IJ2(), {
    clients: H1,
    tools: x,
    commands: F1
  }  =  BV2(w1, T), x1  =  hN2(W, H1), o1  =  dN2([...R, ...G], x), a1  =  pN2(A, F1), [PA, cA]  =  j9.useState(null);
  IV2(H1), A$2(H1, cA);
  let [FA, f1]  =  j9.useState("responding"), [B1, v1]  =  j9.useState(null), [M1, AA]  =  j9.useState(!1), [NA, OA]  =  j9
    .useState(null), [o, A1]  =  j9.useState(null), [I1, E1]  =  j9.useState([]), [N1, t]  =  j9.useState(D ?? []), [S1, k1]  = 
    j9.useState([]), [d1, e1]  =  j9.useState(""), [IA, zA]  =  j9.useState("prompt"), {
      queuedCommands: X0,
      queuedCommandsRef: kA,
      setQueuedCommands: z0
    }  =  Z$2(), [s2, B2]  =  j9.useState([]), [E2, g2]  =  j9.useState(0), [Q9, o4]  =  j9.useState(0), [Z0, h0]  =  j9.useState(
      !1), [m0, L0]  =  j9.useState(!1), [H0, j2]  =  j9.useState(rt1()), [y9, z8]  =  j9.useState(VA()
      .hasAcknowledgedCostThreshold), [zB, H6]  =  j9.useState("INSERT"), [T2, x4]  =  j9.useState(null), {
      haikuWords: f0,
      generateHaikuWord: U2
    }  =  QU2(M1), [r2, T6]  =  j9.useState(null), [w8, u3]  =  j9.useState(!1);
  j9.useEffect(()  = > {
    function p1(JA) {
      if (!Jz || !JA) return;
      O((ZA)  = > {
        if (ZA?.ide) return ZA;
        return {
          ...ZA,
          ide: {
            type: JA.url.startsWith("ws:") ? "ws-ide" : "sse-ide",
            url: JA.url,
            ideName: JA.name
          }
        }
      })
    }
    TD0(p1, ()  = > u3(!0), (JA)  = > {
      T6(JA)
    })
  }, []), j9.useEffect(()  = > {
    if (U !== M.resetsAt) N((p1)  = > ({
      ...p1,
      rateLimitResetsAt: M.resetsAt
    }));
    if (xu0(K, U, M, (p1)  = > N((JA)  = > ({
        ...JA,
        maxRateLimitFallbackActive: p1
      }))), K && V === null) w1({
      text: `Claude Opus 4 limit reached, now using ${ Ui(kP()) }`
    })
  }, [w1, K, V, U, M, N]);
  let iB  =  j9.useCallback((p1)  = > {
      k1(p1), K3(), j2(rt1())
    }, []),
    z6  =  (p1)  = > {
      if (e1(p1), IA !== "prompt") return;
      if (!p1) return;
      if (f0.length > 0 && (!p1.endsWith(" ") || d1.endsWith(" "))) return;
      if (!p1.includes(" ")) return;
      if (p1.length >= 3 && !p1.startsWith("!") && !p1.startsWith("#") && !p1.startsWith("/")) U2(p1)
    },
    H3  =  j9.useCallback((p1, JA)  = > {
      return new Promise((ZA)  = > {
        x4({
          m1: p1,
          m2: JA,
          resolve: ZA
        })
      })
    }, []),
    E8  =  j9.useMemo(()  = > Cb(), []),
    QB  =  j9.useRef({
      [E8]: {
        content: JSON.stringify(Z || []),
        timestamp: 0
      }
    }),
    {
      status: OQ,
      reverify: V2
    }  =  kN2();

  function N9() {
    if (!M1) return;
    if (AA(!1), I1[0]) I1[0].onAbort(), E1([]);
    else B1?.abort()
  }
  let z3  =  j9.useCallback(()  = > {
    if (X0.length === 0) return;
    e1([...X0.map((p1)  = > p1.value), d1].filter(Boolean).join(`
`)), zA("prompt"), z0(()  = > [])
  }, [X0, e1, zA, z0, d1]);
  xN2(E1, x4, N9, M1, Z0, X0, B1?.signal, z3, zB), j9.useEffect(()  = > {
    if (wH() >= 5 && !m0 && !y9) {
      if (logMetric("tengu_cost_threshold_reached", { }), v31() && !process.env.DISABLE_COST_WARNINGS) L0(!0)
    }
  }, [N1, m0, y9]);
  let G7  =  vN2(E1),
    IB  =  j9.useCallback((p1)  = > {
      N((JA)  = > ({
        ...JA,
        toolPermissionContext: p1
      }))
    }, [N]),
    nB  =  j9.useCallback((p1, JA, ZA)  = > {
      return {
        abortController: ZA,
        options: {
          commands: a1,
          tools: o1,
          debug: B,
          verbose: X,
          mainLoopModel: q,
          maxThinkingTokens: Ej(JA),
          mcpClients: x1,
          ideInstallationStatus: r2,
          isNonInteractiveSession: !1,
          dynamicMcpConfig: T
        },
        getToolPermissionContext() {
          return nt1
        },
        getQueuedCommands() {
          return kA.current
        },
        removeQueuedCommands($A) {
          z0((rA)  = > rA.filter((bA)  = > !$A.includes(bA)))
        },
        messages: p1,
        setMessages: t,
        setMessageHistory: iB,
        onChangeAPIKey: V2,
        readFileState: QB.current,
        setToolJSX: A1,
        addNotification: w1,
        setToolPermissionContext: IB,
        onChangeDynamicMcpConfig: S,
        nestedMemoryAttachmentTriggers: new Set
      }
    }, [a1, B, o1, X, q, x1, r2, T, iB, V2, w1, IB, S, kA, z0]);
  async function $G() {
    V2();
    let p1  =  UZ();
    for (let rA of p1) QB.current[rA.path]  =  {
      content: rA.content,
      timestamp: Date.now()
    };
    if (!Q) return;
    AA(!0), o4(0);
    let JA  =  new AbortController;
    v1(JA);
    let {
      messages: ZA,
      shouldQuery: $A
    }  =  await vd(Q, "prompt", A1, nB(N1, N1, JA), null, PA, void 0);
    if (ZA.length) {
      for (let fA of ZA)
        if (fA.type === "user") Zz(Q);
      if (t((fA)  = > [...fA, ...ZA]), !$A) {
        v1(null), AA(!1);
        return
      }
      let [rA, bA, sA]  =  await Promise.all([YS(o1, q), rY(), Sw(!1)]);
      for await (let fA of coreConversationHandler([...N1, ...ZA], rA, bA, sA, G7, nB([...N1, ...ZA], ZA, JA), H3)) Cp1(fA, (iA)  = > {
        t((P2)  = > [...P2, iA])
      }, (iA)  = > o4((P2)  = > P2 + iA.length), (iA)  = > f1(iA))
    } else Zz(Q);
    z8(VA().hasAcknowledgedCostThreshold || !1), AA(!1)
  }
  async function OZ(p1, JA, ZA) {
    if (t((iA)  = > [...iA, ...p1]), o4(0), ZA) fV.handleQueryStart(x1);
    Bb();
    let $A  =  p1[p1.length - 1];
    if ($A?.type === "user" && typeof $A.message.content === "string") Rw2($A.message.content);
    if (!ZA) {
      v1(null), AA(!1);
      return
    }
    let rA  =  nB([...N1, ...p1], p1, JA),
      [bA, sA, fA]  =  await Promise.all([YS(o1, q), rY(), Sw(!1)]);
    for await (let iA of coreConversationHandler([...N1, ...p1], bA, sA, fA, G7, rA, H3, void 0)) Cp1(iA, (P2)  = > {
      t((F2)  = > [...F2, P2])
    }, (P2)  = > o4((F2)  = > F2 + P2.length), (P2)  = > f1(P2));
    AA(!1)
  }
  KW2(), PX2(N1, N1.length  ==  D?.length), yN2(), j9.useEffect(()  = > {
    if (X0.length < 1) return;
    let p1  =  VA();
    T0({
      ...p1,
      promptQueueUseCount: (p1.promptQueueUseCount ?? 0) + 1
    })
  }, [X0.length]);
  let D7  =  !M1 && m0;
  j9.useEffect(()  = > {
    Op()
  }, [d1, E2]), j9.useEffect(()  = > {
    if (M1) return;
    if (N1.length < 2) return;
    let p1  =  setTimeout(()  = > {
      let JA  =  Date.now() - u21();
      if (!M1 && I1.length === 0 && !o && !T2 && !D7 && !Z0 && JA >= VA().messageIdleNotifThresholdMs) Ad({
        message: "Claude is waiting for your input"
      })
    }, Fr1);
    return ()  = > clearTimeout(p1)
  }, [M1, I1.length, o, T2, D7, Z0, N1]), j9.useEffect(()  = > {
    return $G(), ()  = > {
      fV.shutdown()
    }
  }, []);
  let w3  =  j9.useMemo(()  = > tQ(N1).filter(Z_), [N1]),
    OD  =  j9.useMemo(()  = > tQ(S1).filter(Z_), [S1]),
    TD  =  j9.useMemo(()  = > KN(w3), [w3]),
    PD  =  j9.useMemo(()  = > vF1(w3), [w3]),
    GB  =  j9.useMemo(()  = > bF1(w3), [w3]),
    TZ  =  j9.useCallback((p1)  = > {
      T2?.resolve(p1), setTimeout(()  = > x4(null), 0)
    }, [T2]);
  eN2(async (p1)  = > {
    Y1((JA)  = > JA + 1), a(p1), await K3()
  });
  let [O1, R1]  =  j9.useState(!1);
  if (f === "transcript") return p4.createElement(p4.Fragment, null, p4.createElement(it1, {
    messages: N1,
    normalizedMessageHistory: OD,
    tools: o1,
    verbose: !0,
    toolJSX: null,
    toolUseConfirmQueue: [],
    isMessageSelectorVisible: !1,
    tipOfTheDay: void 0,
    conversationId: H0,
    screen: f,
    screenToggleId: g
  }), p4.createElement(m, {
    alignItems: "center",
    alignSelf: "center",
    borderTopColor: $1().secondaryBorder,
    borderBottom: !1,
    borderLeft: !1,
    borderRight: !1,
    borderStyle: "single",
    marginTop: 1,
    paddingLeft: 2,
    width: "100%"
  }, p4.createElement(y, {
    dimColor: !0
  }, "Showing detailed transcript · Ctrl+R to toggle")));
  return p4.createElement(p4.Fragment, null, p4.createElement(it1, {
    messages: N1,
    normalizedMessageHistory: OD,
    tools: o1,
    verbose: X,
    toolJSX: o,
    toolUseConfirmQueue: I1,
    isMessageSelectorVisible: Z0,
    tipOfTheDay: Y,
    conversationId: H0,
    screen: f,
    screenToggleId: g
  }), p4.createElement(m, {
    flexDirection: "column",
    width: "100%"
  }, !o && I1.length === 0 && !T2 && M1 && p4.createElement(xK1, {
    mode: FA,
    haikuWords: f0,
    currentResponseLength: Q9
  }), o ? o.jsx : null, !1, !o && I1[0] !== void 0 && !Z0 && !T2 && p4.createElement(oz2, {
    onDone: ()  = > E1(([p1, ...JA])  = > JA),
    onReject: z3,
    setToolPermissionContext: IB,
    toolUseConfirm: I1[0],
    toolUseContext: nB(N1, N1, B1 ?? new AbortController),
    verbose: X
  }), !o && I1.length === 0 && !Z0 && !T2 && D7 && p4.createElement(GQ0, {
    onDone: ()  = > {
      L0(!1), z8(!0);
      let p1  =  VA();
      T0({
        ...p1,
        hasAcknowledgedCostThreshold: !0
      }), logMetric("tengu_cost_threshold_acknowledged", { })
    }
  }), O1 && p4.createElement(y71, {
    onDone: async ()  = > {
      await lW(0)
    }
  }), w8 && p4.createElement(KD0, {
    onDone: ()  = > u3(!1),
    installedVersion: r2?.installedVersion ?? null
  }), I1.length === 0 && !o?.shouldHidePromptInput && I && !Z0 && !T2 && !D7 && !O1 && !w8 && p4.createElement(p4
    .Fragment, null, p4.createElement(_N2, {
      debug: B,
      ideSelection: PA,
      getToolUseContext: nB,
      toolPermissionContext: C,
      setToolPermissionContext: IB,
      apiKeyStatus: OQ,
      commands: a1,
      isLoading: M1,
      onExit: async ()  = > {
        if (!await bt1(N1)) {
          await lW(0);
          return
        }
        R1(!0)
      },
      onQuery: OZ,
      verbose: X,
      messages: N1,
      setToolJSX: A1,
      onAutoUpdaterResult: OA,
      autoUpdaterResult: NA,
      input: d1,
      onInputChange: z6,
      mode: IA,
      onModeChange: zA,
      queuedCommands: X0,
      setQueuedCommands: z0,
      submitCount: E2,
      onSubmitCountChange: (p1)  = > {
        return cA(null), g2(p1)
      },
      setIsLoading: AA,
      setAbortController: v1,
      onShowMessageSelector: ()  = > h0((p1)  = > !p1),
      notification: r,
      addNotification: w1,
      mcpClients: H1,
      pastedImagesFromSelector: s2,
      vimMode: zB,
      setVimMode: H6,
      ideInstallationStatus: r2
    }))), Z0 && p4.createElement(AV2, {
    erroredToolUseIDs: GB,
    unresolvedToolUseIDs: TD,
    messages: N1,
    onSelect: async (p1)  = > {
      if (h0(!1), !N1.includes(p1)) return;
      N9(), setImmediate(async ()  = > {
        await K3();
        let JA  =  N1.indexOf(p1),
          ZA  =  N1.slice(0, JA);
        if (t([...ZA]), j2(rt1()), typeof p1.message.content === "string") {
          let $A  =  XG(p1.message.content, "bash-input");
          if ($A) e1($A), zA("bash");
          else e1(p1.message.content), zA("prompt")
        } else if (Array.isArray(p1.message.content) && p1.message.content.length >= 2 && p1.message
          .content.some(($A)  = > $A.type === "image") && p1.message.content.some(($A)  = > $A.type ===
            "text")) {
          let $A  =  p1.message.content.find((bA)  = > bA.type === "text");
          if ($A && $A.type === "text") e1($A.text), zA("prompt");
          let rA  =  p1.message.content.filter((bA)  = > bA.type === "image");
          if (rA.length > 0) {
            let bA  =  rA.flatMap((sA)  = > sA.source.type === "base64" ? [sA.source.data] : []);
            B2(bA)
          }
        }
      })
    },
    onEscape: ()  = > h0(!1),
    tools: o1
  }), !w8 && p4.createElement(GG, null))
}
var M$2  =  J1(q$2(), 1),
  {
    program: CIB,
    createCommand: XIB,
    createArgument: VIB,
    createOption: KIB,
    CommanderError: HIB,
    InvalidArgumentError: zIB,
    InvalidOptionArgumentError: wIB,
    Command: L$2,
    Argument: EIB,
    Option: hd,
    Help: UIB
  }  =  M$2.default;
var BI5  =  i.object({
    behavior: i.literal("allow"),
    updatedInput: i.record(i.unknown())
  }),
  QI5  =  i.object({
    behavior: i.literal("deny"),
    message: i.string()
  }),
  R$2  =  i.union([BI5, QI5]);

function O$2(A, B) {
  let Q  =  {
    type: "permissionPromptTool",
    permissionPromptToolName: B,
    toolResult: A
  };
  switch (A.behavior) {
    case "allow":
      return {
        ...A, decisionReason: Q
      };
    case "deny":
      return {
        ...A, decisionReason: Q, ruleSuggestions: null
      }
  }
}
import {
  randomUUID as T$2
} from "node:crypto";
async function* nonInteractiveSessionHandler({
  commands: A,
  permissionContext: B,
  prompt: Q,
  cwd: I,
  tools: G,
  mcpClients: D,
  verbose: Z  =  !1,
  maxTurns: Y,
  permissionPromptTool: W,
  initialMessages: F  =  [],
  customSystemPrompt: J,
  appendSystemPrompt: C,
  userSpecifiedModel: X
}) {
  dW(I);
  let V  =  Date.now(),
    K  =  G.filter((w1)  = > w1 !== W),
    U  =  X ? parseUserSpecifiedModel(X) : await getDefaultModel(),
    [N, q, M]  =  await Promise.all([YS(K, U), rY(), Sw(!0)]),
    R  =  [...J ? [J] : N, ...C ? [C] : []],
    T  =  II5(F),
    O  =  {
      messages: T,
      setMessages: ()  = > { },
      onChangeAPIKey: ()  = > { },
      options: {
        commands: A,
        debug: !1,
        tools: K,
        verbose: Z,
        mainLoopModel: U,
        maxThinkingTokens: Ej(T),
        mcpClients: D,
        ideInstallationStatus: null,
        isNonInteractiveSession: !0
      },
      getToolPermissionContext: ()  = > B,
      getQueuedCommands: ()  = > [],
      removeQueuedCommands: ()  = > { },
      abortController: new AbortController,
      readFileState: { },
      setToolPermissionContext: ()  = > { }
    },
    S  =  [...T, ...(await vd(Q, "prompt", ()  = > { }, {
      ...O,
      messages: T
    }, null, null)).messages],
    f  =  Ej(T);
  if (f > 0) O  =  {
    messages: S,
    setMessages: ()  = > { },
    onChangeAPIKey: ()  = > { },
    options: {
      commands: A,
      debug: !1,
      tools: K,
      verbose: Z,
      mainLoopModel: U,
      maxThinkingTokens: f,
      mcpClients: D,
      ideInstallationStatus: null,
      isNonInteractiveSession: !0
    },
    getToolPermissionContext: ()  = > B,
    abortController: new AbortController,
    readFileState: { },
    setToolPermissionContext: ()  = > { },
    getQueuedCommands: ()  = > [],
    removeQueuedCommands: ()  = > { }
  };
  let a  =  async (w1, H1, x, F1)  = > {
    let x1  =  await vh(w1, H1, x, F1);
    if (x1.behavior === "allow" || x1.behavior === "deny") return x1;
    if (W)
      for await (let o1 of W.call({
        tool_name: w1.name,
        input: H1
      }, x, a, F1)) {
        if (o1.type !== "result") continue;
        let a1  =  W.mapToolResultToToolResultBlockParam(o1.data, "1");
        if (!a1.content || !Array.isArray(a1.content) || !a1.content[0] || a1.content[0].type !== "text" ||
          typeof a1.content[0].text !== "string") throw new Error(
          'Permission prompt tool returned an invalid result. Expected a single text block param with type = "text" and a string text value.'
          );
        return O$2(R$2.parse(T8(a1.content[0].text)), W.name)
      }
    return x1
  };
  yield {
    type: "system",
    subtype: "init",
    session_id: B5,
    tools: K.map((w1)  = > w1.name),
    mcp_servers: D.map((w1)  = > ({
      name: w1.name,
      status: w1.type
    }))
  };
  let g  =  0;
  for await (let w1 of mainConversationLoop(S, R, q, M, a, O)) {
    switch (S.push(w1), y41(S), w1.type) {
      case "assistant":
        yield* P$2(w1);
        break;
      case "user":
        yield* P$2(w1);
        break;
      case "progress":
      case "attachment":
        break
    }
    if (w1.type === "user" && Y && ++g >= Y) {
      yield {
        type: "result",
        subtype: "error_max_turns",
        cost_usd: wH(),
        duration_ms: Date.now() - V,
        duration_api_ms: wk(),
        is_error: !1,
        num_turns: g,
        session_id: B5,
        total_cost: wH()
      };
      return
    }
  }
  let Y1  =  AY(S);
  if (!Y1 || Y1.type !== "assistant") throw new Error(
    `Expected content to be an assistant message, but got ${ JSON.stringify(Y1?.type, null, 2) }`);
  let r  =  AY(Y1.message.content);
  if (r?.type !== "text" && r?.type !== "thinking" && r?.type !== "redacted_thinking") throw new Error(
    `Expected first content item to be text or thinking, but got ${ JSON.stringify(Y1.message.content[0], null, 2) }`);
  yield {
    type: "result",
    subtype: "success",
    cost_usd: wH(),
    is_error: Boolean(Y1.isApiErrorMessage),
    duration_ms: Date.now() - V,
    duration_api_ms: wk(),
    num_turns: S.length - 1,
    result: r.type === "text" ? r.text : "",
    total_cost: wH(),
    session_id: B5
  }
}

function* P$2(A) {
  switch (A.type) {
    case "assistant":
      for (let B of tQ([A])) yield {
        type: "assistant",
        message: B.message,
        session_id: B5
      };
      break;
    case "user":
      for (let B of tQ([A])) yield {
        type: "user",
        message: B.message,
        session_id: B5
      };
      break;
    default:
  }
}

function II5(messages) {
  return A.flatMap((B)  = > {
    switch (B.type) {
      case "assistant":
        return [{
          type: "assistant",
          message: B.message,
          costUSD: 0,
          durationMs: 0,
          uuid: T$2(),
          timestamp: new Date().toISOString()
        }];
      case "user":
        return [{
          type: "user",
          message: B.message,
          uuid: T$2(),
          timestamp: new Date().toISOString()
        }];
      default:
        return []
    }
  })
}

function _$2(A) {
  return A.flatMap((B)  = > {
    switch (B.type) {
      case "assistant":
        return [{
          type: "assistant",
          message: B.message,
          session_id: B5
        }];
      case "user":
        return [{
          type: "user",
          message: B.message,
          session_id: B5
        }];
      default:
        return []
    }
  })
}
var NG  =  J1(_1(), 1);

function j$2({
  servers: A,
  scope: B,
  onDone: Q
}) {
  let I  =  $1(),
    G  =  Object.keys(A),
    D  =  NG.useMemo(()  = > KV(), []),
    Z  =  G.filter((F)  = > D[F] !== void 0);

  function Y(enableCaching) {
    let J  =  0;
    for (let C of F) {
      let X  =  A[C];
      if (X) {
        let V  =  C;
        if (D[V] !== void 0) {
          let K  =  1;
          while (D[`${ C }_${ K }`] !== void 0) K++;
          V  =  `${ C }_${ K }`
        }
        Sb(V, X, B), J++
      }
    }
    Q(J)
  }
  let W  =  v2();
  return J0((F, J)  = > {
    if (J.escape) {
      Q(0);
      return
    }
  }), NG.default.createElement(NG.default.Fragment, null, NG.default.createElement(m, {
      flexDirection: "column",
      gap: 1,
      padding: 1,
      borderStyle: "round",
      borderColor: I.success
    }, NG.default.createElement(y, {
      bold: !0,
      color: I.success
    }, "Import MCP Servers from Claude Desktop"), NG.default.createElement(y, null, "Found ", G.length,
      " MCP server", G.length !== 1 ? "s" : "", " in Claude Desktop."), Z.length > 0 && NG.default.createElement(
      y, {
        color: I.warning
      },
      "Note: Some servers already exist with the same name. If selected, they will be imported with a numbered suffix."
      ), NG.default.createElement(y, null, "Please select the servers you want to import:"), NG.default
    .createElement(J71, {
      options: G.map((F)  = > ({
        label: `${ F }${ Z.includes(F)?" (already exists)":"" }`,
        value: F
      })),
      defaultValue: G.filter((F)  = > !Z.includes(F)),
      onSubmit: Y
    })), NG.default.createElement(m, {
    marginLeft: 3
  }, NG.default.createElement(y, {
      dimColor: !0
    }, W.pending ? NG.default.createElement(NG.default.Fragment, null, "Press ", W.keyName, " again to exit") : NG
    .default.createElement(NG.default.Fragment, null, "Space to select · Enter to confirm · Esc to cancel"))))
}
import * as De1 from "path";
import * as y$2 from "os";

function GI5() {
  let A  =  UJ();
  if (!qM1.includes(A)) throw new Error(
    `Unsupported platform: ${ A } - Claude Desktop integration only works on macOS and WSL.`);
  if (A === "macos") return De1.join(y$2.homedir(), "Library", "Application Support", "Claude",
    "claude_desktop_config.json");
  let B  =  process.env.USERPROFILE ? process.env.USERPROFILE.replace(/\\/g, "/") : null;
  if (B) {
    let I  =  `/mnt/c${ B.replace(/^[A-Z]:/, "") }/AppData/Roaming/Claude/claude_desktop_config.json`;
    if (b1().existsSync(I)) return I
  }
  try {
    if (b1().existsSync("/mnt/c/Users")) {
      let I  =  b1().readdirSync("/mnt/c/Users");
      for (let G of I) {
        if (G.name === "Public" || G.name === "Default" || G.name === "Default User" || G.name === "All Users")
      continue;
        let D  =  De1.join("/mnt/c/Users", G.name, "AppData", "Roaming", "Claude", "claude_desktop_config.json");
        if (b1().existsSync(D)) return D
      }
    }
  } catch (Q) {
    logError(Q instanceof Error ? Q : new Error(String(Q)))
  }
  throw new Error(
    "Could not find Claude Desktop config file in Windows. Make sure Claude Desktop is installed on Windows.")
}

function k$2() {
  if (!qM1.includes(UJ())) throw new Error(
    "Unsupported platform - Claude Desktop integration only works on macOS and WSL.");
  try {
    let A  =  GI5();
    if (!b1().existsSync(A)) return { };
    let B  =  b1().readFileSync(A, {
        encoding: "utf8"
      }),
      Q  =  T8(B);
    if (!Q || typeof Q !== "object") return { };
    let I  =  Q.mcpServers;
    if (!I || typeof I !== "object") return { };
    let G  =  { };
    for (let [D, Z] of Object.entries(I)) {
      if (!Z || typeof Z !== "object") continue;
      let Y  =  yj1.safeParse(Z);
      if (Y.success) G[D]  =  Y.data
    }
    return G
  } catch (A) {
    return logError(A instanceof Error ? A : new Error(String(A))), { }
  }
}
import {
  cwd as AO
} from "process";

function DI5(messages, systemPrompt, userPrompt) {
  if (Q.type !== "assistant") return;
  if (!Array.isArray(Q.message.content)) return;
  for (let I of Q.message.content) {
    if (I.type !== "tool_use") continue;
    let G  =  A.find((D)  = > D.name === I.name);
    if (G) B.set(I.id, G)
  }
}

function re(messages, systemPrompt) {
  try {
    let Q  =  Mu0(A);
    if (Q[Q.length - 1]?.type === "user") Q.push(D_({
      content: ur
    }));
    let G  =  new Map;
    for (let D of Q) DI5(B, G, D);
    return Q
  } catch (Q) {
    throw logError(Q), Q
  }
}
var $0  =  J1(_1(), 1);
import {
  join as x$2
} from "path";

function ZH1() {
  let A  =  b1(),
    B  =  uA(),
    Q  =  x$2(S5, "projects", B.replace(/[^a-zA-Z0-9]/g, "-"));
  if (!A.existsSync(Q)) return !1;
  let G  =  A.readdirSync(Q).filter((Z)  = > Z.name.endsWith(".jsonl")).map((Z)  = > x$2(Q, Z.name));
  if (G.length === 0) return !1;
  let D  =  new Date("2025-05-12");
  for (let Z of G) try {
    if (A.statSync(Z).birthtime < D) return !0
  } catch {
    continue
  }
  return !1
}

function f$2({
  onDismiss: A
}) {
  let {
    columns: B
  }  =  D4();
  if ($0.default.useEffect(()  = > {
      logMetric("tengu_ga_announcement_shown", { })
    }, []), $0.default.useEffect(()  = > {
      let Q  =  ()  = > {
        A()
      };
      return process.stdin.on("data", Q), ()  = > {
        process.stdin.off("data", Q)
      }
    }, [A]), B < 50) return $0.default.createElement(FI5, {
    onDismiss: A
  });
  else if (B < 84) return $0.default.createElement(WI5, {
    onDismiss: A
  });
  else return $0.default.createElement(ZI5, {
    onDismiss: A
  })
}

function ZI5({
  onDismiss: A
}) {
  let B  =  $1(),
    {
      columns: Q
    }  =  D4(),
    I  =  [{
      toolName: "Read",
      usesTx: "47.5M",
      usesN: 47500000
    }, {
      toolName: "Edit",
      usesTx: "39.3M",
      usesN: 39300000
    }, {
      toolName: "Bash",
      usesTx: "17.9M",
      usesN: 17900000
    }, {
      toolName: "Grep",
      usesTx: "14.7M",
      usesN: 14700000
    }, {
      toolName: "Write",
      usesTx: "6.8M",
      usesN: 6800000
    }];
  return $0.default.createElement(m, {
      flexDirection: "column",
      gap: 1,
      width: Q
    }, $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.claude,
      paddingLeft: 1,
      paddingRight: 1
    }, $0.default.createElement(Ye1, null)), $0.default.createElement(m, null, $0.default.createElement(We1, null)),
    $0.default.createElement(m, {
      gap: 1,
      flexDirection: "row"
    }, $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.secondaryBorder,
      flexDirection: "column",
      paddingLeft: 1,
      paddingRight: 1,
      flexGrow: 1,
      flexBasis: 0
    }, $0.default.createElement(y, {
      bold: !0,
      color: B.text
    }, "CLAUDE CODE IN NUMBERS"), $0.default.createElement(m, {
      flexDirection: "column",
      marginTop: 1
    }, $0.default.createElement(m, null, $0.default.createElement(y, null, "115 K ", $0.default.createElement(
    y, {
      color: B.remember
    }, "developers"))), $0.default.createElement(m, null, $0.default.createElement(y, null, "195 M "), $0
      .default.createElement(y, {
        color: B.success
      }, "lines of code changed last week")))), $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.secondaryBorder,
      flexDirection: "column",
      paddingLeft: 1,
      paddingRight: 1,
      flexGrow: 1,
      flexBasis: 0
    }, $0.default.createElement(y, {
      bold: !0,
      color: B.text
    }, "CLAUDE CODE IN VIBES"), $0.default.createElement(m, {
      flexDirection: "column",
      marginTop: 1
    }, $0.default.createElement(m, null, $0.default.createElement(y, null, "Billions of"), $0.default
      .createElement(y, {
        color: B.claude
      }, " reticulations")), $0.default.createElement(m, null, $0.default.createElement(y, null,
      "81% of devs "), $0.default.createElement(y, {
        color: B.bashBorder
      }, "auto-accepting"))))), $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.secondaryBorder,
      flexDirection: "column",
      paddingLeft: 1,
      paddingRight: 1
    }, $0.default.createElement(y, {
      bold: !0,
      color: B.text
    }, "CLAUDE’S TOP TOOLS"), $0.default.createElement(m, {
      marginTop: 1
    }, $0.default.createElement(Ze1, {
      stats: I,
      width: Q - 3
    }))), $0.default.createElement(m, {
      marginTop: 1
    }, $0.default.createElement(y, {
      color: B.remember
    }, "Press Enter to continue")))
}

function Ze1({
  stats: messages,
  width: B
}) {
  let Q  =  Math.max(...A.map((Z)  = > Z.usesN)),
    G  =  Math.max(...A.map((Z)  = > Z.toolName.length)) + 5,
    D  =  B - G - 2;
  return $0.default.createElement(m, {
    flexDirection: "column",
    gap: 1
  }, A.map((Z, Y)  = > {
    let W  =  " ".repeat(G - Z.toolName.length),
      F  =  Z.usesN / Q;
    return $0.default.createElement(m, {
      key: Y,
      flexDirection: "row"
    }, $0.default.createElement(y, null, Z.toolName, ":", W, $0.default.createElement(YI5, {
      width: D,
      percent: F,
      text: Z.usesTx
    })))
  }))
}

function YI5({
  width: messages,
  percent: systemPrompt,
  text: Q
}) {
  let I  =  $1(),
    {
      theme: G
    }  =  VA(),
    D  =  G.startsWith("dark") ? "rgb(30, 30, 30)" : "rgb(220, 220, 220)",
    Z  =  Math.ceil(A * B),
    Y  =  A - Z,
    W  =  Math.max(0, Z - Q.length - 1),
    F  =  " " + Q + " ".repeat(W),
    J  =  " ".repeat(Math.max(0, Y)),
    C  =  $0.default.createElement(y, {
      backgroundColor: I.claude
    }, F);
  return $0.default.createElement(y, null, C, $0.default.createElement(y, {
    backgroundColor: D
  }, J))
}

function WI5({
  onDismiss: A
}) {
  let B  =  $1(),
    {
      columns: Q
    }  =  D4(),
    I  =  [{
      toolName: "Read",
      usesTx: "47.5M",
      usesN: 47500000
    }, {
      toolName: "Edit",
      usesTx: "39.3M",
      usesN: 39300000
    }, {
      toolName: "Bash",
      usesTx: "17.9M",
      usesN: 17900000
    }, {
      toolName: "Grep",
      usesTx: "14.7M",
      usesN: 14700000
    }, {
      toolName: "Write",
      usesTx: "6.8M",
      usesN: 6800000
    }];
  return $0.default.createElement(m, {
      flexDirection: "column",
      gap: 1,
      width: Q
    }, $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.claude,
      paddingLeft: 1,
      paddingRight: 1
    }, $0.default.createElement(Ye1, null)), $0.default.createElement(m, null, $0.default.createElement(We1, null)),
    $0.default.createElement(m, {
      flexDirection: "column",
      gap: 1
    }, $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.secondaryBorder,
      flexDirection: "column",
      paddingLeft: 1,
      paddingRight: 1
    }, $0.default.createElement(y, {
      bold: !0,
      color: B.text
    }, "CLAUDE CODE IN NUMBERS"), $0.default.createElement(m, {
      marginTop: 1
    }, $0.default.createElement(y, null, "115 K ", $0.default.createElement(y, {
      color: B.remember
    }, "developers"))), $0.default.createElement(m, null, $0.default.createElement(y, null, "195 M "), $0.default
      .createElement(y, {
        color: B.success
      }, "lines of code changed last week"))), $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.secondaryBorder,
      flexDirection: "column",
      paddingLeft: 1,
      paddingRight: 1
    }, $0.default.createElement(y, {
      bold: !0,
      color: B.text
    }, "CLAUDE CODE IN VIBES"), $0.default.createElement(m, {
      marginTop: 1
    }, $0.default.createElement(y, null, "Billions of"), $0.default.createElement(y, {
      color: B.claude
    }, " reticulations")), $0.default.createElement(m, null, $0.default.createElement(y, null, "81% of devs "), $0
      .default.createElement(y, {
        color: B.bashBorder
      }, "auto-accepting"))), $0.default.createElement(m, {
      borderStyle: "round",
      borderColor: B.secondaryBorder,
      flexDirection: "column",
      paddingLeft: 1,
      paddingRight: 1
    }, $0.default.createElement(y, {
      bold: !0,
      color: B.text
    }, "CLAUDE’S TOP TOOLS"), $0.default.createElement(m, {
      marginTop: 1
    }, $0.default.createElement(Ze1, {
      stats: I,
      width: Q - 3
    })))), $0.default.createElement(m, {
      marginTop: 1
    }, $0.default.createElement(y, {
      color: B.remember
    }, "Press Enter to continue")))
}

function FI5({
  onDismiss: A
}) {
  let B  =  $1(),
    {
      columns: Q
    }  =  D4(),
    I  =  [{
      toolName: "Read",
      usesTx: "47.5M",
      usesN: 47500000
    }, {
      toolName: "Edit",
      usesTx: "39.3M",
      usesN: 39300000
    }, {
      toolName: "Bash",
      usesTx: "17.9M",
      usesN: 17900000
    }, {
      toolName: "Grep",
      usesTx: "14.7M",
      usesN: 14700000
    }, {
      toolName: "Write",
      usesTx: "6.8M",
      usesN: 6800000
    }];
  return $0.default.createElement(m, {
    flexDirection: "column",
    gap: 1,
    width: Q
  }, $0.default.createElement(Ye1, null), $0.default.createElement(We1, null), $0.default.createElement(y, {
    bold: !0,
    color: B.text
  }, "CLAUDE CODE IN NUMBERS"), $0.default.createElement(y, null, "115 K ", $0.default.createElement(y, {
    color: B.remember
  }, "developers")), $0.default.createElement(y, null, "195 M ", $0.default.createElement(y, {
    color: B.success
  }, "lines of code changed last week")), $0.default.createElement(y, {
    bold: !0,
    color: B.text
  }, "CLAUDE CODE IN VIBES"), $0.default.createElement(y, null, "Billions of ", $0.default.createElement(y, {
    color: B.claude
  }, "reticulations")), $0.default.createElement(y, null, "81% of devs ", $0.default.createElement(y, {
    color: B.bashBorder
  }, "auto-accepting")), $0.default.createElement(y, {
    bold: !0,
    color: B.text
  }, "CLAUDE’S TOP TOOLS"), $0.default.createElement(m, null, $0.default.createElement(Ze1, {
    stats: I,
    width: Q
  })), $0.default.createElement(m, {
    marginTop: 1
  }, $0.default.createElement(y, {
    color: B.remember
  }, "Press Enter to continue")))
}

function Ye1() {
  let A  =  $1();
  return $0.default.createElement(y, null, $0.default.createElement(y, {
    color: A.claude
  }, "✻ "), $0.default.createElement(y, null, "Welcome to Claude Code"))
}

function We1() {
  return $0.default.createElement(m, {
      flexDirection: "column",
      gap: 1
    }, $0.default.createElement(y, null,
      "Claude Code is now generally available. Thank you for making it possible \uD83D\uDE4F"), $0.default
    .createElement(y, null, "Here's a glimpse at all of the community's contributions:"))
}
var $I  =  J1(_1(), 1);
import {
  homedir as v$2
} from "os";

function b$2({
  onDone: A
}) {
  let B  =  $1(),
    Q  =  zU(),
    I  =  Object.keys(Q).length > 0;
  $I.default.useEffect(()  = > {
    let Z  =  v$2() === uA();
    logMetric("trust_dialog_shown", {
      isHomeDir: Z,
      hasMcpServers: I
    })
  }, [I]);

  function G(temperature) {
    let Y  =  E9();
    if (Z === "no") process.exit(1);
    let W  =  Z === "yes_enable_mcp",
      F  =  v$2() === uA();
    if (logMetric("trust_dialog_accept", {
        isHomeDir: F,
        hasMcpServers: I,
        enableMcp: W
      }), I) j6({
      ...Y,
      ...F ? { } : {
        hasTrustDialogAccepted: !0
      },
      ...W ? {
        enabledMcpjsonServers: Object.keys(Q),
        enableAllProjectMcpServers: !0
      } : {
        disabledMcpjsonServers: Object.keys(Q)
      }
    });
    else if (!F) j6({
      ...Y,
      hasTrustDialogAccepted: !0
    });
    A()
  }
  let D  =  v2();
  return J0((Z, Y)  = > {
    if (Y.escape) {
      process.exit(0);
      return
    }
  }), $I.default.createElement($I.default.Fragment, null, $I.default.createElement(m, {
    flexDirection: "column",
    gap: 1,
    padding: 1,
    borderStyle: "round",
    borderColor: B.warning
  }, $I.default.createElement(y, {
    bold: !0,
    color: B.warning
  }, "Do you trust the files in this folder?"), $I.default.createElement(y, {
    bold: !0
  }, b1().cwd()), $I.default.createElement(m, {
    flexDirection: "column",
    gap: 1
  }, $I.default.createElement(y, null, x0, " may read files in this folder. Reading untrusted files may lead ",
    x0, " to behave in an unexpected ways."), $I.default.createElement(y, null, "With your permission ", x0,
    " may execute files in this folder.", I &&
    " This project also contains MCP servers defined in .mcp.json that can execute code on your machine if enabled.",
    " ", "Executing untrusted code is unsafe."), $I.default.createElement(M7, {
    url: "https://docs.anthropic.com/s/claude-code-security"
  })), $I.default.createElement(B9, {
    options: I ? [{
      label: "Yes, proceed with MCP servers enabled",
      value: "yes_enable_mcp"
    }, {
      label: "Yes, proceed with MCP servers disabled",
      value: "yes_disable_mcp"
    }, {
      label: "No, exit",
      value: "no"
    }] : [{
      label: "Yes, proceed",
      value: "yes_enable_mcp"
    }, {
      label: "No, exit",
      value: "no"
    }],
    onChange: (Z)  = > G(Z),
    onCancel: ()  = > G("no")
  })), $I.default.createElement(m, {
    marginLeft: 3
  }, $I.default.createElement(y, {
      dimColor: !0
    }, D.pending ? $I.default.createElement($I.default.Fragment, null, "Press ", D.keyName, " again to exit") : $I
    .default.createElement($I.default.Fragment, null, "Enter to confirm · Esc to exit"))))
}
var Je1  =  J1(_1(), 1);
var RZ  =  J1(_1(), 1);

function Fe1({
  logs: messages,
  onSelect: B
}) {
  let {
    rows: Q,
    columns: I
  }  =  D4(), G  =  RZ.useMemo(()  = > {
    let X  =  A.filter((V)  = > V.leafUuid).map((V)  = > V.leafUuid);
    return qRA(X)
  }, [A]);
  if (A.length === 0) return null;
  let D  =  Q - 3,
    Z  =  Math.max(0, A.length - D),
    Y  =  12,
    W  =  12,
    F  =  10,
    J  =  A.map((X)  = > {
      let V  =  $logMetric(X.modified).padEnd(Y),
        K  =  $logMetric(X.created).padEnd(W),
        U  =  `${ X.messageCount }`.padStart(F),
        N  =  X.leafUuid && G.get(X.leafUuid) || X.firstPrompt,
        q  =  X.isSidechain ? " (sidechain)" : "",
        M  =  `${ V }${ K }${ U } ${ N }${ q }`;
      return {
        label: M.length > I - 2 ? `${ M.slice(0, I-5) }...` : M,
        value: X.value.toString()
      }
    }),
    C  =  A.length.toString().length;
  return RZ.default.createElement(m, {
    flexDirection: "column",
    height: Q - 1
  }, RZ.default.createElement(m, {
    paddingLeft: 3 + C
  }, RZ.default.createElement(y, {
    bold: !0,
    color: $1().text
  }, "Modified"), RZ.default.createElement(y, null, "    "), RZ.default.createElement(y, {
    bold: !0,
    color: $1().text
  }, "Created"), RZ.default.createElement(y, null, "     "), RZ.default.createElement(y, {
    bold: !0,
    color: $1().text
  }, "# Messages"), RZ.default.createElement(y, null, " "), RZ.default.createElement(y, {
    bold: !0,
    color: $1().text
  }, "Summary")), RZ.default.createElement(B9, {
    options: J,
    onChange: (X)  = > B(parseInt(X, 10)),
    visibleOptionCount: D,
    onCancel: ()  = > process.exit(0)
  }), Z > 0 && RZ.default.createElement(m, {
    paddingLeft: 2
  }, RZ.default.createElement(y, {
    color: $1().secondaryText
  }, "and ", Z, " more…")))
}
var YH1  =  J1(_1(), 1);

function g$2({
  context: A,
  commands: B,
  logs: Q,
  initialTools: I,
  mcpClients: G,
  dynamicMcpConfig: D,
  appState: Z,
  onChangeAppState: Y,
  debug: W
}) {
  let F  =  Q.filter((C)  = > !C.isSidechain);
  v2();
  async function J(processedMessages) {
    let X  =  Q[C];
    if (!X) return;
    try {
      A.unmount?.(), GS(X);
      let V  =  IV();
      await K3(), S8(YH1.default.createElement(WQ, {
        initialState: Z,
        onChangeAppState: Y
      }, YH1.default.createElement(gd, {
        initialPrompt: "",
        debug: W,
        shouldShowPromptInput: !0,
        commands: B,
        initialTools: I,
        initialMessages: re(X.messages, I),
        initialTodos: V,
        mcpClients: G,
        dynamicMcpConfig: D
      })), {
        exitOnCtrlC: !1
      })
    } catch (V) {
      throw logError(V), V
    }
  }
  return YH1.default.createElement(Fe1, {
    logs: F,
    onSelect: J
  })
}
class Ce1 extends Fa {
  constructor(A, B) {
    var Q;
    super(B);
    this._serverInfo  =  A, this._capabilities  =  (Q  =  B === null || B === void 0 ? void 0 : B.capabilities) !== null &&
      Q !== void 0 ? Q : { }, this._instructions  =  B === null || B === void 0 ? void 0 : B.instructions, this
      .setRequestHandler(wx1, (I)  = > this._oninitialize(I)), this.setNotificationHandler(Ux1, ()  = > {
        var I;
        return (I  =  this.oninitialized) === null || I === void 0 ? void 0 : I.call(this)
      })
  }
  registerCapabilities(A) {
    if (this.transport) throw new Error("Cannot register capabilities after connecting to transport");
    this._capabilities  =  cI1(this._capabilities, A)
  }
  assertCapabilityForMethod(A) {
    var B, Q;
    switch (A) {
      case "sampling/createMessage":
        if (!((B  =  this._clientCapabilities) === null || B === void 0 ? void 0 : B.sampling)) throw new Error(
          `Client does not support sampling (required for ${ A })`);
        break;
      case "roots/list":
        if (!((Q  =  this._clientCapabilities) === null || Q === void 0 ? void 0 : Q.roots)) throw new Error(
          `Client does not support listing roots (required for ${ A })`);
        break;
      case "ping":
        break
    }
  }
  assertNotificationCapability(A) {
    switch (A) {
      case "notifications/message":
        if (!this._capabilities.logging) throw new Error(`Server does not support logging (required for ${ A })`);
        break;
      case "notifications/resources/updated":
      case "notifications/resources/list_changed":
        if (!this._capabilities.resources) throw new Error(
          `Server does not support notifying about resources (required for ${ A })`);
        break;
      case "notifications/tools/list_changed":
        if (!this._capabilities.tools) throw new Error(
          `Server does not support notifying of tool list changes (required for ${ A })`);
        break;
      case "notifications/prompts/list_changed":
        if (!this._capabilities.prompts) throw new Error(
          `Server does not support notifying of prompt list changes (required for ${ A })`);
        break;
      case "notifications/cancelled":
        break;
      case "notifications/progress":
        break
    }
  }
  assertRequestHandlerCapability(A) {
    switch (A) {
      case "sampling/createMessage":
        if (!this._capabilities.sampling) throw new Error(`Server does not support sampling (required for ${ A })`);
        break;
      case "logging/setLevel":
        if (!this._capabilities.logging) throw new Error(`Server does not support logging (required for ${ A })`);
        break;
      case "prompts/get":
      case "prompts/list":
        if (!this._capabilities.prompts) throw new Error(`Server does not support prompts (required for ${ A })`);
        break;
      case "resources/list":
      case "resources/templates/list":
      case "resources/read":
        if (!this._capabilities.resources) throw new Error(`Server does not support resources (required for ${ A })`);
        break;
      case "tools/call":
      case "tools/list":
        if (!this._capabilities.tools) throw new Error(`Server does not support tools (required for ${ A })`);
        break;
      case "ping":
      case "initialize":
        break
    }
  }
  async _oninitialize(A) {
    let B  =  A.params.protocolVersion;
    return this._clientCapabilities  =  A.params.capabilities, this._clientVersion  =  A.params.clientInfo, {
      protocolVersion: jI1.includes(B) ? B : FS,
      capabilities: this.getCapabilities(),
      serverInfo: this._serverInfo,
      ...this._instructions && {
        instructions: this._instructions
      }
    }
  }
  getClientCapabilities() {
    return this._clientCapabilities
  }
  getClientVersion() {
    return this._clientVersion
  }
  getCapabilities() {
    return this._capabilities
  }
  async ping() {
    return this.request({
      method: "ping"
    }, kU)
  }
  async createMessage(A, B) {
    return this.request({
      method: "sampling/createMessage",
      params: A
    }, Lx1, B)
  }
  async listRoots(A, B) {
    return this.request({
      method: "roots/list",
      params: A
    }, Tx1, B)
  }
  async sendLoggingMessage(A) {
    return this.notification({
      method: "notifications/message",
      params: A
    })
  }
  async sendResourceUpdated(A) {
    return this.notification({
      method: "notifications/resources/updated",
      params: A
    })
  }
  async sendResourceListChanged() {
    return this.notification({
      method: "notifications/resources/list_changed"
    })
  }
  async sendToolListChanged() {
    return this.notification({
      method: "notifications/tools/list_changed"
    })
  }
  async sendPromptListChanged() {
    return this.notification({
      method: "notifications/prompts/list_changed"
    })
  }
}
import h$2 from "node:process";
class Xe1 {
  constructor(A  =  h$2.stdin, B  =  h$2.stdout) {
    this._stdin  =  A, this._stdout  =  B, this._readBuffer  =  new Ja, this._started  =  !1, this._ondata  =  (Q)  = > {
      this._readBuffer.append(Q), this.processReadBuffer()
    }, this._onerror  =  (Q)  = > {
      var I;
      (I  =  this.onerror) === null || I === void 0 || I.call(this, Q)
    }
  }
  async start() {
    if (this._started) throw new Error(
      "StdioServerTransport already started! If using Server class, note that connect() calls start() automatically."
      );
    this._started  =  !0, this._stdin.on("data", this._ondata), this._stdin.on("error", this._onerror)
  }
  processReadBuffer() {
    var A, B;
    while (!0) try {
      let Q  =  this._readBuffer.readMessage();
      if (Q === null) break;
      (A  =  this.onmessage) === null || A === void 0 || A.call(this, Q)
    } catch (Q) {
      (B  =  this.onerror) === null || B === void 0 || B.call(this, Q)
    }
  }
  async close() {
    var A;
    if (this._stdin.off("data", this._ondata), this._stdin.off("error", this._onerror), this._stdin.listenerCount(
        "data") === 0) this._stdin.pause();
    this._readBuffer.clear(), (A  =  this.onclose) === null || A === void 0 || A.call(this)
  }
  send(A) {
    return new Promise((B)  = > {
      let Q  =  iI1(A);
      if (this._stdout.write(Q)) B();
      else this._stdout.once("drain", B)
    })
  }
}
var m$2  =  {
    readFileState: { }
  },
  d$2  =  [hK1];
async function u$2(A, B, Q) {
  process.env.CLAUDE_CODE_ENTRYPOINT  =  "mcp", dW(A);
  let I  =  new Ce1({
    name: "claude/tengu",
    version: {
      ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
      PACKAGE_URL: "@anthropic-ai/claude-code",
      README_URL: "https://docs.anthropic.com/s/claude-code",
      VERSION: "1.0.3"
    }.VERSION
  }, {
    capabilities: {
      tools: { }
    }
  });
  I.setRequestHandler(qx1, async ()  = > {
    let D  =  BV(),
      Z  =  $j(D, VA().todoFeatureEnabled);
    return {
      tools: await Promise.all(Z.map(async (Y)  = > ({
        ...Y,
        description: await Y.description({ }, {
          isNonInteractiveSession: !0,
          getToolPermissionContext: ()  = > D,
          tools: Z
        }),
        inputSchema: Vb(Y.inputSchema)
      })))
    }
  }), I.setRequestHandler(Mx1, async ({
    params: {
      name: D,
      arguments: Z
    }
  })  = > {
    let Y  =  $j({
        mode: "default",
        alwaysAllowRules: { },
        alwaysDenyRules: { }
      }, VA().todoFeatureEnabled),
      W  =  Y.find((F)  = > F.name === D);
    if (!W) throw new Error(`Tool ${ D } not found`);
    try {
      if (!W.isEnabled()) throw new Error(`Tool ${ D } is not enabled`);
      let F  =  await getDefaultModel(),
        J  =  await W.validateInput?.(Z ?? { }, {
          abortController: new AbortController,
          options: {
            commands: d$2,
            tools: Y,
            mainLoopModel: F,
            maxThinkingTokens: 0,
            mcpClients: [],
            isNonInteractiveSession: !0,
            debug: B,
            verbose: Q
          },
          getQueuedCommands: ()  = > [],
          getToolPermissionContext: BV,
          removeQueuedCommands: ()  = > { },
          readFileState: m$2.readFileState
        });
      if (J && !J.result) throw new Error(`Tool ${ D } input is invalid: ${ J.message }`);
      let C  =  W.call(Z ?? { }, {
          abortController: new AbortController,
          options: {
            commands: d$2,
            tools: Y,
            mainLoopModel: await getDefaultModel(),
            maxThinkingTokens: 0,
            mcpClients: [],
            isNonInteractiveSession: !0,
            debug: B,
            verbose: Q
          },
          getQueuedCommands: ()  = > [],
          getToolPermissionContext: BV,
          removeQueuedCommands: ()  = > { },
          readFileState: m$2.readFileState
        }, vh, D_({
          content: []
        })),
        X  =  await KF(C);
      if (X.type !== "result") throw new Error(`Tool ${ D } did not return a result`);
      return {
        content: Array.isArray(X) ? X.map((V)  = > ({
          type: "text",
          text: "text" in V ? V.text : JSON.stringify(V)
        })) : [{
          type: "text",
          text: typeof X === "string" ? X : JSON.stringify(X.data)
        }]
      }
    } catch (F) {
      return logError(F instanceof Error ? F : new Error(String(F))), {
        isError: !0,
        content: [{
          type: "text",
          text: `Error: ${ F instanceof Error?F.message:String(F) }`
        }]
      }
    }
  });
  async function G() {
    let D  =  new Xe1;
    await I.connect(D)
  }
  return await G()
}
import {
  join as WH1
} from "path";
var JI5  =  30;

function c$2() {
  let Q  =  (s7().cleanupPeriodDays ?? JI5) * 24 * 60 * 60 * 1000;
  return new Date(Date.now() - Q)
}

function CI5(messages, systemPrompt) {
  return {
    messages: A.messages + B.messages,
    errors: A.errors + B.errors
  }
}

function XI5(messages) {
  let B  =  A.split(".")[0].replace(/T(\d{ 2 })-(\d{ 2 })-(\d{ 2 })-(\d{ 3 })Z/, "T$1:$2:$3.$4Z");
  return new Date(B)
}

function p$2(A, B, Q) {
  let I  =  {
    messages: 0,
    errors: 0
  };
  try {
    let G  =  b1().readdirSync(A);
    for (let D of G) try {
      if (XI5(D.name) < B)
        if (b1().unlinkSync(WH1(A, D.name)), Q) I.messages++;
        else I.errors++
    } catch (Z) {
      logError(Z)
    }
  } catch (G) {
    if (G instanceof Error && "code" in G && G.code !== "ENOENT") logError(G)
  }
  return I
}
async function VI5() {
  let A  =  b1(),
    B  =  c$2(),
    Q  =  MX.messages(),
    I  =  MX.errors(),
    G  =  MX.baseLogs(),
    D  =  p$2(I, B, !1);
  try {
    if (A.existsSync(Q)) A.rmSync(Q, {
      recursive: !0,
      force: !0
    }), D.messages++
  } catch {
    D.errors++
  }
  try {
    if (A.existsSync(G)) {
      let Y  =  A.readdirSync(G).filter((W)  = > W.isDirectory() && W.name.startsWith("mcp-logs-")).map((W)  = > WH1(G, W
        .name));
      for (let W of Y) {
        D  =  CI5(D, p$2(W, B, !0));
        try {
          if (A.isDirEmptySync(W)) A.rmdirSync(W)
        } catch { }
      }
    }
  } catch (Z) {
    if (Z instanceof Error && "code" in Z && Z.code !== "ENOENT") logError(Z)
  }
  return D
}

function KI5() {
  let A  =  c$2(),
    B  =  {
      messages: 0,
      errors: 0
    },
    Q  =  _M1(),
    I  =  b1();
  try {
    if (!I.existsSync(Q)) return B;
    let D  =  I.readdirSync(Q).filter((Z)  = > Z.isDirectory()).map((Z)  = > WH1(Q, Z.name));
    for (let Z of D) try {
      let W  =  I.readdirSync(Z).filter((F)  = > F.isFile() && F.name.endsWith(".jsonl"));
      for (let F of W) try {
        let J  =  WH1(Z, F.name);
        if (I.statSync(J).mtime < A) I.unlinkSync(J), B.messages++
      } catch {
        B.errors++;
        continue
      }
      try {
        if (I.isDirEmptySync(Z)) I.rmdirSync(Z)
      } catch {
        B.errors++
      }
    } catch {
      B.errors++;
      continue
    }
  } catch {
    B.errors++
  }
  return B
}

function l$2() {
  setImmediate(()  = > {
    VI5(), KI5()
  }).unref()
}
var HI5  =  `
Summarize this coding conversation in under 50 characters.
Capture the main task, key files, problems addressed, and current status.
`.trim();
async function zI5(messages) {
  if (!A.length) throw new Error("Can't summarize empty conversation");
  let Q  =  [`Please write a 5-10 word title the following conversation:

${ tQ(A).map((G) = >{ if(G.type==="user"){ if(typeof G.message.content==="string")return`User: ${ G.message.content }`;else if(Array.isArray(G.message.content))return`User: ${ G.message.content.filter((D) = >D.type==="text").map((D) = >D.type==="text"?D.text:"").join(`
`).trim() }` }else if(G.type==="assistant"){ let D = mF1(G);if(D)return`
    Claude: $ {
      cr(D).trim()
    }
    ` }return null }).filter((G) = >G!==null).join(`

    `) }
`, "Respond with the title for the conversation and nothing else."
  ];
  return (await callClaudeAPI({
    systemPrompt: [HI5],
    userPrompt: Q.join(`
`),
    enablePromptCaching: !0,
    isNonInteractiveSession: !1
  })).message.content.filter((G)  = > G.type === "text").map((G)  = > G.text).join("")
}
async function i$2() {
  let A  =  MRA();
  if (A.length === 0) return;
  for (let B of A) {
    let Q  =  B[B.length - 1],
      I  =  await zI5(B);
    try {
      if (I) $RA(Q.uuid, I)
    } catch (G) {
      logError(G instanceof Error ? G : new Error(String(G)))
    }
  }
}
import {
  resolve as MI5
} from "path";
var oe  =  J1(_1(), 1);
var QW  =  J1(_1(), 1);
var Ve1  =  J1(_1(), 1);

function FH1() {
  return Ve1.default.createElement(y, null,
    "MCP servers may execute code or access system resources. All tool calls require approval. Learn more in the",
    " ", Ve1.default.createElement(BS, {
      url: "https://docs.anthropic.com/s/claude-code-mcp"
    }, "MCP documentation"), ".")
}

function n$2({
  serverNames: A,
  onDone: B
}) {
  let Q  =  $1();

  function I(options) {
    let Z  =  E9();
    if (!Z.enabledMcpjsonServers) Z.enabledMcpjsonServers  =  [];
    if (!Z.disabledMcpjsonServers) Z.disabledMcpjsonServers  =  [];
    let [Y, W]  =  RU1(A, (F)  = > D.includes(F));
    logMetric("tengu_mcp_multidialog_choice", {
      approved: Y.length,
      rejected: W.length
    }), Z.enabledMcpjsonServers.push(...Y), Z.disabledMcpjsonServers.push(...W), j6(Z), B()
  }
  let G  =  v2();
  return J0((D, Z)  = > {
    if (Z.escape) {
      let Y  =  E9();
      if (!Y.disabledMcpjsonServers) Y.disabledMcpjsonServers  =  [];
      for (let W of A)
        if (!Y.disabledMcpjsonServers.includes(W)) Y.disabledMcpjsonServers.push(W);
      j6(Y), B();
      return
    }
  }), QW.default.createElement(QW.default.Fragment, null, QW.default.createElement(m, {
    flexDirection: "column",
    gap: 1,
    padding: 1,
    borderStyle: "round",
    borderColor: Q.warning
  }, QW.default.createElement(y, {
    bold: !0,
    color: Q.warning
  }, A.length, " new MCP servers found in .mcp.json"), QW.default.createElement(y, null,
    "Select any you wish to enable."), QW.default.createElement(FH1, null), QW.default.createElement(J71, {
    options: A.map((D)  = > ({
      label: D,
      value: D
    })),
    defaultValue: A,
    onSubmit: I
  })), QW.default.createElement(m, {
    marginLeft: 3
  }, QW.default.createElement(y, {
      dimColor: !0
    }, G.pending ? QW.default.createElement(QW.default.Fragment, null, "Press ", G.keyName, " again to exit") : QW
    .default.createElement(QW.default.Fragment, null, "Space to select · Enter to confirm · Esc to reject all"))))
}
var _F  =  J1(_1(), 1);

function a$2({
  serverName: A,
  onDone: B
}) {
  let Q  =  $1();

  function I(options) {
    let Z  =  E9();
    switch (logMetric("tengu_mcp_dialog_choice", {
        choice: D
      }), D) {
      case "yes":
      case "yes_all": {
        if (!Z.enabledMcpjsonServers) Z.enabledMcpjsonServers  =  [];
        if (!Z.enabledMcpjsonServers.includes(A)) Z.enabledMcpjsonServers.push(A);
        if (D === "yes_all") Z.enableAllProjectMcpServers  =  !0;
        j6(Z), B();
        break
      }
      case "no": {
        if (!Z.disabledMcpjsonServers) Z.disabledMcpjsonServers  =  [];
        if (!Z.disabledMcpjsonServers.includes(A)) Z.disabledMcpjsonServers.push(A);
        j6(Z), B();
        break
      }
    }
  }
  let G  =  v2();
  return J0((D, Z)  = > {
    if (Z.escape) {
      B();
      return
    }
  }), _F.default.createElement(_F.default.Fragment, null, _F.default.createElement(m, {
    flexDirection: "column",
    gap: 1,
    padding: 1,
    borderStyle: "round",
    borderColor: Q.warning
  }, _F.default.createElement(y, {
    bold: !0,
    color: Q.warning
  }, "New MCP server found in .mcp.json: ", A), _F.default.createElement(FH1, null), _F.default.createElement(
  B9, {
    options: [{
      label: "Use this and all future MCP servers in this project",
      value: "yes_all"
    }, {
      label: "Use this MCP server",
      value: "yes"
    }, {
      label: "Continue without using this MCP server",
      value: "no"
    }],
    onChange: (D)  = > I(D),
    onCancel: ()  = > I("no")
  })), _F.default.createElement(m, {
    marginLeft: 3
  }, _F.default.createElement(y, {
      dimColor: !0
    }, G.pending ? _F.default.createElement(_F.default.Fragment, null, "Press ", G.keyName, " again to exit") : _F
    .default.createElement(_F.default.Fragment, null, "Enter to confirm · Esc to reject"))))
}
async function s$2() {
  let A  =  zU(),
    B  =  Object.keys(A).filter((Q)  = > Yf1(Q) === "pending");
  if (B.length === 0) return;
  await new Promise((Q)  = > {
    let I  =  ()  = > {
      process.stdout.write("\x1B[2J\x1B[3J\x1B[H", ()  = > {
        Q()
      })
    };
    if (B.length === 1 && B[0] !== void 0) {
      let G  =  S8(oe.default.createElement(WQ, null, oe.default.createElement(a$2, {
        serverName: B[0],
        onDone: ()  = > {
          G.unmount?.(), I()
        }
      })), {
        exitOnCtrlC: !1
      })
    } else {
      let G  =  S8(oe.default.createElement(WQ, null, oe.default.createElement(n$2, {
        serverNames: B,
        onDone: ()  = > {
          G.unmount?.(), I()
        }
      })), {
        exitOnCtrlC: !1
      })
    }
  })
}
var qI  =  J1(_1(), 1);

function r$2({
  onAccept: A
}) {
  let B  =  $1();
  qI.default.useEffect(()  = > {
    logMetric("bypass_permissions_mode_dialog_shown", { })
  }, []);

  function Q(signal) {
    let D  =  VA();
    switch (G) {
      case "accept": {
        logMetric("bypass_permissions_mode_dialog_accept", { }), T0({
          ...D,
          bypassPermissionsModeAccepted: !0
        }), A();
        break
      }
      case "decline": {
        process.exit(1);
        break
      }
    }
  }
  let I  =  v2();
  return J0((G, D)  = > {
    if (D.escape) {
      process.exit(0);
      return
    }
  }), qI.default.createElement(qI.default.Fragment, null, qI.default.createElement(m, {
    flexDirection: "column",
    gap: 1,
    padding: 1,
    borderStyle: "round",
    borderColor: B.error
  }, qI.default.createElement(y, {
    bold: !0,
    color: B.error
  }, "WARNING: ", x0, " running in Bypass Permissions mode"), qI.default.createElement(m, {
      flexDirection: "column",
      gap: 1
    }, qI.default.createElement(y, null, "In Bypass Permissions mode, ", x0,
      " will not ask for your approval before running potentially dangerous commands.", qI.default.createElement(
        GG, null),
      "This mode should only be used in a sandboxed container/VM that has restricted internet access and can easily be restored if damaged."
      ), qI.default.createElement(y, null,
      "By proceeding, you accept all responsibility for actions taken while running in Bypass Permissions mode."),
    qI.default.createElement(M7, {
      url: "https://docs.anthropic.com/s/claude-code-security"
    })), qI.default.createElement(B9, {
    options: [{
      label: "No, exit",
      value: "decline"
    }, {
      label: "Yes, I accept",
      value: "accept"
    }],
    onChange: (G)  = > Q(G),
    onCancel: ()  = > Q("decline")
  })), qI.default.createElement(m, {
    marginLeft: 3
  }, qI.default.createElement(y, {
      dimColor: !0
    }, I.pending ? qI.default.createElement(qI.default.Fragment, null, "Press ", I.keyName, " again to exit") : qI
    .default.createElement(qI.default.Fragment, null, "Enter to confirm · Esc to exit"))))
}
var JH1  =  J1(_1(), 1);

function o$2() {
  let A  =  $1();
  return JH1.default.createElement(m, {
    flexDirection: "row"
  }, JH1.default.createElement(y, {
    color: A.secondaryText
  }, "※ Tip: Use git worktrees to run multiple Claude sessions in parallel.", " ", JH1.default.createElement(M7, {
    url: "https://docs.anthropic.com/s/claude-code-worktrees"
  }, "Learn more")))
}
var wI5  =  [{
    id: "claude-opus-welcome",
    content: "New! Introducing Claude Opus 4 - our most powerful model yet. Use /model to try it out.",
    cooldownSessions: 1 / 0,
    isRelevant: ()  = > !process.env.IS_DEMO && ZH1()
  }, {
    id: "ide-hotkey",
    content: `${ UJ()==="macos"?"Cmd+Escape":"Ctrl+Escape" } to launch Claude in your IDE`,
    cooldownSessions: 15,
    isRelevant: Hx1
  }, {
    id: "new-user-warmup",
    content: "Start with small features or bug fixes, tell Claude to propose a plan, and verify its suggested edits",
    cooldownSessions: 3,
    isRelevant: ()  = > {
      return VA().numStartups < 10
    }
  }, {
    id: "git-worktrees",
    content: o$2,
    cooldownSessions: 30,
    isRelevant: async ()  = > {
      try {
        let A  =  VA();
        return await Sc() <= 1 && A.numStartups > 50
      } catch (A) {
        return !1
      }
    }
  }, {
    id: "terminal-setup",
    content: dA.terminal === "Apple_Terminal" ?
      "Run /terminal-setup to enable convenient terminal integration like Option + Enter for new line and more" :
      "Run /terminal-setup to enable convenient terminal integration like Shift + Enter for new line and more",
    cooldownSessions: 15,
    isRelevant: ()  = > {
      let A  =  VA();
      if (dA.terminal === "Apple_Terminal") return Dz.isEnabled && !A.optionAsMetaKeyInstalled;
      return Dz.isEnabled && !A.shiftEnterKeyBindingInstalled
    }
  }, {
    id: "shift-enter",
    content: dA.terminal === "Apple_Terminal" ? "Press Option+Enter to send a multi-line message" :
      "Press Shift+Enter to send a multi-line message",
    cooldownSessions: 20,
    isRelevant: ()  = > {
      let A  =  VA();
      return Boolean((dA.terminal === "Apple_Terminal" ? A.optionAsMetaKeyInstalled : A
        .shiftEnterKeyBindingInstalled) && A.numStartups > 3)
    }
  }, {