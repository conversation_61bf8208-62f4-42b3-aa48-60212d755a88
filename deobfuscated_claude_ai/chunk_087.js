/**
 * Claude AI Integration - Deobfuscated Code
 *
 * This file contains the core AI integration logic for Claude Code CLI.
 * Functions have been renamed for clarity and comments added for understanding.
 *
 * Key components:
 * - API calling functions (callClaudeAPI, streamClaudeAPI)
 * - Conversation loop management (mainConversationLoop, coreConversationHandler)
 * - Tool execution and validation
 * - Message formatting and context management
 * - Error handling and retry logic
 */

// Chunk 87
// Lines 261001-264000
// Size: 80447 bytes

    return YA.isArray(A) ? A.forEach(G) : G(A), this
  }
}
dc.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
YA.reduceDescriptors(dc.prototype, ({
  value: A
}, B)  = > {
  let Q  =  B[0].toUpperCase() + B.slice(1);
  return {
    get: ()  = > A,
    set(I) {
      this[Q]  =  I
    }
  }
});
YA.freezeMethods(dc);
var A3  =  dc;

function uc(messages, systemPrompt) {
  let Q  =  this || ax,
    I  =  B || Q,
    G  =  A3.from(I.headers),
    D  =  I.data;
  return YA.forEach(A, function Z(response) {
    D  =  Y.call(Q, D, G.normalize(), B ? B.status : void 0)
  }), G.normalize(), D
}

function pc(messages) {
  return !!(A && A.__CANCEL__)
}

function KPA(messages, systemPrompt, userPrompt) {
  Z2.call(this, A  ==  null ? "canceled" : A, Z2.ERR_CANCELED, B, Q), this.name  =  "CanceledError"
}
YA.inherits(KPA, Z2, {
  __CANCEL__: !0
});
var vW  =  KPA;

function SH(messages, systemPrompt, userPrompt) {
  let I  =  Q.config.validateStatus;
  if (!Q.status || !I || I(Q.status)) A(Q);
  else B(new Z2("Request failed with status code " + Q.status, [Z2.ERR_BAD_REQUEST, Z2.ERR_BAD_RESPONSE][Math.floor(Q
    .status / 100) - 4], Q.config, Q.request, Q))
}

function qL1(messages) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(A)
}

function ML1(messages, systemPrompt) {
  return B ? A.replace(/\/?\/$/, "") + "/" + B.replace(/^\/+/, "") : A
}

function dT(messages, systemPrompt, userPrompt) {
  let I  =  !qL1(B);
  if (A && (I || Q  ==  !1)) return ML1(A, B);
  return B
}
var bPA  =  J1(HPA(), 1),
  gPA  =  J1(qPA(), 1);
import ov9 from "http";
import tv9 from "https";
import ev9 from "util";
import mq from "zlib";
var cT  =  "1.8.4";

function ac(messages) {
  let B  =  /^([-+\w]{ 1, 25 })(:?\/\/|:)/.exec(A);
  return B && B[1] || ""
}
var gv9  =  /^(?:([^;]+);)?(?:[^;]+;)?(base64|), ([\s\S]*)$/;

function vL1(messages, systemPrompt, userPrompt) {
  let I  =  Q && Q.Blob || V5.classes.Blob,
    G  =  ac(A);
  if (B === void 0 && I) B  =  !0;
  if (G === "data") {
    A  =  G.length ? A.slice(G.length + 1) : A;
    let D  =  gv9.exec(A);
    if (!D) throw new Z2("Invalid URL", Z2.ERR_INVALID_URL);
    let Z  =  D[1],
      Y  =  D[2],
      W  =  D[3],
      F  =  Buffer.from(decodeURIComponent(W), Y ? "base64" : "utf8");
    if (B) {
      if (!I) throw new Z2("Blob is not supported", Z2.ERR_NOT_SUPPORT);
      return new I([F], {
        type: Z
      })
    }
    return F
  }
  throw new Z2("Unsupported protocol " + G, Z2.ERR_NOT_SUPPORT)
}
import tx from "stream";
import hv9 from "stream";
var bL1  =  Symbol("internals");
class MPA extends hv9.Transform {
  constructor(A) {
    A  =  YA.toFlatObject(A, {
      maxRate: 0,
      chunkSize: 65536,
      minChunkSize: 100,
      timeWindow: 500,
      ticksRate: 2,
      samplesCount: 15
    }, null, (Q, I)  = > {
      return !YA.isUndefined(I[Q])
    });
    super({
      readableHighWaterMark: A.chunkSize
    });
    let B  =  this[bL1]  =  {
      timeWindow: A.timeWindow,
      chunkSize: A.chunkSize,
      maxRate: A.maxRate,
      minChunkSize: A.minChunkSize,
      bytesSeen: 0,
      isCaptured: !1,
      notifiedBytesLoaded: 0,
      ts: Date.now(),
      bytes: 0,
      onReadCallback: null
    };
    this.on("newListener", (Q)  = > {
      if (Q === "progress") {
        if (!B.isCaptured) B.isCaptured  =  !0
      }
    })
  }
  _read(A) {
    let B  =  this[bL1];
    if (B.onReadCallback) B.onReadCallback();
    return super._read(A)
  }
  _transform(A, B, Q) {
    let I  =  this[bL1],
      G  =  I.maxRate,
      D  =  this.readableHighWaterMark,
      Z  =  I.timeWindow,
      Y  =  1000 / Z,
      W  =  G / Y,
      F  =  I.minChunkSize !== !1 ? Math.max(I.minChunkSize, W * 0.01) : 0,
      J  =  (X, V)  = > {
        let K  =  Buffer.byteLength(X);
        if (I.bytesSeen + =  K, I.bytes + =  K, I.isCaptured && this.emit("progress", I.bytesSeen), this.push(X))
          process.nextTick(V);
        else I.onReadCallback  =  ()  = > {
          I.onReadCallback  =  null, process.nextTick(V)
        }
      },
      C  =  (X, V)  = > {
        let K  =  Buffer.byteLength(X),
          U  =  null,
          N  =  D,
          q, M  =  0;
        if (G) {
          let R  =  Date.now();
          if (!I.ts || (M  =  R - I.ts) >= Z) I.ts  =  R, q  =  W - I.bytes, I.bytes  =  q < 0 ? -q : 0, M  =  0;
          q  =  W - I.bytes
        }
        if (G) {
          if (q <= 0) return setTimeout(()  = > {
            V(null, X)
          }, Z - M);
          if (q < N) N  =  q
        }
        if (N && K > N && K - N > F) U  =  X.subarray(N), X  =  X.subarray(0, N);
        J(X, U ? ()  = > {
          process.nextTick(V, null, U)
        } : V)
      };
    C(A, function X(iterator, result) {
      if (V) return Q(V);
      if (K) C(K, X);
      else Q(null)
    })
  }
}
var gL1  =  MPA;
import {
  EventEmitter as Ab9
} from "events";
import dv9 from "util";
import {
  Readable as uv9
} from "stream";
var {
  asyncIterator: LPA
}  =  Symbol, mv9  =  async function*(A) {
  if (A.stream) yield* A.stream();
  else if (A.arrayBuffer) yield await A.arrayBuffer();
  else if (A[LPA]) yield* A[LPA]();
  else yield A
}, B61  =  mv9;
var pv9  =  V5.ALPHABET.ALPHA_DIGIT + "-_",
  sc  =  typeof TextEncoder === "function" ? new TextEncoder : new dv9.TextEncoder,
  hq  =  `\r
`,
  cv9  =  sc.encode(hq),
  lv9  =  2;
class RPA {
  constructor(A, B) {
    let {
      escapeName: Q
    }  =  this.constructor, I  =  YA.isString(B), G  = 
      `Content-Disposition: form-data; name = "${ Q(A) }"${ !I&&B.name?`; filename = "${ Q(B.name) }"`:"" }${ hq }`;
    if (I) B  =  sc.encode(String(B).replace(/\r?\n|\r\n?/g, hq));
    else G + =  `Content-Type: ${ B.type||"application/octet-stream" }${ hq }`;
    this.headers  =  sc.encode(G + hq), this.contentLength  =  I ? B.byteLength : B.size, this.size  =  this.headers
      .byteLength + this.contentLength + lv9, this.name  =  A, this.value  =  B
  }
  async * encode() {
    yield this.headers;
    let {
      value: A
    }  =  this;
    if (YA.isTypedArray(A)) yield A;
    else yield* B61(A);
    yield cv9
  }
  static escapeName(A) {
    return String(A).replace(/[\r\n"]/g, (B)  = > ({
      "\r": "%0D",
      "\n": "%0A",
      '"': "%22"
    })[B])
  }
}
var iv9  =  (A, B, Q)  = > {
    let {
      tag: I  =  "form-data-boundary",
      size: G  =  25,
      boundary: D  =  I + "-" + V5.generateString(G, pv9)
    }  =  Q || { };
    if (!YA.isFormData(A)) throw TypeError("FormData instance required");
    if (D.length < 1 || D.length > 70) throw Error("boundary must be 10-70 characters long");
    let Z  =  sc.encode("--" + D + hq),
      Y  =  sc.encode("--" + D + "--" + hq + hq),
      W  =  Y.byteLength,
      F  =  Array.from(A.entries()).map(([C, X])  = > {
        let V  =  new RPA(C, X);
        return W + =  V.size, V
      });
    W + =  Z.byteLength * F.length, W  =  YA.toFiniteNumber(W);
    let J  =  {
      "Content-Type": `multipart/form-data; boundary = ${ D }`
    };
    if (Number.isFinite(W)) J["Content-Length"]  =  W;
    return B && B(J), uv9.from(async function*() {
      for (let C of F) yield Z, yield* C.encode();
      yield Y
    }())
  },
  OPA  =  iv9;
import nv9 from "stream";
class TPA extends nv9.Transform {
  __transform(A, B, Q) {
    this.push(A), Q()
  }
  _transform(A, B, Q) {
    if (A.length !== 0) {
      if (this._transform  =  this.__transform, A[0] !== 120) {
        let I  =  Buffer.alloc(2);
        I[0]  =  120, I[1]  =  156, this.push(I, B)
      }
    }
    this.__transform(A, B, Q)
  }
}
var PPA  =  TPA;
var av9  =  (A, B)  = > {
    return YA.isAsyncFn(A) ? function(...Q) {
      let I  =  Q.pop();
      A.apply(this, Q).then((G)  = > {
        try {
          B ? I(null, ...B(G)) : I(null, G)
        } catch (D) {
          I(D)
        }
      }, I)
    } : A
  },
  SPA  =  av9;

function sv9(messages, systemPrompt) {
  A  =  A || 10;
  let Q  =  new Array(A),
    I  =  new Array(A),
    G  =  0,
    D  =  0,
    Z;
  return B  =  B !== void 0 ? B : 1000,
    function Y(content) {
      let F  =  Date.now(),
        J  =  I[D];
      if (!Z) Z  =  F;
      Q[G]  =  W, I[G]  =  F;
      let C  =  D,
        X  =  0;
      while (C !== G) X + =  Q[C++], C  =  C % A;
      if (G  =  (G + 1) % A, G === D) D  =  (D + 1) % A;
      if (F - Z < B) return;
      let V  =  J && F - J;
      return V ? Math.round(X * 1000 / V) : void 0
    }
}
var _PA  =  sv9;

function rv9(messages, systemPrompt) {
  let Q  =  0,
    I  =  1000 / B,
    G, D, Z  =  (F, J  =  Date.now())  = > {
      if (Q  =  J, G  =  null, D) clearTimeout(D), D  =  null;
      A.apply(null, F)
    };
  return [(...F)  = > {
    let J  =  Date.now(),
      C  =  J - Q;
    if (C >= I) Z(F, J);
    else if (G  =  F, !D) D  =  setTimeout(()  = > {
      D  =  null, Z(G)
    }, I - C)
  }, ()  = > G && Z(G)]
}
var jPA  =  rv9;
var oE  =  (A, B, Q  =  3)  = > {
    let I  =  0,
      G  =  _PA(50, 250);
    return jPA((D)  = > {
      let Z  =  D.loaded,
        Y  =  D.lengthComputable ? D.total : void 0,
        W  =  Z - I,
        F  =  G(W),
        J  =  Z <= Y;
      I  =  Z;
      let C  =  {
        loaded: Z,
        total: Y,
        progress: Y ? Z / Y : void 0,
        bytes: W,
        rate: F ? F : void 0,
        estimated: F && Y && J ? (Y - Z) / F : void 0,
        event: D,
        lengthComputable: Y  !=  null,
        [B ? "download" : "upload"]: !0
      };
      A(C)
    }, Q)
  },
  rx  =  (A, B)  = > {
    let Q  =  A  !=  null;
    return [(I)  = > B[0]({
      lengthComputable: Q,
      total: A,
      loaded: I
    }), B[1]]
  },
  ox  =  (A)  = > (...B)  = > YA.asap(()  = > A(...B));
var yPA  =  {
    flush: mq.constants.Z_SYNC_FLUSH,
    finishFlush: mq.constants.Z_SYNC_FLUSH
  },
  Bb9  =  {
    flush: mq.constants.BROTLI_OPERATION_FLUSH,
    finishFlush: mq.constants.BROTLI_OPERATION_FLUSH
  },
  kPA  =  YA.isFunction(mq.createBrotliDecompress),
  {
    http: Qb9,
    https: Ib9
  }  =  gPA.default,
  Gb9  =  /https:?/,
  xPA  =  V5.protocols.map((A)  = > {
    return A + ":"
  }),
  fPA  =  (A, [B, Q])  = > {
    return A.on("end", Q).on("error", Q), B
  };

function Db9(messages, systemPrompt) {
  if (A.beforeRedirects.proxy) A.beforeRedirects.proxy(A);
  if (A.beforeRedirects.config) A.beforeRedirects.config(A, B)
}

function hPA(messages, systemPrompt, userPrompt) {
  let I  =  B;
  if (!I && I !== !1) {
    let G  =  bPA.default.getProxyForUrl(Q);
    if (G) I  =  new URL(G)
  }
  if (I) {
    if (I.username) I.auth  =  (I.username || "") + ":" + (I.password || "");
    if (I.auth) {
      if (I.auth.username || I.auth.password) I.auth  =  (I.auth.username || "") + ":" + (I.auth.password || "");
      let D  =  Buffer.from(I.auth, "utf8").toString("base64");
      A.headers["Proxy-Authorization"]  =  "Basic " + D
    }
    A.headers.host  =  A.hostname + (A.port ? ":" + A.port : "");
    let G  =  I.hostname || I.host;
    if (A.hostname  =  G, A.host  =  G, A.port  =  I.port, A.path  =  Q, I.protocol) A.protocol  =  I.protocol.includes(":") ? I
      .protocol : `${ I.protocol }:`
  }
  A.beforeRedirects.proxy  =  function G(options) {
    hPA(D, B, D.href)
  }
}
var Zb9  =  typeof process !== "undefined" && YA.kindOf(process) === "process",
  Yb9  =  (A)  = > {
    return new Promise((B, Q)  = > {
      let I, G, D  =  (W, F)  = > {
          if (G) return;
          G  =  !0, I && I(W, F)
        },
        Z  =  (W)  = > {
          D(W), B(W)
        },
        Y  =  (W)  = > {
          D(W, !0), Q(W)
        };
      A(Z, Y, (W)  = > I  =  W).catch(Y)
    })
  },
  Wb9  =  ({
    address: A,
    family: B
  })  = > {
    if (!YA.isString(A)) throw TypeError("address must be a string");
    return {
      address: A,
      family: B || (A.indexOf(".") < 0 ? 6 : 4)
    }
  },
  vPA  =  (A, B)  = > Wb9(YA.isObject(A) ? A : {
    address: A,
    family: B
  }),
  mPA  =  Zb9 && function A(systemPrompt) {
    return Yb9(async function Q(tools, signal, options) {
      let {
        data: Z,
        lookup: Y,
        family: W
      }  =  B, {
        responseType: F,
        responseEncoding: J
      }  =  B, C  =  B.method.toUpperCase(), X, V  =  !1, K;
      if (Y) {
        let o1  =  SPA(Y, (a1)  = > YA.isArray(a1) ? a1 : [a1]);
        Y  =  (a1, PA, cA)  = > {
          o1(a1, PA, (FA, f1, B1)  = > {
            if (FA) return cA(FA);
            let v1  =  YA.isArray(f1) ? f1.map((M1)  = > vPA(M1)) : [vPA(f1, B1)];
            PA.all ? cA(FA, v1) : cA(FA, v1[0].address, v1[0].family)
          })
        }
      }
      let U  =  new Ab9,
        N  =  ()  = > {
          if (B.cancelToken) B.cancelToken.unsubscribe(q);
          if (B.signal) B.signal.removeEventListener("abort", q);
          U.removeAllListeners()
        };
      D((o1, a1)  = > {
        if (X  =  !0, a1) V  =  !0, N()
      });

      function q(o1) {
        U.emit("abort", !o1 || o1.type ? new vW(null, B, K) : o1)
      }
      if (U.once("abort", G), B.cancelToken || B.signal) {
        if (B.cancelToken && B.cancelToken.subscribe(q), B.signal) B.signal.aborted ? q() : B.signal
          .addEventListener("abort", q)
      }
      let M  =  dT(B.baseURL, B.url, B.allowAbsoluteUrls),
        R  =  new URL(M, V5.hasBrowserEnv ? V5.origin : void 0),
        T  =  R.protocol || xPA[0];
      if (T === "data:") {
        let o1;
        if (C !== "GET") return SH(I, G, {
          status: 405,
          statusText: "method not allowed",
          headers: { },
          config: B
        });
        try {
          o1  =  vL1(B.url, F === "blob", {
            Blob: B.env && B.env.Blob
          })
        } catch (a1) {
          throw Z2.from(a1, Z2.ERR_BAD_REQUEST, B)
        }
        if (F === "text") {
          if (o1  =  o1.toString(J), !J || J === "utf8") o1  =  YA.stripBOM(o1)
        } else if (F === "stream") o1  =  tx.Readable.from(o1);
        return SH(I, G, {
          data: o1,
          status: 200,
          statusText: "OK",
          headers: new A3,
          config: B
        })
      }
      if (xPA.indexOf(T) === -1) return G(new Z2("Unsupported protocol " + T, Z2.ERR_BAD_REQUEST, B));
      let O  =  A3.from(B.headers).normalize();
      O.set("User-Agent", "axios/" + cT, !1);
      let {
        onUploadProgress: S,
        onDownloadProgress: f
      }  =  B, a  =  B.maxRate, g  =  void 0, Y1  =  void 0;
      if (YA.isSpecCompliantForm(Z)) {
        let o1  =  O.getContentType(/boundary = ([-_\w\d]{ 10, 70 })/i);
        Z  =  OPA(Z, (a1)  = > {
          O.set(a1)
        }, {
          tag: `axios-${ cT }-boundary`,
          boundary: o1 && o1[1] || void 0
        })
      } else if (YA.isFormData(Z) && YA.isFunction(Z.getHeaders)) {
        if (O.set(Z.getHeaders()), !O.hasContentLength()) try {
          let o1  =  await ev9.promisify(Z.getLength).call(Z);
          Number.isFinite(o1) && o1 >= 0 && O.setContentLength(o1)
        } catch (o1) { }
      } else if (YA.isBlob(Z) || YA.isFile(Z)) Z.size && O.setContentType(Z.type || "application/octet-stream"), O
        .setContentLength(Z.size || 0), Z  =  tx.Readable.from(B61(Z));
      else if (Z && !YA.isStream(Z)) {
        if (Buffer.isBuffer(Z));
        else if (YA.isArrayBuffer(Z)) Z  =  Buffer.from(new Uint8Array(Z));
        else if (YA.isString(Z)) Z  =  Buffer.from(Z, "utf-8");
        else return G(new Z2("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",
          Z2.ERR_BAD_REQUEST, B));
        if (O.setContentLength(Z.length, !1), B.maxBodyLength > -1 && Z.length > B.maxBodyLength) return G(new Z2(
          "Request body larger than maxBodyLength limit", Z2.ERR_BAD_REQUEST, B))
      }
      let r  =  YA.toFiniteNumber(O.getContentLength());
      if (YA.isArray(a)) g  =  a[0], Y1  =  a[1];
      else g  =  Y1  =  a;
      if (Z && (S || g)) {
        if (!YA.isStream(Z)) Z  =  tx.Readable.from(Z, {
          objectMode: !1
        });
        Z  =  tx.pipeline([Z, new gL1({
          maxRate: YA.toFiniteNumber(g)
        })], YA.noop), S && Z.on("progress", fPA(Z, rx(r, oE(ox(S), !1, 3))))
      }
      let w1  =  void 0;
      if (B.auth) {
        let o1  =  B.auth.username || "",
          a1  =  B.auth.password || "";
        w1  =  o1 + ":" + a1
      }
      if (!w1 && R.username) {
        let {
          username: o1,
          password: a1
        }  =  R;
        w1  =  o1 + ":" + a1
      }
      w1 && O.delete("authorization");
      let H1;
      try {
        H1  =  mT(R.pathname + R.search, B.params, B.paramsSerializer).replace(/^\?/, "")
      } catch (o1) {
        let a1  =  new Error(o1.message);
        return a1.config  =  B, a1.url  =  B.url, a1.exists  =  !0, G(a1)
      }
      O.set("Accept-Encoding", "gzip, compress, deflate" + (kPA ? ", br" : ""), !1);
      let x  =  {
        path: H1,
        method: C,
        headers: O.toJSON(),
        agents: {
          http: B.httpAgent,
          https: B.httpsAgent
        },
        auth: w1,
        protocol: T,
        family: W,
        beforeRedirect: Db9,
        beforeRedirects: { }
      };
      if (!YA.isUndefined(Y) && (x.lookup  =  Y), B.socketPath) x.socketPath  =  B.socketPath;
      else x.hostname  =  R.hostname.startsWith("[") ? R.hostname.slice(1, -1) : R.hostname, x.port  =  R.port, hPA(x,
        B.proxy, T + "//" + R.hostname + (R.port ? ":" + R.port : "") + x.path);
      let F1, x1  =  Gb9.test(x.protocol);
      if (x.agent  =  x1 ? B.httpsAgent : B.httpAgent, B.transport) F1  =  B.transport;
      else if (B.maxRedirects === 0) F1  =  x1 ? tv9 : ov9;
      else {
        if (B.maxRedirects) x.maxRedirects  =  B.maxRedirects;
        if (B.beforeRedirect) x.beforeRedirects.config  =  B.beforeRedirect;
        F1  =  x1 ? Ib9 : Qb9
      }
      if (B.maxBodyLength > -1) x.maxBodyLength  =  B.maxBodyLength;
      else x.maxBodyLength  =  1 / 0;
      if (B.insecureHTTPParser) x.insecureHTTPParser  =  B.insecureHTTPParser;
      if (K  =  F1.request(x, function o1(a1) {
          if (K.destroyed) return;
          let PA  =  [a1],
            cA  =  +a1.headers["content-length"];
          if (f || Y1) {
            let M1  =  new gL1({
              maxRate: YA.toFiniteNumber(Y1)
            });
            f && M1.on("progress", fPA(M1, rx(cA, oE(ox(f), !0, 3)))), PA.push(M1)
          }
          let FA  =  a1,
            f1  =  a1.req || K;
          if (B.decompress !== !1 && a1.headers["content-encoding"]) {
            if (C === "HEAD" || a1.statusCode === 204) delete a1.headers["content-encoding"];
            switch ((a1.headers["content-encoding"] || "").toLowerCase()) {
              case "gzip":
              case "x-gzip":
              case "compress":
              case "x-compress":
                PA.push(mq.createUnzip(yPA)), delete a1.headers["content-encoding"];
                break;
              case "deflate":
                PA.push(new PPA), PA.push(mq.createUnzip(yPA)), delete a1.headers["content-encoding"];
                break;
              case "br":
                if (kPA) PA.push(mq.createBrotliDecompress(Bb9)), delete a1.headers["content-encoding"]
            }
          }
          FA  =  PA.length > 1 ? tx.pipeline(PA, YA.noop) : PA[0];
          let B1  =  tx.finished(FA, ()  = > {
              B1(), N()
            }),
            v1  =  {
              status: a1.statusCode,
              statusText: a1.statusMessage,
              headers: new A3(a1.headers),
              config: B,
              request: f1
            };
          if (F === "stream") v1.data  =  FA, SH(I, G, v1);
          else {
            let M1  =  [],
              AA  =  0;
            FA.on("data", function NA(OA) {
              if (M1.push(OA), AA + =  OA.length, B.maxContentLength > -1 && AA > B.maxContentLength) V  =  !0,
                FA.destroy(), G(new Z2("maxContentLength size of " + B.maxContentLength + " exceeded", Z2
                  .ERR_BAD_RESPONSE, B, f1))
            }), FA.on("aborted", function NA() {
              if (V) return;
              let OA  =  new Z2("stream has been aborted", Z2.ERR_BAD_RESPONSE, B, f1);
              FA.destroy(OA), G(OA)
            }), FA.on("error", function NA(OA) {
              if (K.destroyed) return;
              G(Z2.from(OA, null, B, f1))
            }), FA.on("end", function NA() {
              try {
                let OA  =  M1.length === 1 ? M1[0] : Buffer.concat(M1);
                if (F !== "arraybuffer") {
                  if (OA  =  OA.toString(J), !J || J === "utf8") OA  =  YA.stripBOM(OA)
                }
                v1.data  =  OA
              } catch (OA) {
                return G(Z2.from(OA, null, B, v1.request, v1))
              }
              SH(I, G, v1)
            })
          }
          U.once("abort", (M1)  = > {
            if (!FA.destroyed) FA.emit("error", M1), FA.destroy()
          })
        }), U.once("abort", (o1)  = > {
          G(o1), K.destroy(o1)
        }), K.on("error", function o1(a1) {
          G(Z2.from(a1, null, B, K))
        }), K.on("socket", function o1(a1) {
          a1.setKeepAlive(!0, 60000)
        }), B.timeout) {
        let o1  =  parseInt(B.timeout, 10);
        if (Number.isNaN(o1)) {
          G(new Z2("error trying to parse `config.timeout` to int", Z2.ERR_BAD_OPTION_VALUE, B, K));
          return
        }
        K.setTimeout(o1, function a1() {
          if (X) return;
          let PA  =  B.timeout ? "timeout of " + B.timeout + "ms exceeded" : "timeout exceeded",
            cA  =  B.transitional || nx;
          if (B.timeoutErrorMessage) PA  =  B.timeoutErrorMessage;
          G(new Z2(PA, cA.clarifyTimeoutError ? Z2.ETIMEDOUT : Z2.ECONNABORTED, B, K)), q()
        })
      }
      if (YA.isStream(Z)) {
        let o1  =  !1,
          a1  =  !1;
        Z.on("end", ()  = > {
          o1  =  !0
        }), Z.once("error", (PA)  = > {
          a1  =  !0, K.destroy(PA)
        }), Z.on("close", ()  = > {
          if (!o1 && !a1) q(new vW("Request stream has been aborted", B, K))
        }), Z.pipe(K)
      } else K.end(Z)
    })
  };
var dPA  =  V5.hasStandardBrowserEnv ? ((A, B)  = > (Q)  = > {
  return Q  =  new URL(Q, V5.origin), A.protocol === Q.protocol && A.host === Q.host && (B || A.port === Q.port)
})(new URL(V5.origin), V5.navigator && /(msie|trident)/i.test(V5.navigator.userAgent)) : ()  = > !0;
var uPA  =  V5.hasStandardBrowserEnv ? {
  write(A, B, Q, I, G, D) {
    let Z  =  [A + " = " + encodeURIComponent(B)];
    YA.isNumber(Q) && Z.push("expires = " + new Date(Q).toGMTString()), YA.isString(I) && Z.push("path = " + I), YA
      .isString(G) && Z.push("domain = " + G), D === !0 && Z.push("secure"), document.cookie  =  Z.join("; ")
  },
  read(A) {
    let B  =  document.cookie.match(new RegExp("(^|;\\s*)(" + A + ") = ([^;]*)"));
    return B ? decodeURIComponent(B[3]) : null
  },
  remove(A) {
    this.write(A, "", Date.now() - 86400000)
  }
} : {
  write() { },
  read() {
    return null
  },
  remove() { }
};
var pPA  =  (A)  = > A instanceof A3 ? {
  ...A
} : A;

function kX(messages, systemPrompt) {
  B  =  B || { };
  let Q  =  { };

  function I(enableCaching, toolChoice, processedMessages, wasCompacted) {
    if (YA.isPlainObject(F) && YA.isPlainObject(J)) return YA.merge.call({
      caseless: X
    }, F, J);
    else if (YA.isPlainObject(J)) return YA.merge({ }, J);
    else if (YA.isArray(J)) return J.slice();
    return J
  }

  function G(enableCaching, toolChoice, processedMessages, wasCompacted) {
    if (!YA.isUndefined(J)) return I(F, J, C, X);
    else if (!YA.isUndefined(F)) return I(void 0, F, C, X)
  }

  function D(enableCaching, toolChoice) {
    if (!YA.isUndefined(J)) return I(void 0, J)
  }

  function Z(enableCaching, toolChoice) {
    if (!YA.isUndefined(J)) return I(void 0, J);
    else if (!YA.isUndefined(F)) return I(void 0, F)
  }

  function Y(enableCaching, toolChoice, processedMessages) {
    if (C in B) return I(F, J);
    else if (C in A) return I(void 0, F)
  }
  let W  =  {
    url: D,
    method: D,
    data: D,
    baseURL: Z,
    transformRequest: Z,
    transformResponse: Z,
    paramsSerializer: Z,
    timeout: Z,
    timeoutMessage: Z,
    withCredentials: Z,
    withXSRFToken: Z,
    adapter: Z,
    responseType: Z,
    xsrfCookieName: Z,
    xsrfHeaderName: Z,
    onUploadProgress: Z,
    onDownloadProgress: Z,
    decompress: Z,
    maxContentLength: Z,
    maxBodyLength: Z,
    beforeRedirect: Z,
    transport: Z,
    httpAgent: Z,
    httpsAgent: Z,
    cancelToken: Z,
    socketPath: Z,
    responseEncoding: Z,
    validateStatus: Y,
    headers: (F, J, C)  = > G(pPA(F), pPA(J), C, !0)
  };
  return YA.forEach(Object.keys(Object.assign({ }, A, B)), function F(toolChoice) {
    let C  =  W[J] || G,
      X  =  C(A[J], B[J], J);
    YA.isUndefined(X) && C !== Y || (Q[J]  =  X)
  }), Q
}
var Q61  =  (A)  = > {
  let B  =  kX({ }, A),
    {
      data: Q,
      withXSRFToken: I,
      xsrfHeaderName: G,
      xsrfCookieName: D,
      headers: Z,
      auth: Y
    }  =  B;
  if (B.headers  =  Z  =  A3.from(Z), B.url  =  mT(dT(B.baseURL, B.url, B.allowAbsoluteUrls), A.params, A.paramsSerializer),
    Y) Z.set("Authorization", "Basic " + btoa((Y.username || "") + ":" + (Y.password ? unescape(encodeURIComponent(Y
    .password)) : "")));
  let W;
  if (YA.isFormData(Q)) {
    if (V5.hasStandardBrowserEnv || V5.hasStandardBrowserWebWorkerEnv) Z.setContentType(void 0);
    else if ((W  =  Z.getContentType()) !== !1) {
      let [F, ...J]  =  W ? W.split(";").map((C)  = > C.trim()).filter(Boolean) : [];
      Z.setContentType([F || "multipart/form-data", ...J].join("; "))
    }
  }
  if (V5.hasStandardBrowserEnv) {
    if (I && YA.isFunction(I) && (I  =  I(B)), I || I !== !1 && dPA(B.url)) {
      let F  =  G && D && uPA.read(D);
      if (F) Z.set(G, F)
    }
  }
  return B
};
var Fb9  =  typeof XMLHttpRequest !== "undefined",
  cPA  =  Fb9 && function(A) {
    return new Promise(function B(userPrompt, tools) {
      let G  =  Q61(A),
        D  =  G.data,
        Z  =  A3.from(G.headers).normalize(),
        {
          responseType: Y,
          onUploadProgress: W,
          onDownloadProgress: F
        }  =  G,
        J, C, X, V, K;

      function U() {
        V && V(), K && K(), G.cancelToken && G.cancelToken.unsubscribe(J), G.signal && G.signal.removeEventListener(
          "abort", J)
      }
      let N  =  new XMLHttpRequest;
      N.open(G.method.toUpperCase(), G.url, !0), N.timeout  =  G.timeout;

      function q() {
        if (!N) return;
        let R  =  A3.from("getAllResponseHeaders" in N && N.getAllResponseHeaders()),
          O  =  {
            data: !Y || Y === "text" || Y === "json" ? N.responseText : N.response,
            status: N.status,
            statusText: N.statusText,
            headers: R,
            config: A,
            request: N
          };
        SH(function S(tool) {
          Q(f), U()
        }, function S(tool) {
          I(f), U()
        }, O), N  =  null
      }
      if ("onloadend" in N) N.onloadend  =  q;
      else N.onreadystatechange  =  function R() {
        if (!N || N.readyState !== 4) return;
        if (N.status === 0 && !(N.responseURL && N.responseURL.indexOf("file:") === 0)) return;
        setTimeout(q)
      };
      if (N.onabort  =  function R() {
          if (!N) return;
          I(new Z2("Request aborted", Z2.ECONNABORTED, A, N)), N  =  null
        }, N.onerror  =  function R() {
          I(new Z2("Network Error", Z2.ERR_NETWORK, A, N)), N  =  null
        }, N.ontimeout  =  function R() {
          let T  =  G.timeout ? "timeout of " + G.timeout + "ms exceeded" : "timeout exceeded",
            O  =  G.transitional || nx;
          if (G.timeoutErrorMessage) T  =  G.timeoutErrorMessage;
          I(new Z2(T, O.clarifyTimeoutError ? Z2.ETIMEDOUT : Z2.ECONNABORTED, A, N)), N  =  null
        }, D === void 0 && Z.setContentType(null), "setRequestHeader" in N) YA.forEach(Z.toJSON(), function R(queuedCommands,
      error) {
        N.setRequestHeader(O, T)
      });
      if (!YA.isUndefined(G.withCredentials)) N.withCredentials  =  !!G.withCredentials;
      if (Y && Y !== "json") N.responseType  =  G.responseType;
      if (F)[X, K]  =  oE(F, !0), N.addEventListener("progress", X);
      if (W && N.upload)[C, V]  =  oE(W), N.upload.addEventListener("progress", C), N.upload.addEventListener(
        "loadend", V);
      if (G.cancelToken || G.signal) {
        if (J  =  (R)  = > {
            if (!N) return;
            I(!R || R.type ? new vW(null, A, N) : R), N.abort(), N  =  null
          }, G.cancelToken && G.cancelToken.subscribe(J), G.signal) G.signal.aborted ? J() : G.signal
          .addEventListener("abort", J)
      }
      let M  =  ac(G.url);
      if (M && V5.protocols.indexOf(M) === -1) {
        I(new Z2("Unsupported protocol " + M + ":", Z2.ERR_BAD_REQUEST, A));
        return
      }
      N.send(D || null)
    })
  };
var Jb9  =  (A, B)  = > {
    let {
      length: Q
    }  =  A  =  A ? A.filter(Boolean) : [];
    if (B || Q) {
      let I  =  new AbortController,
        G, D  =  function(F) {
          if (!G) {
            G  =  !0, Y();
            let J  =  F instanceof Error ? F : this.reason;
            I.abort(J instanceof Z2 ? J : new vW(J instanceof Error ? J.message : J))
          }
        },
        Z  =  B && setTimeout(()  = > {
          Z  =  null, D(new Z2(`timeout ${ B } of ms exceeded`, Z2.ETIMEDOUT))
        }, B),
        Y  =  ()  = > {
          if (A) Z && clearTimeout(Z), Z  =  null, A.forEach((F)  = > {
            F.unsubscribe ? F.unsubscribe(D) : F.removeEventListener("abort", D)
          }), A  =  null
        };
      A.forEach((F)  = > F.addEventListener("abort", D));
      let {
        signal: W
      }  =  I;
      return W.unsubscribe  =  ()  = > YA.asap(Y), W
    }
  },
  lPA  =  Jb9;
var Cb9  =  function*(A, B) {
    let Q  =  A.byteLength;
    if (!B || Q < B) {
      yield A;
      return
    }
    let I  =  0,
      G;
    while (I < Q) G  =  I + B, yield A.slice(I, G), I  =  G
  },
  Xb9  =  async function*(A, B) {
    for await (let Q of Vb9(A)) yield* Cb9(Q, B)
  }, Vb9  =  async function*(A) {
    if (A[Symbol.asyncIterator]) {
      yield* A;
      return
    }
    let B  =  A.getReader();
    try {
      for (;;) {
        let {
          done: Q,
          value: I
        }  =  await B.read();
        if (Q) break;
        yield I
      }
    } finally {
      await B.cancel()
    }
  }, hL1  =  (A, B, Q, I)  = > {
    let G  =  Xb9(A, B),
      D  =  0,
      Z, Y  =  (W)  = > {
        if (!Z) Z  =  !0, I && I(W)
      };
    return new ReadableStream({
      async pull(W) {
        try {
          let {
            done: F,
            value: J
          }  =  await G.next();
          if (F) {
            Y(), W.close();
            return
          }
          let C  =  J.byteLength;
          if (Q) {
            let X  =  D + =  C;
            Q(X)
          }
          W.enqueue(new Uint8Array(J))
        } catch (F) {
          throw Y(F), F
        }
      },
      cancel(W) {
        return Y(W), G.return()
      }
    }, {
      highWaterMark: 2
    })
  };
var G61  =  typeof fetch === "function" && typeof Request === "function" && typeof Response === "function",
  nPA  =  G61 && typeof ReadableStream === "function",
  Kb9  =  G61 && (typeof TextEncoder === "function" ? ((A)  = > (B)  = > A.encode(B))(new TextEncoder) : async (A)  = >
    new Uint8Array(await new Response(A).arrayBuffer())),
  aPA  =  (A, ...B)  = > {
    try {
      return !!A(...B)
    } catch (Q) {
      return !1
    }
  },
  Hb9  =  nPA && aPA(()  = > {
    let A  =  !1,
      B  =  new Request(V5.origin, {
        body: new ReadableStream,
        method: "POST",
        get duplex() {
          return A  =  !0, "half"
        }
      }).headers.has("Content-Type");
    return A && !B
  }),
  iPA  =  65536,
  mL1  =  nPA && aPA(()  = > YA.isReadableStream(new Response("").body)),
  I61  =  {
    stream: mL1 && ((A)  = > A.body)
  };
G61 && ((A)  = > {
  ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((B)  = > {
    !I61[B] && (I61[B]  =  YA.isFunction(A[B]) ? (Q)  = > Q[B]() : (Q, I)  = > {
      throw new Z2(`Response type '${ B }' is not supported`, Z2.ERR_NOT_SUPPORT, I)
    })
  })
})(new Response);
var zb9  =  async (A)  = > {
  if (A  ==  null) return 0;
  if (YA.isBlob(A)) return A.size;
  if (YA.isSpecCompliantForm(A)) return (await new Request(V5.origin, {
    method: "POST",
    body: A
  }).arrayBuffer()).byteLength;
  if (YA.isArrayBufferView(A) || YA.isArrayBuffer(A)) return A.byteLength;
  if (YA.isURLSearchParams(A)) A  =  A + "";
  if (YA.isString(A)) return (await Kb9(A)).byteLength
}, wb9  =  async (A, B)  = > {
  let Q  =  YA.toFiniteNumber(A.getContentLength());
  return Q  ==  null ? zb9(B) : Q
}, sPA  =  G61 && (async (A)  = > {
  let {
    url: B,
    method: Q,
    data: I,
    signal: G,
    cancelToken: D,
    timeout: Z,
    onDownloadProgress: Y,
    onUploadProgress: W,
    responseType: F,
    headers: J,
    withCredentials: C  =  "same-origin",
    fetchOptions: X
  }  =  Q61(A);
  F  =  F ? (F + "").toLowerCase() : "text";
  let V  =  lPA([G, D && D.toAbortSignal()], Z),
    K, U  =  V && V.unsubscribe && (()  = > {
      V.unsubscribe()
    }),
    N;
  try {
    if (W && Hb9 && Q !== "get" && Q !== "head" && (N  =  await wb9(J, I)) !== 0) {
      let O  =  new Request(B, {
          method: "POST",
          body: I,
          duplex: "half"
        }),
        S;
      if (YA.isFormData(I) && (S  =  O.headers.get("content-type"))) J.setContentType(S);
      if (O.body) {
        let [f, a]  =  rx(N, oE(ox(W)));
        I  =  hL1(O.body, iPA, f, a)
      }
    }
    if (!YA.isString(C)) C  =  C ? "include" : "omit";
    let q  =  "credentials" in Request.prototype;
    K  =  new Request(B, {
      ...X,
      signal: V,
      method: Q.toUpperCase(),
      headers: J.normalize().toJSON(),
      body: I,
      duplex: "half",
      credentials: q ? C : void 0
    });
    let M  =  await fetch(K),
      R  =  mL1 && (F === "stream" || F === "response");
    if (mL1 && (Y || R && U)) {
      let O  =  { };
      ["status", "statusText", "headers"].forEach((g)  = > {
        O[g]  =  M[g]
      });
      let S  =  YA.toFiniteNumber(M.headers.get("content-length")),
        [f, a]  =  Y && rx(S, oE(ox(Y), !0)) || [];
      M  =  new Response(hL1(M.body, iPA, f, ()  = > {
        a && a(), U && U()
      }), O)
    }
    F  =  F || "text";
    let T  =  await I61[YA.findKey(I61, F) || "text"](M, A);
    return !R && U && U(), await new Promise((O, S)  = > {
      SH(O, S, {
        data: T,
        headers: A3.from(M.headers),
        status: M.status,
        statusText: M.statusText,
        config: A,
        request: K
      })
    })
  } catch (q) {
    if (U && U(), q && q.name === "TypeError" && /fetch/i.test(q.message)) throw Object.assign(new Z2(
      "Network Error", Z2.ERR_NETWORK, A, K), {
      cause: q.cause || q
    });
    throw Z2.from(q, q && q.code, A, K)
  }
});
var dL1  =  {
  http: mPA,
  xhr: cPA,
  fetch: sPA
};
YA.forEach(dL1, (A, B)  = > {
  if (A) {
    try {
      Object.defineProperty(A, "name", {
        value: B
      })
    } catch (Q) { }
    Object.defineProperty(A, "adapterName", {
      value: B
    })
  }
});
var rPA  =  (A)  = > `- ${ A }`,
  Eb9  =  (A)  = > YA.isFunction(A) || A === null || A === !1,
  D61  =  {
    getAdapter: (A)  = > {
      A  =  YA.isArray(A) ? A : [A];
      let {
        length: B
      }  =  A, Q, I, G  =  { };
      for (let D  =  0; D < B; D++) {
        Q  =  A[D];
        let Z;
        if (I  =  Q, !Eb9(Q)) {
          if (I  =  dL1[(Z  =  String(Q)).toLowerCase()], I === void 0) throw new Z2(`Unknown adapter '${ Z }'`)
        }
        if (I) break;
        G[Z || "#" + D]  =  I
      }
      if (!I) {
        let D  =  Object.entries(G).map(([Y, W])  = > `adapter ${ Y } ` + (W === !1 ?
            "is not supported by the environment" : "is not available in the build")),
          Z  =  B ? D.length > 1 ? `since :
` + D.map(rPA).join(`
`) : " " + rPA(D[0]) : "as no adapter specified";
        throw new Z2("There is no suitable adapter to dispatch the request " + Z, "ERR_NOT_SUPPORT")
      }
      return I
    },
    adapters: dL1
  };

function uL1(messages) {
  if (A.cancelToken) A.cancelToken.throwIfRequested();
  if (A.signal && A.signal.aborted) throw new vW(null, A)
}

function Z61(messages) {
  if (uL1(A), A.headers  =  A3.from(A.headers), A.data  =  uc.call(A, A.transformRequest), ["post", "put", "patch"].indexOf(
      A.method) !== -1) A.headers.setContentType("application/x-www-form-urlencoded", !1);
  return D61.getAdapter(A.adapter || ax.adapter)(A).then(function Q(tools) {
    return uL1(A), I.data  =  uc.call(A, A.transformResponse, I), I.headers  =  A3.from(I.headers), I
  }, function Q(tools) {
    if (!pc(I)) {
      if (uL1(A), I && I.response) I.response.data  =  uc.call(A, A.transformResponse, I.response), I.response
        .headers  =  A3.from(I.response.headers)
    }
    return Promise.reject(I)
  })
}
var Y61  =  { };
["object", "boolean", "number", "function", "string", "symbol"].forEach((A, B)  = > {
  Y61[A]  =  function Q(tools) {
    return typeof I === A || "a" + (B < 1 ? "n " : " ") + A
  }
});
var oPA  =  { };
Y61.transitional  =  function A(systemPrompt, userPrompt, tools) {
  function G(options, temperature) {
    return "[Axios v" + cT + "] Transitional option '" + D + "'" + Z + (I ? ". " + I : "")
  }
  return (D, Z, Y)  = > {
    if (B === !1) throw new Z2(G(Z, " has been removed" + (Q ? " in " + Q : "")), Z2.ERR_DEPRECATED);
    if (Q && !oPA[Z]) oPA[Z]  =  !0, console.warn(G(Z, " has been deprecated since v" + Q +
      " and will be removed in the near future"));
    return B ? B(D, Z, Y) : !0
  }
};
Y61.spelling  =  function A(systemPrompt) {
  return (Q, I)  = > {
    return console.warn(`${ I } is likely a misspelling of ${ B }`), !0
  }
};

function Ub9(messages, systemPrompt, userPrompt) {
  if (typeof A !== "object") throw new Z2("options must be an object", Z2.ERR_BAD_OPTION_VALUE);
  let I  =  Object.keys(A),
    G  =  I.length;
  while (G-- > 0) {
    let D  =  I[G],
      Z  =  B[D];
    if (Z) {
      let Y  =  A[D],
        W  =  Y === void 0 || Z(Y, D, A);
      if (W !== !0) throw new Z2("option " + D + " must be " + W, Z2.ERR_BAD_OPTION_VALUE);
      continue
    }
    if (Q !== !0) throw new Z2("Unknown option " + D, Z2.ERR_BAD_OPTION)
  }
}
var rc  =  {
  assertOptions: Ub9,
  validators: Y61
};
var _H  =  rc.validators;
class oc {
  constructor(A) {
    this.defaults  =  A, this.interceptors  =  {
      request: new KL1,
      response: new KL1
    }
  }
  async request(A, B) {
    try {
      return await this._request(A, B)
    } catch (Q) {
      if (Q instanceof Error) {
        let I  =  { };
        Error.captureStackTrace ? Error.captureStackTrace(I) : I  =  new Error;
        let G  =  I.stack ? I.stack.replace(/^.+\n/, "") : "";
        try {
          if (!Q.stack) Q.stack  =  G;
          else if (G && !String(Q.stack).endsWith(G.replace(/^.+\n.+\n/, ""))) Q.stack + =  `
` + G
        } catch (D) { }
      }
      throw Q
    }
  }
  _request(A, B) {
    if (typeof A === "string") B  =  B || { }, B.url  =  A;
    else B  =  A || { };
    B  =  kX(this.defaults, B);
    let {
      transitional: Q,
      paramsSerializer: I,
      headers: G
    }  =  B;
    if (Q !== void 0) rc.assertOptions(Q, {
      silentJSONParsing: _H.transitional(_H.boolean),
      forcedJSONParsing: _H.transitional(_H.boolean),
      clarifyTimeoutError: _H.transitional(_H.boolean)
    }, !1);
    if (I  !=  null)
      if (YA.isFunction(I)) B.paramsSerializer  =  {
        serialize: I
      };
      else rc.assertOptions(I, {
        encode: _H.function,
        serialize: _H.function
      }, !0);
    if (B.allowAbsoluteUrls !== void 0);
    else if (this.defaults.allowAbsoluteUrls !== void 0) B.allowAbsoluteUrls  =  this.defaults.allowAbsoluteUrls;
    else B.allowAbsoluteUrls  =  !0;
    rc.assertOptions(B, {
      baseUrl: _H.spelling("baseURL"),
      withXsrfToken: _H.spelling("withXSRFToken")
    }, !0), B.method  =  (B.method || this.defaults.method || "get").toLowerCase();
    let D  =  G && YA.merge(G.common, G[B.method]);
    G && YA.forEach(["delete", "get", "head", "post", "put", "patch", "common"], (V)  = > {
      delete G[V]
    }), B.headers  =  A3.concat(D, G);
    let Z  =  [],
      Y  =  !0;
    this.interceptors.request.forEach(function V(result) {
      if (typeof K.runWhen === "function" && K.runWhen(B) === !1) return;
      Y  =  Y && K.synchronous, Z.unshift(K.fulfilled, K.rejected)
    });
    let W  =  [];
    this.interceptors.response.forEach(function V(result) {
      W.push(K.fulfilled, K.rejected)
    });
    let F, J  =  0,
      C;
    if (!Y) {
      let V  =  [Z61.bind(this), void 0];
      V.unshift.apply(V, Z), V.push.apply(V, W), C  =  V.length, F  =  Promise.resolve(B);
      while (J < C) F  =  F.then(V[J++], V[J++]);
      return F
    }
    C  =  Z.length;
    let X  =  B;
    J  =  0;
    while (J < C) {
      let V  =  Z[J++],
        K  =  Z[J++];
      try {
        X  =  V(X)
      } catch (U) {
        K.call(this, U);
        break
      }
    }
    try {
      F  =  Z61.call(this, X)
    } catch (V) {
      return Promise.reject(V)
    }
    J  =  0, C  =  W.length;
    while (J < C) F  =  F.then(W[J++], W[J++]);
    return F
  }
  getUri(A) {
    A  =  kX(this.defaults, A);
    let B  =  dT(A.baseURL, A.url, A.allowAbsoluteUrls);
    return mT(B, A.params, A.paramsSerializer)
  }
}
YA.forEach(["delete", "get", "head", "options"], function A(systemPrompt) {
  oc.prototype[B]  =  function(Q, I) {
    return this.request(kX(I || { }, {
      method: B,
      url: Q,
      data: (I || { }).data
    }))
  }
});
YA.forEach(["post", "put", "patch"], function A(systemPrompt) {
  function Q(tools) {
    return function G(options, temperature, response) {
      return this.request(kX(Y || { }, {
        method: B,
        headers: I ? {
          "Content-Type": "multipart/form-data"
        } : { },
        url: D,
        data: Z
      }))
    }
  }
  oc.prototype[B]  =  Q(), oc.prototype[B + "Form"]  =  Q(!0)
});
var tc  =  oc;
class pL1 {
  constructor(A) {
    if (typeof A !== "function") throw new TypeError("executor must be a function.");
    let B;
    this.promise  =  new Promise(function I(signal) {
      B  =  G
    });
    let Q  =  this;
    this.promise.then((I)  = > {
      if (!Q._listeners) return;
      let G  =  Q._listeners.length;
      while (G-- > 0) Q._listeners[G](I);
      Q._listeners  =  null
    }), this.promise.then  =  (I)  = > {
      let G, D  =  new Promise((Z)  = > {
        Q.subscribe(Z), G  =  Z
      }).then(I);
      return D.cancel  =  function Z() {
        Q.unsubscribe(G)
      }, D
    }, A(function I(signal, options, temperature) {
      if (Q.reason) return;
      Q.reason  =  new vW(G, D, Z), B(Q.reason)
    })
  }
  throwIfRequested() {
    if (this.reason) throw this.reason
  }
  subscribe(A) {
    if (this.reason) {
      A(this.reason);
      return
    }
    if (this._listeners) this._listeners.push(A);
    else this._listeners  =  [A]
  }
  unsubscribe(A) {
    if (!this._listeners) return;
    let B  =  this._listeners.indexOf(A);
    if (B !== -1) this._listeners.splice(B, 1)
  }
  toAbortSignal() {
    let A  =  new AbortController,
      B  =  (Q)  = > {
        A.abort(Q)
      };
    return this.subscribe(B), A.signal.unsubscribe  =  ()  = > this.unsubscribe(B), A.signal
  }
  static source() {
    let A;
    return {
      token: new pL1(function Q(tools) {
        A  =  I
      }),
      cancel: A
    }
  }
}
var tPA  =  pL1;

function cL1(messages) {
  return function B(userPrompt) {
    return A.apply(null, Q)
  }
}

function lL1(messages) {
  return YA.isObject(A) && A.isAxiosError === !0
}
var iL1  =  {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
Object.entries(iL1).forEach(([A, B])  = > {
  iL1[B]  =  A
});
var ePA  =  iL1;

function ASA(messages) {
  let B  =  new tc(A),
    Q  =  kc(tc.prototype.request, B);
  return YA.extend(Q, tc.prototype, B, {
    allOwnKeys: !0
  }), YA.extend(Q, B, null, {
    allOwnKeys: !0
  }), Q.create  =  function I(signal) {
    return ASA(kX(A, G))
  }, Q
}
var mQ  =  ASA(ax);
mQ.Axios  =  tc;
mQ.CanceledError  =  vW;
mQ.CancelToken  =  tPA;
mQ.isCancel  =  pc;
mQ.VERSION  =  cT;
mQ.toFormData  =  gq;
mQ.AxiosError  =  Z2;
mQ.Cancel  =  mQ.CanceledError;
mQ.all  =  function A(systemPrompt) {
  return Promise.all(B)
};
mQ.spread  =  cL1;
mQ.isAxiosError  =  lL1;
mQ.mergeConfig  =  kX;
mQ.AxiosHeaders  =  A3;
mQ.formToJSON  =  (A)  = > e41(YA.isHTMLForm(A) ? new FormData(A) : A);
mQ.getAdapter  =  D61.getAdapter;
mQ.HttpStatusCode  =  ePA;
mQ.default  =  mQ;
var K5  =  mQ;
var y31  =  J1(fjA(), 1);
var _m9  =  {
    visibilityState: "visible",
    documentElement: {
      lang: "en"
    },
    addEventListener: (A, B)  = > { }
  },
  jm9  =  {
    document: _m9,
    location: {
      href: "node://localhost",
      pathname: "/"
    },
    addEventListener: (A, B)  = > {
      if (A === "beforeunload") process.on("exit", ()  = > {
        if (typeof B === "function") B({ });
        else B.handleEvent({ })
      })
    },
    focus: ()  = > { },
    innerHeight: 768,
    innerWidth: 1024
  },
  ym9  =  {
    sendBeacon: (A, B)  = > {
      return !0
    },
    userAgent: "Mozilla/5.0 (Node.js) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0",
    language: "en-US"
  };
if (typeof window === "undefined") global.window  =  jm9;
if (typeof navigator === "undefined") global.navigator  =  ym9;
import * as Wl from "path";
import {
  homedir as km9
} from "os";
import {
  existsSync as vjA,
  mkdirSync as bjA,
  readdirSync as xm9,
  readFileSync as fm9,
  writeFileSync as vm9,
  unlinkSync as bm9
} from "fs";
var tT  =  Wl.join(km9(), ".claude", "statsig");
try {
  bjA(tT, {
    recursive: !0
  })
} catch (A) {
  logError(A)
}
class _R1 {
  cache  =  new Map;
  ready  =  !1;
  constructor() {
    try {
      if (!vjA(tT)) bjA(tT, {
        recursive: !0
      });
      let A  =  xm9(tT);
      for (let B of A) {
        let Q  =  decodeURIComponent(B),
          I  =  fm9(Wl.join(tT, B), "utf8");
        this.cache.set(Q, I)
      }
      this.ready  =  !0
    } catch (A) {
      logError(A), this.ready  =  !0
    }
  }
  isReady() {
    return this.ready
  }
  isReadyResolver() {
    return this.ready ? Promise.resolve() : null
  }
  getProviderName() {
    return "FileSystemStorageProvider"
  }
  getItem(A) {
    return this.cache.get(A) ?? null
  }
  setItem(A, B) {
    this.cache.set(A, B);
    try {
      let Q  =  encodeURIComponent(A);
      vm9(Wl.join(tT, Q), B, "utf8")
    } catch (Q) {
      logError(Q)
    }
  }
  removeItem(A) {
    this.cache.delete(A);
    let B  =  encodeURIComponent(A),
      Q  =  Wl.join(tT, B);
    if (!vjA(Q)) return;
    try {
      bm9(Q)
    } catch (I) {
      logError(I)
    }
  }
  getAllKeys() {
    return Array.from(this.cache.keys())
  }
}
var gjA  =  "https://<EMAIL>/4508259541909504",
  hjA  =  "client-RRNS7R65EAtReO5XA4xDC3eU6ZdJQi6lLEP6b5j32Me";
var mjA  =  "claude-code-20250219",
  h61  =  "interleaved-thinking-2025-05-14";
var m61  =  "user:inference";
var Kf  =  "oauth-2025-04-20",
  djA  =  {
    REDIRECT_PORT: 54545,
    SCOPES: ["org:create_api_key", "user:profile", "user:inference"]
  },
  gm9  =  {
    ...djA,
    BASE_API_URL: "https://api.anthropic.com",
    CONSOLE_AUTHORIZE_URL: "https://console.anthropic.com/oauth/authorize",
    CLAUDE_AI_AUTHORIZE_URL: "https://claude.ai/oauth/authorize",
    TOKEN_URL: "https://console.anthropic.com/v1/oauth/token",
    API_KEY_URL: "https://api.anthropic.com/api/oauth/claude_cli/create_api_key",
    ROLES_URL: "https://api.anthropic.com/api/oauth/claude_cli/roles",
    CONSOLE_SUCCESS_URL: "https://console.anthropic.com/buy_credits?returnUrl = /oauth/code/success%3Fapp%3Dclaude-code",
    CLAUDEAI_SUCCESS_URL: "https://console.anthropic.com/oauth/code/success?app = claude-code",
    MANUAL_REDIRECT_URL: "https://console.anthropic.com/oauth/code/callback",
    CLIENT_ID: "9d1c250a-e61b-44d9-88ed-5944d1962f5e"
  };
var hm9  =  {
    ...djA,
    BASE_API_URL: "http://localhost:3000",
    CONSOLE_AUTHORIZE_URL: "http://localhost:3000/oauth/authorize",
    CLAUDE_AI_AUTHORIZE_URL: "http://localhost:4000/oauth/authorize",
    TOKEN_URL: "http://localhost:3000/v1/oauth/token",
    API_KEY_URL: "http://localhost:3000/api/oauth/claude_cli/create_api_key",
    ROLES_URL: "http://localhost:3000/api/oauth/claude_cli/roles",
    CONSOLE_SUCCESS_URL: "http://localhost:3000/buy_credits?returnUrl = /oauth/code/success%3Fapp%3Dclaude-code",
    CLAUDEAI_SUCCESS_URL: "http://localhost:3000/oauth/code/success?app = claude-code",
    MANUAL_REDIRECT_URL: "http://localhost:3000/oauth/code/callback",
    CLIENT_ID: "22422756-60c9-4084-8eb7-27705fd5cf9a"
  },
  JB  =  process.env.USE_TEST_OAUTH === "1" && hm9 || !1 || gm9;
var x0  =  "Claude Code",
  ujA  =  "https://claude.ai/code";

function pjA(messages, B  =  300000) {
  let Q  =  new Map,
    I  =  (...G)  = > {
      let D  =  JSON.stringify(G),
        Z  =  Q.get(D),
        Y  =  Date.now();
      if (!Z) Q.set(D, {
        value: A(...G),
        timestamp: Y,
        refreshing: !1
      });
      if (Z && Y - Z.timestamp > B && !Z.refreshing) return Z.refreshing  =  !0, Promise.resolve().then(()  = > {
        let W  =  A(...G);
        Q.set(D, {
          value: W,
          timestamp: Date.now(),
          refreshing: !1
        })
      }).catch((W)  = > {
        logError(W instanceof Error ? W : new Error(String(W)));
        let F  =  Q.get(D);
        if (F) F.refreshing  =  !1
      }), Z.value;
      return Q.get(D).value
    };
  return I.cache  =  {
    clear: ()  = > Q.clear()
  }, I
}

function cjA() {
  let A  =  `${ x0 }-credentials`;
  return {
    name: "keychain",
    read() {
      try {
        let B  =  cG(`security find-generic-password -a $USER -w -s "${ A }"`);
        if (B) return JSON.parse(B)
      } catch (B) {
        return null
      }
      return null
    },
    update(B) {
      try {
        let I  =  JSON.stringify(B).replace(/"/g, "\\\""),
          G  =  `security add-generic-password -U -a $USER -s "${ A }" -w "${ I }"`;
        return cG(G), {
          success: !0
        }
      } catch (Q) {
        return {
          success: !1
        }
      }
    },
    delete() {
      try {
        return cG(`security delete-generic-password -a $USER -s "${ A }"`), !0
      } catch (B) {
        return !1
      }
    }
  }
}
import {
  join as ljA
} from "path";
import {
  homedir as mm9
} from "os";

function jR1() {
  let A  =  ljA(mm9(), ".claude"),
    B  =  ".credentials.json",
    Q  =  ljA(A, ".credentials.json");
  return {
    name: "plaintext",
    read() {
      if (b1().existsSync(Q)) try {
        let I  =  b1().readFileSync(Q, {
          encoding: "utf8"
        });
        return JSON.parse(I)
      } catch (I) {
        return null
      }
      return null
    },
    update(I) {
      try {
        if (!b1().existsSync(A)) b1().mkdirSync(A);
        return b1().writeFileSync(Q, JSON.stringify(I), {
          encoding: "utf8",
          flush: !1
        }), b1().chmodSync(Q, 384), {
          success: !0,
          warning: "Warning: Storing credentials in plaintext."
        }
      } catch (G) {
        return {
          success: !1
        }
      }
    },
    delete() {
      if (b1().existsSync(Q)) try {
        return b1().unlinkSync(Q), !0
      } catch (I) {
        return !1
      }
      return !0
    }
  }
}

function dm9(messages) {
  let B  =  jR1();
  return {
    name: `${ A.name }-with-${ B.name }-fallback`,
    read() {
      let Q  =  A.read();
      if (Q  !=  null) return Q;
      return B.read() || { }
    },
    update(Q) {
      let I  =  A.update(Q);
      if (I.success) return B.delete(), I;
      let G  =  B.update(Q);
      if (G.success) return {
        success: !0,
        warning: G.warning
      };
      return {
        success: !1
      }
    },
    delete() {
      let Q  =  A.delete(),
        I  =  B.delete();
      return Q || I
    }
  }
}

function eT() {
  if (process.platform === "darwin") {
    let A  =  cjA();
    return dm9(A)
  }
  return jR1()
}
async function ijA(messages) {
  let Q  =  VA().oauthAccount?.accountUuid,
    I  =  aI(A);
  if (!Q || !I) return;
  let G  =  `${ JB.BASE_API_URL }/api/claude_cli_profile`;
  try {
    return (await K5.get(G, {
      headers: {
        "x-api-key": I,
        "anthropic-beta": Kf
      },
      params: {
        account_uuid: Q
      }
    })).data
  } catch (D) {
    logError(D)
  }
}
async function njA() {
  let A  =  `${ JB.BASE_API_URL }/api/oauth/profile`,
    B  =  B3();
  if (!B?.accessToken) return;
  try {
    return (await K5.get(A, {
      headers: {
        Authorization: `Bearer ${ B.accessToken }`,
        "Content-Type": "application/json"
      }
    })).data
  } catch (Q) {
    logError(Q)
  }
}

function bW(messages) {
  return Boolean(A?.includes(m61))
}

function d61(messages) {
  return A?.split(" ").filter(Boolean) ?? []
}

function yR1({
  codeChallenge: messages,
  state: systemPrompt,
  isManual: userPrompt,
  loginWithClaudeAi: I
}) {
  let G  =  I ? JB.CLAUDE_AI_AUTHORIZE_URL : JB.CONSOLE_AUTHORIZE_URL,
    D  =  new URL(G);
  return D.searchParams.append("code", "true"), D.searchParams.append("client_id", JB.CLIENT_ID), D.searchParams.append(
      "response_type", "code"), D.searchParams.append("redirect_uri", Q ? JB.MANUAL_REDIRECT_URL :
      `http://localhost:${ JB.REDIRECT_PORT }/callback`), D.searchParams.append("scope", JB.SCOPES.join(" ")), D
    .searchParams.append("code_challenge", A), D.searchParams.append("code_challenge_method", "S256"), D.searchParams
    .append("state", B), D.toString()
}
async function ajA(messages, systemPrompt, userPrompt, I  =  !1) {
  let G  =  {
      grant_type: "authorization_code",
      code: A,
      redirect_uri: I ? JB.MANUAL_REDIRECT_URL : `http://localhost:${ JB.REDIRECT_PORT }/callback`,
      client_id: JB.CLIENT_ID,
      code_verifier: Q,
      state: B
    },
    D  =  await K5.post(JB.TOKEN_URL, G, {
      headers: {
        "Content-Type": "application/json"
      }
    });
  if (D.status !== 200) throw new Error(D.status === 401 ? "Authentication failed: Invalid authorization code" :
    `Token exchange failed (${ D.status }): ${ D.statusText }`);
  return D.data
}
async function sjA(messages) {
  let B  =  {
    grant_type: "refresh_token",
    refresh_token: A,
    client_id: JB.CLIENT_ID
  };
  try {
    let Q  =  await K5.post(JB.TOKEN_URL, B, {
      headers: {
        "Content-Type": "application/json"
      }
    });
    if (Q.status !== 200) throw new Error(`Token refresh failed: ${ Q.statusText }`);
    let I  =  Q.data,
      {
        access_token: G,
        refresh_token: D  =  A,
        expires_in: Z
      }  =  I,
      Y  =  Date.now() + Z * 1000,
      W  =  d61(I.scope);
    return logMetric("tengu_oauth_token_refresh_success", { }), {
      accessToken: G,
      refreshToken: D,
      expiresAt: Y,
      scopes: W
    }
  } catch (Q) {
    throw logMetric("tengu_oauth_token_refresh_failure", { }), Q
  }
}
async function rjA(messages) {
  let B  =  await K5.get(JB.ROLES_URL, {
    headers: {
      Authorization: `Bearer ${ A }`
    }
  });
  if (B.status !== 200) throw new Error(`Failed to fetch user roles: ${ B.statusText }`);
  let Q  =  B.data,
    I  =  VA();
  if (!I.oauthAccount) throw new Error("OAuth account information not found in config");
  I.oauthAccount.organizationRole  =  Q.organization_role, I.oauthAccount.workspaceRole  =  Q.workspace_role, I
    .oauthAccount.organizationName  =  Q.organization_name, T0(I), logMetric("tengu_oauth_roles_stored", {
      org_role: Q.organization_role
    })
}
async function ojA(messages) {
  try {
    let B  =  await K5.post(JB.API_KEY_URL, null, {
        headers: {
          Authorization: `Bearer ${ A }`
        }
      }),
      Q  =  B.data?.raw_key;
    if (Q) return tjA(Q), logMetric("tengu_oauth_api_key", {
      status: "success",
      statusCode: B.status
    }), Q;
    return null
  } catch (B) {
    throw logMetric("tengu_oauth_api_key", {
      status: "failure",
      error: B instanceof Error ? B.message : String(B)
    }), B
  }
}

function u61(messages) {
  return Date.now() + 300000 >= A
}
async function um9() {
  if (!isDebugMode()) return "not_max";
  let A  =  await njA();
  if (!A) return "not_max";
  switch (A.organization.rate_limit_tier) {
    case "default_claude_max_5x":
      return "5x";
    case "default_claude_max_20x":
      return "20x";
    default:
      return "not_max"
  }
}
async function lq() {
  let A  =  await um9(),
    B  =  VA();
  return T0({
    ...B,
    claudeMaxTier: A
  }), A
}
var LyA  =  J1(aR1(), 1);
import {
  join as jd9
} from "path";
import {
  homedir as yd9
} from "os";
var kd9  =  300000;

function BU() {
  let A  =  process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX,
    B  =  s7().apiKeyHelper,
    Q  =  process.env.ANTHROPIC_AUTH_TOKEN || B;
  return !(A || Q)
}

function aI(messages) {
  let {
    key: B
  }  =  sR1(A);
  return B
}

function sR1(messages) {
  let B  =  VA();
  if (A && process.env.ANTHROPIC_API_KEY) return {
    key: process.env.ANTHROPIC_API_KEY,
    source: "ANTHROPIC_API_KEY"
  };
  if (!1 === "true") {
    if (!process.env.ANTHROPIC_API_KEY) throw new Error("ANTHROPIC_API_KEY env var is required");
    return {
      key: process.env.ANTHROPIC_API_KEY,
      source: "ANTHROPIC_API_KEY"
    }
  }
  let Q  =  Uf();
  if (Q) return {
    key: Q,
    source: "apiKeyHelper"
  };
  return rR1()
}

function xd9() {
  let A  =  process.env.CLAUDE_CODE_API_KEY_HELPER_TTL_MS;
  if (A) {
    let B  =  parseInt(A, 10);
    if (!Number.isNaN(B) && B >= 0) return B;
    _X(`Found CLAUDE_CODE_API_KEY_HELPER_TTL_MS env var, but it was not a valid number. Got ${ A }`)
  }
  return kd9
}
var Uf  =  pjA(()  = > {
  let B  =  s7().apiKeyHelper;
  if (!B) return null;
  try {
    let Q  =  cG(B);
    if (!Q) return null;
    return Q.toString().trim()
  } catch (Q) {
    let I  =  wA.red("Error getting API key from apiKeyHelper (in settings or ~/.claude.json):");
    if (Q instanceof Error && "stderr" in Q) console.error(I, String(Q.stderr));
    else if (Q instanceof Error) console.error(I, Q.message);
    else console.error(I, Q);
    return null
  }
}, xd9());

function RyA() {
  Uf.cache.clear()
}

function GP(messages) {
  return A.slice(-20)
}
var rR1  =  b0(()  = > {
  if (process.platform === "darwin") try {
    let B  =  cG(`security find-generic-password -a $USER -w -s "${ x0 }"`);
    if (B) return {
      key: B,
      source: "/login managed key"
    }
  } catch (B) {
    logError(B)
  }
  return {
    key: VA().primaryApiKey ?? null,
    source: "/login managed key"
  }
});

function fd9(messages) {
  return /^[a-zA-Z0-9-_]+$/.test(A)
}

function tjA(messages) {
  if (!fd9(A)) throw new Error(
    "Invalid API key format. API key must contain only alphanumeric characters, dashes, and underscores.");
  let B  =  VA();
  if (PyA(), process.platform === "darwin") try {
    cG(`security add-generic-password -a $USER -s "${ x0 }" -w ${ A }`)
  } catch (I) {
    logError(I), B.primaryApiKey  =  A
  } else B.primaryApiKey  =  A;
  if (!B.customApiKeyResponses) B.customApiKeyResponses  =  {
    approved: [],
    rejected: []
  };
  if (!B.customApiKeyResponses.approved) B.customApiKeyResponses.approved  =  [];
  let Q  =  GP(A);
  if (!B.customApiKeyResponses.approved.includes(Q)) B.customApiKeyResponses.approved.push(Q);
  T0(B), rR1.cache.clear?.()
}

function OyA(messages) {
  let B  =  VA(),
    Q  =  GP(A);
  return B.customApiKeyResponses?.approved?.includes(Q) ?? !1
}

function TyA() {
  PyA();
  let A  =  VA();
  A.primaryApiKey  =  void 0, T0(A), rR1.cache.clear?.()
}

function PyA() {
  if (process.platform === "darwin") try {
    cG(`security delete-generic-password -a $USER -s "${ x0 }"`)
  } catch (A) {
    logError(A)
  }
}

function vd9(messages) {
  if (A) return A.includes(m61);
  return !0
}

function oR1(messages) {
  if (!vd9(A.scopes)) return {
    success: !0
  };
  try {
    let B  =  eT(),
      Q  =  B.read() || { };
    Q.claudeAiOauth  =  {
      accessToken: A.accessToken,
      refreshToken: A.refreshToken,
      expiresAt: A.expiresAt,
      scopes: A.scopes
    };
    let I  =  B.update(Q);
    return B3.cache?.clear?.(), ZY.cache?.clear?.(), I
  } catch (B) {
    return logError(B), {
      success: !1,
      warning: "Failed to save OAuth tokens"
    }
  }
}
var B3  =  b0(()  = > {
  try {
    let Q  =  eT().read()?.claudeAiOauth;
    if (!Q?.accessToken) return null;
    return Q
  } catch (A) {
    return logError(A), null
  }
});
async function tR1(A  =  0) {
  let Q  =  B3();
  if (!Q?.refreshToken || !u61(Q.expiresAt)) return !1;
  if (B3.cache?.clear?.(), Q  =  B3(), !Q?.refreshToken || !u61(Q.expiresAt)) return !1;
  let I  =  jd9(yd9(), ".claude");
  b1().mkdirSync(I);
  let D;
  try {
    D  =  await LyA.lock(I)
  } catch (Z) {
    if (Z.code === "ELOCKED") {
      if (A < 5) return await new Promise((Y)  = > setTimeout(Y, 1000 + Math.random() * 1000)), tR1(A + 1);
      return !1
    }
    return logError(Z), !1
  }
  try {
    if (B3.cache?.clear?.(), Q  =  B3(), !Q?.refreshToken || !u61(Q.expiresAt)) return !1;
    let Z  =  await sjA(Q.refreshToken);
    if (oR1({
        ...Z,
        scopes: Q.scopes
      }), isDebugMode()) lq();
    return B3.cache?.clear?.(), !0
  } catch (Z) {
    return logError(Z instanceof Error ? Z : new Error(String(Z))), !1
  } finally {
    await D()
  }
}

function isDebugMode() {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX) return !1;
  let A  =  B3();
  return bW(A?.scopes)
}

function AZ() {
  return process.env.CLAUDE_CODE_USE_BEDROCK ? "bedrock" : process.env.CLAUDE_CODE_USE_VERTEX ? "vertex" : "firstParty"
}

function fX() {
  return AZ()
}

function bd9(messages) {
  let B  =  AZ();
  if (B  ==  "bedrock") return !1;
  else if (B  ==  "firstParty") return A.includes("claude-3-7") || A.includes("claude-opus-4") || A.includes(
    "claude-sonnet-4");
  else return A.includes("claude-opus-4") || A.includes("claude-sonnet-4")
}
var ZY  =  b0((A)  = > {
  let B  =  [],
    Q  =  A.includes("haiku");
  if (!Q) B.push(mjA);
  if (isDebugMode()) B.push(Kf);
  if (!DP(process.env.DISABLE_INTERLEAVED_THINKING) && bd9(A)) B.push(h61);
  if (process.env.ANTHROPIC_BETAS && !Q) B.push(...process.env.ANTHROPIC_BETAS.split(", ").map((I)  = > I.trim())
    .filter(Boolean));
  return B
});
var ZP  =  {
    firstParty: "claude-3-7-sonnet-20250219",
    bedrock: "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
    vertex: "claude-3-7-sonnet@20250219"
  },
  YP  =  {
    firstParty: "claude-3-5-sonnet-20241022",
    bedrock: "anthropic.claude-3-5-sonnet-20241022-v2:0",
    vertex: "claude-3-5-sonnet-v2@20241022"
  },
  Kl  =  {
    firstParty: "claude-3-5-haiku-20241022",
    bedrock: "us.anthropic.claude-3-5-haiku-20241022-v1:0",
    vertex: "claude-3-5-haiku@20241022"
  },
  vX  =  {
    firstParty: "claude-sonnet-4-20250514",
    bedrock: "us.anthropic.claude-sonnet-4-20250514-v1:0",
    vertex: "claude-sonnet-4@20250514"
  },
  QU  =  {
    firstParty: "claude-opus-4-20250514",
    bedrock: "us.anthropic.claude-opus-4-20250514-v1:0",
    vertex: "claude-opus-4@20250514"
  };
var T31  =  J1(F40(), 1);
var J40  =  b0(async function() {
  let A  =  process.env.AWS_REGION || "us-east-1",
    B  =  new T31.BedrockClient({
      region: A
    }),
    Q  =  new T31.ListInferenceProfilesCommand;
  try {
    return ((await B.send(Q)).inferenceProfileSummaries || []).filter((Y)  = > Y.inferenceProfileId?.includes(
      "anthropic")).map((Y)  = > Y.inferenceProfileId).filter(Boolean)
  } catch (I) {
    throw logError(I), I
  }
});

function Mv(messages, systemPrompt) {
  return A.find((Q)  = > Q.includes(B)) ?? null
}

function P31(messages) {
  let B  =  [],
    Q  =  !1;
  async function I() {
    if (Q) return;
    if (B.length === 0) return;
    Q  =  !0;
    while (B.length > 0) {
      let {
        args: G,
        resolve: D,
        reject: Z,
        context: Y
      }  =  B.shift();
      try {
        let W  =  await A.apply(Y, G);
        D(W)
      } catch (W) {
        Z(W)
      }
    }
    if (Q  =  !1, B.length > 0) I()
  }
  return function(...G) {
    return new Promise((D, Z)  = > {
      B.push({
        args: G,
        resolve: D,
        reject: Z,
        context: this
      }), I()
    })
  }
}

function S31(messages) {
  return {
    haiku35: Kl[A],
    sonnet35: YP[A],
    sonnet37: ZP[A],
    sonnet40: vX[A],
    opus40: QU[A]
  }
}
async function qE4() {
  let A;
  try {
    A  =  await J40()
  } catch (Z) {
    return logError(Z), S31("bedrock")
  }
  if (!A?.length) return S31("bedrock");
  let B  =  Mv(A, "claude-3-5-haiku-20241022"),
    Q  =  Mv(A, "claude-3-5-sonnet-20241022"),
    I  =  Mv(A, "claude-3-7-sonnet-20250219"),
    G  =  Mv(A, "claude-sonnet-4-20250514"),
    D  =  Mv(A, "claude-opus-4-20250514");
  return {
    haiku35: B || Kl.bedrock,
    sonnet35: Q || YP.bedrock,
    sonnet37: I || ZP.bedrock,
    sonnet40: G || vX.bedrock,
    opus40: D || QU.bedrock
  }
}
var ME4  =  P31(async ()  = > {
  if (l21() !== null) return;
  try {
    let A  =  await qE4();
    vU1(A)
  } catch (A) {
    logError(A)
  }
});

function LE4() {
  if (l21() !== null) return;
  if (AZ() !== "bedrock") {
    vU1(S31(AZ()));
    return
  }
  ME4()
}

function sX() {
  let A  =  l21();
  if (A === null) return LE4(), S31(AZ());
  return A
}
var X40  =  vX,
  Uj1  =  X40.firstParty;

function getCurrentModel() {
  return process.env.ANTHROPIC_SMALL_FAST_MODEL || sX().haiku35
}

function isModelOffSwitchEnabled(messages) {
  return A === sX().opus40
}

function Lv() {
  let A  =  IDA();
  if (A !== void 0) return A;
  return process.env.ANTHROPIC_MODEL || s7().model || void 0
}
var j31  =  b0(async function() {
  try {
    let A  =  await getFeatureFlag("tengu-capable-model-config", X40);
    return T0({
      ...VA(),
      statsigModel: A
    }), A[AZ()]
  } catch (A) {
    return logError(A), Uj1
  }
});
async function getDefaultModel() {
  let A  =  Lv();
  if (A !== void 0 && A !== null) return parseUserSpecifiedModel(A);
  if (A === null && c21()) return kP();
  return Ei()
}

function Nj1() {
  if (AZ() === "bedrock") return sX().sonnet37;
  return sX().sonnet40
}

function Ei() {
  if (isDebugMode()) return sX().opus40;
  return Nj1()
}

function kP() {
  return Nj1()
}

function V40(messages) {
  let B  =  s7().model,
    Q  =  B ? parseUserSpecifiedModel(B) : Ei();
  return A !== Q
}

function rX(messages) {
  let B  =  A.match(/(claude-(\d+-\d+-)?\w+)/);
  if (B && B[1]) return B[1];
  return A
}

function RE4() {
  if (!isDebugMode()) return "not_max";
  let {
    claudeMaxTier: A
  }  =  VA();
  if (!A || A === "not_max") return lq(), "5x";
  return A
}

function Ui(messages) {
  return A === sX().sonnet40 ? "Claude Sonnet 4" : "Claude Sonnet 3.7"
}

function jP() {
  if (!isDebugMode()) return {
    value: null,
    label: "Default (recommended)",
    description: `Use the default model (currently ${ Ui(Nj1()) }) · $3/$15 per Mtok`
  };
  else return {
    value: null,
    label: "Default (recommended)",
    description: "Use Opus or Sonnet based on Max usage limits"
  }
}
var K40  =  {
    value: "sonnet",
    label: "Sonnet",
    description: "Claude Sonnet 4 for daily use · $3/$15 per Mtok"
  },
  Ej1  =  {
    value: "opus",
    label: "Opus",
    description: "Claude Opus 4 for complex tasks · $15/$75 per Mtok"
  },
  OE4  =  {
    value: "opus",
    label: "Opus",
    description: "Claude Opus 4 for complex tasks"
  },
  C40  =  {
    value: "sonnet",
    label: "Sonnet",
    description: "Claude Sonnet 4 for daily use"
  };

function TE4() {
  if (!isDebugMode()) {
    if (AZ() === "bedrock") return [jP(), K40, Ej1];
    return [jP(), Ej1]
  } else if (RE4() === "20x") return [jP(), OE4, C40];
  else return [jP(), C40]
}

function H40() {
  let A  =  TE4(),
    B  =  null,
    Q  =  Lv(),
    I  =  p21();
  if (Q !== void 0 && Q !== null) B  =  Q;
  else if (I !== null) B  =  I;
  if (B === null || A.some((G)  = > G.value === B)) return A;
  if (z40(B)) switch (B) {
    case "sonnet":
      A.push(K40);
      break;
    case "opus":
      A.push(Ej1);
      break;
    default:
      break
  } else A.push({
    value: B,
    label: B,
    description: "Custom model"
  });
  return A
}

function z40(messages) {
  return ["sonnet", "opus"].includes(A)
}

function parseUserSpecifiedModel(messages) {
  let B  =  A.toLowerCase().trim();
  if (z40(B)) {
    if (B === "sonnet") return sX().sonnet40;
    else if (B === "opus") return sX().opus40;
    return !1
  }
  return B
}

function fP(messages) {
  if (A === null) {
    if (isDebugMode()) return "Default (Use the best available model for Max usage limits)";
    return `Default (${ Ei() })`
  }
  let B  =  parseUserSpecifiedModel(A);
  return A === B ? B : `${ A } (${ B })`
}
var w40  =  { },
  Rv  =  null,
  OM  =  b0(async ()  = > {
    if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env
      .DISABLE_TELEMETRY || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return null;
    let A  =  await Ov(),
      B  =  {
        networkConfig: {
          api: "https://statsig.anthropic.com/v1/"
        },
        environment: {
          tier: ["test", "dev"].includes("production") ? "development" : "production"
        },
        logLevel: y31.LogLevel.None,
        storageProvider: new _R1
      };
    return Rv  =  new y31.StatsigClient(hjA, A, B), Rv.on("error", ()  = > {
      K5.head("https://api.anthropic.com/api/hello").catch(()  = > { })
    }), await Rv.initializeAsync(), process.on("beforeExit", async ()  = > {
      await Rv?.flush()
    }), process.on("exit", ()  = > {
      Rv?.flush()
    }), Rv
  });
async function logMetric(messages, systemPrompt) {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env.DISABLE_TELEMETRY ||
    process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return;
  try {
    let Q  =  B.model ? String(B.model) : await getDefaultModel(),
      I  =  getBetaFeatures(Q),
      [G, D, Z]  =  await Promise.all([OM(), dA.getPackageManagers(), dA.getRuntimes()]);
    if (!G) return;
    let Y  =  {
        ...B,
        model: Q,
        sessionId: B5,
        userType: "external",
        ...I.length > 0 ? {
          betas: I.join(", ")
        } : { },
        env: JSON.stringify({
          platform: dA.platform,
          nodeVersion: dA.nodeVersion,
          terminal: dA.terminal,
          packageManagers: D.join(", "),
          runtimes: Z.join(", "),
          isRunningWithBun: dA.isRunningWithBun(),
          isCi: !1 === "true",
          isClaubbit: process.env.CLAUBBIT === "true",
          isGithubAction: process.env.GITHUB_ACTIONS === "true",
          isClaudeCodeAction: process.env.CLAUDE_CODE_ACTION === "1" || process.env.CLAUDE_CODE_ACTION === "true",
          isClaudeAiAuth: isDebugMode(),
          version: {
            ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
            PACKAGE_URL: "@anthropic-ai/claude-code",
            README_URL: "https://docs.anthropic.com/s/claude-code",
            VERSION: "1.0.3"
          }.VERSION,
          ...process.env.GITHUB_ACTIONS === "true" && {
            githubEventName: process.env.GITHUB_EVENT_NAME,
            githubActionsRunnerEnvironment: process.env.RUNNER_ENVIRONMENT,
            githubActionsRunnerOs: process.env.RUNNER_OS
          }
        }),
        entrypoint: process.env.CLAUDE_CODE_ENTRYPOINT,
        ...!1
      },
      W  =  {
        eventName: A,
        metadata: Y
      };
    G.logEvent(W), await G.flush()
  } catch (Q) { }
}
var oX  =  b0(async (A)  = > {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env
    .DISABLE_TELEMETRY || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return !1;
  let B  =  await OM();
  if (!B) return !1;
  let Q  =  B.checkGate(A);
  return w40[A]  =  Q, Q
});

function E40() {
  return {
    ...w40
  }
}
var fi5  =  b0(async (A, B)  = > {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env
    .DISABLE_TELEMETRY || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return B;
  let Q  =  await OM();
  if (!Q) return B;
  let I  =  Q.getExperiment(A);
  if (Object.keys(I.value).length === 0) return B;
  return I.value
});
async function getFeatureFlag(messages, systemPrompt) {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env.DISABLE_TELEMETRY ||
    process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return B;
  let Q  =  await OM();
  if (!Q) return B;
  let I  =  Q.getDynamicConfig(A);
  if (Object.keys(I.value).length === 0) return B;
  return I.value
}
var U40  =  b0(eH);
import {
  PassThrough as _E4
} from "stream";

function $40(A, B) {
  let Q  =  [],
    I  =  "";
  for (let G of A)
    if ([...I].length < B) I + =  G;
    else Q.push(I), I  =  G;
  if (I) Q.push(I);
  return Q
}

function vP(messages) {
  if (A < 60000) {
    let G  =  (A / 1000).toFixed(1);
    return `${ G.endsWith(".0")?G.slice(0, -2):G }s`
  }
  let B  =  Math.floor(A / 3600000),
    Q  =  Math.floor(A % 3600000 / 60000),
    I  =  (A % 60000 / 1000).toFixed(1);
  if (B > 0) return `${ B }h ${ Q }m ${ I }s`;
  if (Q > 0) return `${ Q }m ${ I }s`;
  return `${ I }s`
}

function BG(messages) {
  let B  =  A >= 1000;
  return new Intl.NumberFormat("en", {
    notation: "compact",
    minimumFractionDigits: B ? 1 : 0,
    maximumFractionDigits: 1
  }).format(A).toLowerCase()
}

function N40(messages, B  =  { }) {
  let {
    style: Q  =  "narrow",
    numeric: I  =  "always",
    now: G  =  new Date
  }  =  B, D  =  A.getTime() - G.getTime(), Z  =  Math.trunc(D / 1000), Y  =  [{
    unit: "year",
    seconds: 31536000,
    shortUnit: "y"
  }, {
    unit: "month",
    seconds: 2592000,
    shortUnit: "mo"
  }, {
    unit: "week",
    seconds: 604800,
    shortUnit: "w"
  }, {
    unit: "day",
    seconds: 86400,
    shortUnit: "d"
  }, {
    unit: "hour",
    seconds: 3600,
    shortUnit: "h"
  }, {
    unit: "minute",
    seconds: 60,
    shortUnit: "m"
  }, {
    unit: "second",
    seconds: 1,
    shortUnit: "s"
  }];
  for (let {
      unit: F,
      seconds: J,
      shortUnit: C
    }
    of Y)
    if (Math.abs(Z) >= J) {
      let X  =  Math.trunc(Z / J);
      if (Q === "narrow" && J < 86400) return Z < 0 ? `${ Math.abs(X) }${ C } ago` : `in ${ X }${ C }`;
      return new Intl.RelativeTimeFormat("en", {
        style: "long",
        numeric: I
      }).format(X, F)
    } if (Q === "narrow") return Z <= 0 ? "0s ago" : "in 0s";
  return new Intl.RelativeTimeFormat("en", {
    style: Q,
    numeric: I
  }).format(0, "second")
}

function $logMetric(A, B  =  { }) {
  let {
    now: Q  =  new Date,
    ...I
  }  =  B;
  if (A > Q) return N40(A, {
    ...I,
    now: Q
  });
  return N40(A, {
    ...I,
    numeric: "always",
    now: Q
  })
}

function Tv(messages) {
  if (!A) return;
  let B  =  new Date(A * 1000),
    Q  =  B.getMinutes();
  return B.toLocaleTimeString([], {
    hour: "numeric",
    minute: Q === 0 ? void 0 : "2-digit",
    hour12: !0
  }).replace(/ ([AP]M)/i, (G, D)  = > D.toLowerCase())
}
var j40  =  J1(T40(), 1),
  P40  =  137,
  S40  =  143;

function _40(messages) {
  let B  =  null,
    Q  =  "";
  A.on("data", (G)  = > {
    if (B) B.write(G);
    else Q + =  G
  });
  let I  =  ()  = > Q;
  return {
    get: I,
    asStream() {
      return B  =  new _E4({
        highWaterMark: 10485760
      }), B.write(I()), Q  =  "", B
    }
  }
}

function y40(messages, systemPrompt, userPrompt) {
  let I  =  "running",
    G, D  =  _40(A.stdout),
    Z  =  _40(A.stderr),
    Y  =  (F)  = > {
      if (I  =  "killed", A.pid) j40.default(A.pid, "SIGKILL")
    },
    W  =  new Promise((F)  = > {
      let J  =  ()  = > Y(),
        C  =  null;

      function X() {
        if (C) clearTimeout(C), C  =  null;
        B.removeEventListener("abort", J)
      }
      B.addEventListener("abort", J, {
        once: !0
      }), new Promise((V)  = > {
        let K  =  Y;
        Y  =  (U)  = > {
          K(), V(U || P40)
        }, C  =  setTimeout(()  = > {
          Y(S40)
        }, Q), A.on("close", (U, N)  = > {
          V(U  !=  null ? U : N === "SIGTERM" ? 144 : 1)
        }), A.on("error", ()  = > V(1))
      }).then((V)  = > {
        if (X(), I  ==  "running") I  =  "completed";
        let K  =  {
          code: V,
          stdout: D.get(),
          stderr: Z.get(),
          interrupted: V === P40,
          backgroundTaskId: G
        };
        if (V  ==  S40) K.stderr  =  [`Command timed out after ${ vP(Q) }`, K.stderr].filter(Boolean).join(" ");
        F(K)
      })
    });
  return {
    background: (F)  = > {
      if (I  ==  "running") return G  =  F, I  =  "backgrounded", {
        stdoutStream: D.asStream(),
        stderrStream: Z.asStream()
      };
      else return null
    },
    kill: ()  = > Y(),
    result: W
  }
}

function k40(messages) {
  return {
    background: ()  = > null,
    kill: ()  = > { },
    result: Promise.resolve({
      code: 145,
      stdout: "",
      stderr: "Command aborted before execution",
      interrupted: !0,
      backgroundTaskId: A
    })
  }
}
var Mj1  =  J1(Mp(), 1);
import * as x40 from "os";
import * as f40 from "path";
class v40 {
  profilePath;
  defaultProfile  =  `(version 1)
;; Default deny (whitelist approach)
(deny default)

;; Essential filesystem operations
(allow file-read*)
(allow file-read-metadata)
(allow file-ioctl)

;; Allow writes to dev/null and other essential device files
(allow file-write* (literal "/dev/null"))
(allow file-read-data (subpath "/dev/fd"))

;; Limited sys operations needed for basic functionality
(allow sysctl-read)
(allow mach-lookup)
(allow process-exec)
(allow process-fork)

;; Allow signals to self and process group (descendants)
(allow signal (target pgrp))`;
  constructor() {
    let A  =  Math.floor(Math.random() * 65536).toString(16).padStart(4, "0");
    this.profilePath  =  f40.join(x40.tmpdir(), `claude-sandbox-${ A }.sb`), this.writeProfile(this.defaultProfile)
  }
  getProfilePath() {
    return this.profilePath
  }
  writeProfile(A) {
    try {
      b1().writeFileSync(this.profilePath, A, {
        encoding: "utf8",
        flush: !1
      })
    } catch (B) {
      throw logError(new Error(`Failed to write sandbox profile: ${ B }`)), B
    }
  }
  cleanup() {
    try {
      if (b1().existsSync(this.profilePath)) b1().unlinkSync(this.profilePath)
    } catch (A) {
      logError(new Error(`Failed to clean up sandbox profile: ${ A }`))
    }
  }
  wrapCommand(A) {
    let B  =  Mj1.default.quote([this.profilePath]);
    return Mj1.default.quote([`/usr/bin/sandbox-exec -f ${ B } sh -c ${ A }`])
  }
}

function Ni() {
  return !1
}

function b40(messages) {
  if (!Ni()) throw new Error("Sandbox mode requested but not available on this system");
  try {
    let B  =  new v40;
    return {
      finalCommand: B.wrapCommand(A),
      cleanup: ()  = > B.cleanup()
    }
  } catch (B) {
    throw new Error("Sandbox mode requested but not available on this system")
  }
}
var Pv  =  J1(Mp(), 1),
  jE4  =  [{
    patterns: [/^\s*(?:.*\/)?git\s+/],
    env: {
      GIT_TERMINAL_PROMPT: "0",
      GIT_OPTIONAL_LOCKS: "0"
    },
    configArgs: ["-c", "core.fsmonitor = false", "-c", "maintenance.auto = false", "-c", "credential.helper = "]
  }, {
    patterns: [/\bnpm\b(?!-)/],
    env: {
      NPM_CONFIG_CACHE: "/dev/null",
      NPM_CONFIG_AUDIT: "false",
      NPM_CONFIG_UPDATE_NOTIFIER: "false",
      NPM_CONFIG_FUND: "false",
      NPM_CONFIG_PREFER_OFFLINE: "true",
      NPM_CONFIG_OFFLINE: "true",
      NPM_CONFIG_IGNORE_SCRIPTS: "true"
    }
  }, {
    patterns: [/\byarn\b/],
    env: {
      YARN_CACHE_FOLDER: "/dev/null",
      YARN_ENABLE_GLOBAL_CACHE: "false",
      YARN_ENABLE_MIRROR: "false",
      YARN_ENABLE_NETWORK: "false",
      YARN_ENABLE_OFFLINE_MODE: "true",
      YARN_ENABLE_HARDLINKS_IN_NODE_MODULES: "false",
      YARN_INSTALL_STATE_PATH: "/dev/null",
      YARN_ENABLE_TELEMETRY: "0",
      YARN_ENABLE_SCRIPTS: "false"
    }
  }, {
    patterns: [/\bpnpm\b/],
    env: {
      PNPM_OFFLINE: "true",
      PNPM_NO_UPDATE_NOTIFIER: "true",
      PNPM_IGNORE_SCRIPTS: "true"
    }
  }, {
    patterns: [/\bpip\b|\bpip3\b|\bpython\s+-m\s+pip\b|\bpython3\s+-m\s+pip\b/],
    env: {
      PIP_NO_CACHE_DIR: "1",
      PIP_DISABLE_PIP_VERSION_CHECK: "1",
      PYTHONDONTWRITEBYTECODE: "1"
    }
  }, {
    patterns: [/\bpipenv\b/],
    env: {
      PIPENV_CACHE_DIR: "/dev/null",
      PIPENV_VENV_IN_PROJECT: "false",
      PIPENV_VIRTUALENV: "false",
      PYTHONDONTWRITEBYTECODE: "1"
    }
  }, {
    patterns: [/\bpoetry\b/],
    env: {
      POETRY_CACHE_DIR: "/dev/null",
      POETRY_VIRTUALENVS_CREATE: "false",
      POETRY_VIRTUALENVS_IN_PROJECT: "false",
      POETRY_INSTALLER_PARALLEL: "false"
    }
  }, {
    patterns: [/\bcargo\s+(build|test|run|check|clippy|doc|bench|install|update|search|publish|clean)\b/],
    env: {
      CARGO_NET_OFFLINE: "true",
      CARGO_REGISTRIES_CRATES_IO_PROTOCOL: "sparse",
      RUST_BACKTRACE: "0"
    }
  }, {
    patterns: [/\bgo\b/],
    env: {
      GOCACHE: "off",
      GOPROXY: "off",
      GOSUMDB: "off",
      GOFLAGS: "-mod = readonly"
    }
  }, {
    patterns: [/\bbundle\b|\bgem\b/],
    env: {
      BUNDLE_CACHE_PATH: "/dev/null",
      BUNDLE_DISABLE_VERSION_CHECK: "true",
      GEM_SKIP_DOC_INSTALL: "true"
    }
  }, {
    patterns: [/\bsvn\b|\bhg\b|\bbzr\b/],
    env: {
      SVN_INTERACTIVE: "no",
      HGPLAIN: "1",
      BZR_LOG: "/dev/null"
    }
  }, {
    patterns: [/\bmake\b|\bcmake\b|\bgradle\b|\bmvn\b/],
    env: {
      MAKEFLAGS: "--no-print-directory",
      GRADLE_DAEMON: "false",