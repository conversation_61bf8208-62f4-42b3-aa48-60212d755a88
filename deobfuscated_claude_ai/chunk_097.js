/**
 * Claude AI Integration - Deobfuscated Code
 *
 * This file contains the core AI integration logic for Claude Code CLI.
 * Functions have been renamed for clarity and comments added for understanding.
 *
 * Key components:
 * - API calling functions (callClaudeAPI, streamClaudeAPI)
 * - Conversation loop management (mainConversationLoop, coreConversationHandler)
 * - Tool execution and validation
 * - Message formatting and context management
 * - Error handling and retry logic
 */

// Chunk 97
// Lines 291001-294000
// Size: 75390 bytes

    shouldSkipPermissionCheck: !1
  };
  if (Y.isApiErrorMessage) return {
    type: "binary_feedback_result",
    message: Z,
    shouldSkipPermissionCheck: !1
  };
  if (Z.isApiErrorMessage) return {
    type: "binary_feedback_result",
    message: Y,
    shouldSkipPermissionCheck: !1
  };
  if (!await YF2(Z, Y, A)) return {
    type: "binary_feedback_result",
    message: Z,
    shouldSkipPermissionCheck: !1
  };
  return await Q(Z, Y)
}
/**
 * Main conversation loop entry point
 * Filters out stream events and request start events
 * Delegates to core conversation handler
 */
// === CONVERSATION MANAGEMENT ===

async function* mainConversationLoop(A, B, Q, I, G, D, Z) {
  for await (let Y of coreConversationHandler(A, B, Q, I, G, D, Z)) if (Y.type !== "stream_event" && Y.type !== "stream_request_start")
    yield Y
}
/**
 * Core conversation processing logic
 * Handles message compression, tool execution, and context management
 * Manages conversation state and error recovery
 */
async function* coreConversationHandler(A, B, Q, I, G, D, Z, Y) {
  yield {
    type: "stream_request_start"
  };
  let W  =  A;

  function F(error) {
    return streamAPIRequest(Pu0(W, Q), Tu0(B, I), D.options.maxThinkingTokens, D.options.tools, D.abortController.signal, {
      getToolPermissionContext: D.getToolPermissionContext,
      model: O ?? D.options.mainLoopModel,
      prependCLISysprompt: !0,
      toolChoice: void 0,
      isNonInteractiveSession: D.options.isNonInteractiveSession
    })
  }
  let J  =  Y,
    {
      messages: C,
      wasCompacted: X
    }  =  await compressMessages(A, D);
  if (X) {
    if (logMetric("tengu_auto_compact_succeeded", {
        originalMessageCount: A.length,
        compactedMessageCount: C.length
      }), !J?.compacted) J  =  {
      compacted: !0,
      turnId: ZA5(),
      turnCounter: 0
    };
    W  =  C
  }
  let V  =  WA5(D, F, Z),
    K;
  try {
    do
      if (K  =  await V.next(), K.value.type === "stream_event") yield K.value; while (!K.done)
  } catch (O) {
    logError(O instanceof Error ? O : new Error(String(O))), yield createErrorResponse({
      toolUse: !1,
      hardcodedMessage: void 0
    });
    return
  }
  if (!K || !K.value) {
    yield createErrorResponse({
      toolUse: !1
    });
    return
  }
  if (K.value.message === null) {
    yield createErrorResponse({
      toolUse: !1
    });
    return
  }
  let U  =  createStreamResponse(K.value.message),
    N  =  K.value.shouldSkipPermissionCheck;
  yield U;
  let q  =  U.message.content.filter((O)  = > O.type === "tool_use");
  if (!q.length) return;
  let M  =  [];
  for await (let O of executeToolBlocks(q, U, G, D, N)) yield O, M.push(...processMessageHistory([O]).filter((S)  = > S.type === "user"));
  if (D.abortController.signal.aborted) {
    yield createErrorResponse({
      toolUse: !0,
      hardcodedMessage: void 0
    });
    return
  }
  let R  =  M.sort((O, S)  = > {
    let f  =  q.findIndex((g)  = > g.id === (O.type === "user" && O.message.content[0].id)),
      a  =  q.findIndex((g)  = > g.id === (S.type === "user" && S.message.content[0].id));
    return f - a
  });
  if (J?.compacted) J.turnCounter++, logMetric("tengu_post_autocompact_turn", {
    turnId: J.turnId,
    turnCounter: J.turnCounter
  });
  let T  =  [...D.getQueuedCommands()];
  for await (let O of uX1(null, D, null, T)) yield O, M.push(O);
  D.removeQueuedCommands(T), yield* await coreConversationHandler([...W, U, ...R], B, Q, I, G, D, Z, J)
}
async function* executeToolBlocks(A, B, Q, I, G) {
  let D  =  A.reduce((Z, Y)  = > {
    let W  =  I.options.tools.find((C)  = > C.name === Y.name),
      F  =  W?.inputSchema.safeParse(Y.input),
      J  =  F?.success ? Boolean(W?.isReadOnly(F.data)) : !1;
    if (J && Z[Z.length - 1]?.isReadOnly) Z[Z.length - 1].blocks.push(Y);
    else Z.push({
      isReadOnly: J,
      blocks: [Y]
    });
    return Z
  }, []);
  for (let {
      isReadOnly: Z,
      blocks: Y
    }
    of D)
    if (Z) yield* executeReadOnlyTools(Y, B, Q, I, G);
    else yield* executeNonReadOnlyTools(Y, B, Q, I, G)
}
async function* executeNonReadOnlyTools(A, B, Q, I, G) {
  for (let D of A) yield* executeSingleTool(D, B, Q, I, G)
}
async function* executeReadOnlyTools(A, B, Q, I, G) {
  yield* uF1(A.map((D)  = > executeSingleTool(D, B, Q, I, G)), YA5)
}
/**
 * Executes a single tool with validation and error handling
 * Checks tool availability, validates input, and processes results
 * Handles cancellation and permission checking
 */
// === TOOL EXECUTION ===

async function* executeSingleTool(A, B, Q, I, G) {
  let D  =  A.name,
    Z  =  I.options.tools.find((W)  = > W.name === D);
  if (!Z) {
    logMetric("tengu_tool_use_error", {
      error: `No such tool available: ${ D }`,
      toolName: D,
      toolUseID: A.id,
      isMcp: !1
    }), yield createMessage({
      content: [{
        type: "tool_result",
        content: `Error: No such tool available: ${ D }`,
        is_error: !0,
        tool_use_id: A.id
      }],
      toolUseResult: `Error: No such tool available: ${ D }`
    });
    return
  }
  let Y  =  A.input;
  try {
    if (I.abortController.signal.aborted) {
      logMetric("tengu_tool_use_cancelled", {
        toolName: Z.name,
        toolUseID: A.id,
        isMcp: Z.isMcp ?? !1
      });
      let W  =  createCancelledResponse(A.id);
      yield createMessage({
        content: [W],
        toolUseResult: CANCELLED_RESULT
      });
      return
    }
    for await (let W of executeToolWithValidation(Z, A.id, Y, I, Q, B, G)) yield W
  } catch (W) {
    if (logError(W instanceof Error ? W : new Error(String(W))), I.options.propagateErrors) throw W;
    yield createMessage({
      content: [{
        type: "tool_result",
        content: "Error calling tool",
        is_error: !0,
        tool_use_id: A.id
      }],
      toolUseResult: "Error calling tool"
    })
  }
}

function Is1(messages, systemPrompt) {
  switch (A) {
    case N4: {
      let {
        command: Q,
        timeout: I,
        sandbox: G,
        description: D
      }  =  N4.inputSchema.parse(B), Z  =  Q.replace(`cd ${ uA() } && `, "");
      if (/^echo\s+["']?[^|&;><]*["']?$/i.test(Z.trim())) logMetric("bash_tool_simple_echo", { });
      return {
        command: Z,
        ...I ? {
          timeout: I
        } : { },
        ...G !== void 0 ? {
          sandbox: G
        } : { },
        ...D ? {
          description: D
        } : { }
      }
    }
    case $Q: {
      let Q  =  $Q.inputSchema.parse(B),
        {
          file_path: I,
          edits: G
        }  =  Qs1({
          file_path: Q.file_path,
          edits: [{
            old_string: Q.old_string,
            new_string: Q.new_string,
            expected_replacements: Q.expected_replacements
          }]
        });
      return {
        expected_replacements: G[0].expected_replacements,
        file_path: I,
        old_string: G[0].old_string,
        new_string: G[0].new_string
      }
    }
    case oV: {
      let Q  =  oV.inputSchema.parse(B);
      return Qs1(Q)
    }
    default:
      return B
  }
}
/**
 * Tool execution with comprehensive validation
 * Validates input schema, checks permissions, and handles errors
 * Tracks execution metrics and performance
 */
async function* executeToolWithValidation(A, B, Q, I, G, D, Z) {
  let Y  =  A.inputSchema.safeParse(Q);
  if (!Y.success) {
    let V  =  formatValidationError(A.name, Y.error);
    logMetric("tengu_tool_use_error", {
      error: "InputValidationError",
      messageID: D.message.id,
      toolName: A.name
    }), yield createMessage({
      content: [{
        type: "tool_result",
        content: `InputValidationError: ${ V }`,
        is_error: !0,
        tool_use_id: B
      }],
      toolUseResult: `InputValidationError: ${ Y.error.message }`
    });
    return
  }
  let W  =  Is1(A, Q),
    F  =  A.inputSchema.safeParse(W);
  if (!F.success) {
    let V  =  formatValidationError(A.name, F.error);
    yield createMessage({
      content: [{
        type: "tool_result",
        content: `InputValidationError: ${ V }`,
        is_error: !0,
        tool_use_id: B
      }],
      toolUseResult: `InputValidationError: ${ F.error.message }`
    });
    return
  }
  let J  =  await A.validateInput?.(F.data, I);
  if (J?.result === !1) {
    logMetric("tengu_tool_use_error", {
      messageID: D.message.id,
      toolName: A.name,
      errorCode: J.errorCode
    }), yield createMessage({
      content: [{
        type: "tool_result",
        content: J.message,
        is_error: !0,
        tool_use_id: B
      }],
      toolUseResult: `Error: ${ J.message }`
    });
    return
  }
  let C  =  Z ? {
    behavior: "allow",
    updatedInput: F.data
  } : await G(A, F.data, I, D);
  if (C.behavior !== "allow") {
    yield createMessage({
      content: [{
        type: "tool_result",
        content: C.message,
        is_error: !0,
        tool_use_id: B
      }],
      toolUseResult: `Error: ${ C.message }`
    });
    return
  }
  let X  =  Date.now();
  try {
    let V  =  A.call(C.updatedInput, I, G, D);
    for await (let K of V) switch (K.type) {
      case "result":
        logMetric("tengu_tool_use_success", {
          messageID: D.message.id,
          toolName: A.name,
          isMcp: A.isMcp ?? !1,
          durationMs: Date.now() - X
        }), yield createMessage({
          content: [A.mapToolResultToToolResultBlockParam(K.data, B)],
          toolUseResult: K.data
        });
        break;
      case "progress":
        logMetric("tengu_tool_use_progress", {
          messageID: D.message.id,
          toolName: A.name,
          isMcp: A.isMcp ?? !1
        }), yield createProgressUpdate({
          toolUseID: K.toolUseID,
          parentToolUseID: B,
          data: K.data
        });
        break
    }
  } catch (V) {
    if (I.options.propagateErrors) throw V;
    if (!(V instanceof CancellationError)) logError(V instanceof Error ? V : new Error(String(V))), logMetric("tengu_tool_use_error", {
      messageID: D.message.id,
      toolName: A.name,
      isMcp: A.isMcp ?? !1
    });
    let K  =  formatErrorMessage(V);
    yield createMessage({
      content: [{
        type: "tool_result",
        content: K,
        is_error: !0,
        tool_use_id: B
      }],
      toolUseResult: `Error: ${ K }`
    })
  }
}

// === ERROR HANDLING ===

function formatErrorMessage(messages) {
  if (A instanceof CancellationError) return CANCELLED_MESSAGE;
  if (!(A instanceof Error)) return String(A);
  let Q  =  extractErrorDetails(A).filter(Boolean).join(`
`).trim() || "Error";
  if (Q.length <= 1e4) return Q;
  let I  =  5000,
    G  =  Q.slice(0, I),
    D  =  Q.slice(-I);
  return `${ G }

... [${ Q.length-1e4 } characters truncated] ...

${ D }`
}

function extractErrorDetails(messages) {
  if (A instanceof ProcessError) return [A.interrupted ? CANCELLED_MESSAGE : "", A.stderr, A.stdout];
  let B  =  [A.message];
  if ("stderr" in A && typeof A.stderr === "string") B.push(A.stderr);
  if ("stdout" in A && typeof A.stdout === "string") B.push(A.stdout);
  return B
}

function formatValidationError(messages, systemPrompt) {
  let Q  =  B.errors.filter((Y)  = > Y.code === "invalid_type" && Y.received === "undefined" && Y.message === "Required")
    .map((Y)  = > String(Y.path[0])),
    I  =  B.errors.filter((Y)  = > Y.code === "unrecognized_keys").flatMap((Y)  = > Y.keys),
    G  =  B.errors.filter((Y)  = > Y.code === "invalid_type" && ("received" in Y) && Y.received !== "undefined" && Y
      .message !== "Required").map((Y)  = > {
      let W  =  Y;
      return {
        param: String(Y.path[0]),
        expected: W.expected,
        received: W.received
      }
    }),
    D  =  B.message,
    Z  =  [];
  if (Q.length > 0) {
    let Y  =  Q.map((W)  = > `The required parameter \`${ W }\` is missing`);
    Z.push(...Y)
  }
  if (I.length > 0) {
    let Y  =  I.map((W)  = > `An unexpected parameter \`${ W }\` was provided`);
    Z.push(...Y)
  }
  if (G.length > 0) {
    let Y  =  G.map(({
      param: W,
      expected: F,
      received: J
    })  = > `The parameter \`${ W }\` type is expected as \`${ F }\` but provided as \`${ J }\``);
    Z.push(...Y)
  }
  if (Z.length > 0) D  =  `${ A } failed due to the following ${ Z.length>1?"issues":"issue" }:
${ Z.join(`
`) }`;
  return D
}
var Ts1  =  J1(_1(), 1);
var dt  =  J1(_1(), 1);

function mt(messages) {
  if (A === "Local") return "project (local)";
  return A.toLowerCase()
}

function lX1(messages) {
  if (A === "Local") return "Project (local) memory";
  return A + " memory"
}
import {
  relative as oF2
} from "path";
import {
  homedir as HA5
} from "os";

function iX1(messages) {
  let B  =  HA5(),
    Q  =  uA(),
    I  =  A.startsWith(B) ? "~/" + oF2(B, A) : null,
    G  =  A.startsWith(Q) ? "./" + oF2(Q, A) : null;
  if (I && G) return I.length <= G.length ? I : G;
  return I || G || A
}

function tF2({
  memoryType: messages,
  memoryPath: B
}) {
  let Q  =  $1(),
    I  =  iX1(B);
  return dt.createElement(m, {
    flexDirection: "column",
    flexGrow: 1
  }, dt.createElement(y, {
    color: Q.secondaryText
  }, lX1(A), " updated in ", I, " · /memory to edit"))
}

function getMemoryUpdatePrompt(messages) {
  return `You have been asked to add a memory or update memories in the memory file at ${ A }.

Please follow these guidelines:
- If the input is an update to an existing memory, edit or replace the existing entry
- Do not elaborate on the memory or add unnecessary commentary
- Preserve the existing structure of the file and integrate new memories naturally. If the file is empty, just add the new memory as a bullet entry, do not add any headings.
- IMPORTANT: Your response MUST be a single tool use for the FileWriteTool`
}
import {
  dirname as zA5
} from "path";
import {
  execFileSync as wA5
} from "child_process";

function readFileContent(messages) {
  if (!b1().existsSync(A)) return "";
  return b1().readFileSync(A, {
    encoding: "utf-8"
  })
}

function AJ2(messages) {
  try {
    wA5("git", ["rev-parse", "--is-inside-work-tree"], {
      cwd: A,
      stdio: "ignore"
    })
  } catch (B) {
    return !1
  }
  return !0
}
async function createLocalMemoryFile(messages) {
  let B  =  zA5(A);
  await P41("CLAUDE.local.md", B)
}

function getMemoryFilePath(messages) {
  let B  =  u4();
  if (A === "ExperimentalUltraClaudeMd") return getMemoryFilePath("User");
  switch (A) {
    case "User":
      return pt(S5, "CLAUDE.md");
    case "Local":
      return pt(B, "CLAUDE.local.md");
    case "Project":
      return pt(B, "CLAUDE.md");
    case "Managed":
      return pt(PM1(), "CLAUDE.md");
    case "ExperimentalUltraClaudeMd":
      return pt(S5, "ULTRACLAUDE.md")
  }
}
var QJ2  =  P31(async function(A, B, Q  =  "User") {
  let I  =  getMemoryFilePath(Q);
  if (Q === "Local" && !b1().existsSync(I)) await createLocalMemoryFile(I);
  B.addNotification?.({
    text: `Saving ${ mt(Q) } memory…`
  }, {
    timeoutMs: 30000
  }), logMetric("tengu_add_memory_start", {
    memory_type: Q
  }), UA5();
  let G  =  readFileContent(I);
  if (!b1().existsSync(BJ2(I))) try {
    b1().mkdirSync(BJ2(I))
  } catch (X) {
    logError(X instanceof Error ? X : new Error(String(X)))
  }
  let D  =  [HD],
    Z  =  createMessage({
      content: `Memory to add/update:
\`\`\`
${ A }
\`\`\`

Existing memory file content:
\`\`\`
${ G||"[empty file]" }
\`\`\``
    }),
    Y  =  await executeWithRetry([Z], [getMemoryUpdatePrompt(I)], 0, D, B.abortController.signal, {
      getToolPermissionContext: B.getToolPermissionContext,
      model: getCurrentModel(),
      prependCLISysprompt: !0,
      toolChoice: {
        name: HD.name,
        type: "tool"
      },
      isNonInteractiveSession: B.options.isNonInteractiveSession
    }),
    W  =  Y.message.content.find((X)  = > X.type === "tool_use");
  if (!W) {
    logError(new Error("No tool use found in response")), B.addNotification?.({
      text: "Failed to save memory: No tool use found in response",
      color: "error"
    });
    return
  }
  let F  =  tQ([await KF(executeSingleTool(W, Y, (X, V)  = > EA5(X, V, I), {
    options: B.options,
    abortController: B.abortController,
    readFileState: {
      [I]: {
        content: b1().existsSync(I) ? dG(I) : "",
        timestamp: b1().existsSync(I) ? b1().statSync(I).mtime.getTime() + 1 : Date.now()
      }
    },
    setToolJSX: B.setToolJSX,
    getToolPermissionContext: B.getToolPermissionContext,
    getQueuedCommands: ()  = > [],
    removeQueuedCommands: ()  = > { }
  }))])[0];
  if (F.type === "user" && F.message.content[0].type === "tool_result" && F.message.content[0].is_error) throw logMetric(
    "tengu_add_memory_failure", { }), new Error(F.message.content[0].content);
  let J  =  readFileContent(I);
  if (B.readFileState[I]  =  {
      content: J,
      timestamp: b1().statSync(I).mtimeMs
    }, logMetric("tengu_add_memory_success", { }), wF({
      filePath: I,
      fileContents: G,
      edits: [{
        old_string: G,
        new_string: J
      }],
      ignoreWhitespace: !0
    }).length > 0) B.addNotification?.({
    jsx: Ts1.createElement(tF2, {
      memoryType: Q,
      memoryPath: I
    })
  }, {
    timeoutMs: 1e4
  });
  else B.addNotification?.({
    text: `No changes made to ${ mt(Q) } memory`
  })
});
async function EA5(messages, systemPrompt, userPrompt) {
  if (A !== HD) return {
    behavior: "ask",
    message: "Used incorrect tool"
  };
  let {
    file_path: I
  }  =  HD.inputSchema.parse(B);
  if (I !== Q) return {
    behavior: "ask",
    message: `Must use correct memory file path: ${ Q }`
  };
  return {
    behavior: "allow",
    updatedInput: B
  }
}

function UA5() {
  let A  =  VA(),
    B  =  (A.memoryUsageCount || 0) + 1;
  T0({
    ...A,
    memoryUsageCount: B
  })
}
var p4  =  J1(_1(), 1),
  j9  =  J1(_1(), 1);
var ct  =  J1(_1(), 1);

function IJ2() {
  let [A, B]  =  ct.useState(0), [Q, I]  =  ct.useState({
    show: !1
  }), G  =  ct.useCallback((D, Z  =  { })  = > {
    let {
      timeoutMs: Y  =  8000
    }  =  Z;
    B((W)  = > {
      let F  =  W + 1;
      return I({
        show: !0,
        content: D
      }), setTimeout(()  = > {
        B((J)  = > {
          if (F === J) I({
            show: !1
          });
          return J
        })
      }, Y), F
    })
  }, []);
  return {
    notification: Q,
    addNotification: G
  }
}
var OX2  =  J1(LX2(), 1);

function os1({
  message: messages,
  title: B
}) {
  let Q  =  B ? `${ B }:
${ A }` : A;
  try {
    process.stdout.write(`\x1B]9;

${ Q }\x07`)
  } catch { }
}

function RX2({
  message: messages,
  title: B
}) {
  try {
    let Q  =  Math.floor(Math.random() * 1e4);
    process.stdout.write(`\x1B]99;i = ${ Q }:d = 0:p = title;${ B||x0 }\x1B\\`), process.stdout.write(
      `\x1B]99;i = ${ Q }:p = body;${ A }\x1B\\`), process.stdout.write(`\x1B]99;i = ${ Q }:d = 1:a = focus;\x1B\\`)
  } catch { }
}

function k05({
  message: messages,
  title: B
}) {
  try {
    let Q  =  B || x0;
    process.stdout.write(`\x1B]777;notify;${ Q };${ A }\x07`)
  } catch { }
}

function ts1() {
  process.stdout.write("\x07")
}
async function x05(messages, systemPrompt) {
  return;
  try {
    let Q  =  A.title || x0,
      I  =  U41(B, uA());
    await I2(I, [Q, A.message])
  } catch (Q) {
    _X(`Error triggering custom notify script: ${ String(Q) }`)
  }
}
async function f05() {
  try {
    if (dA.terminal !== "Apple_Terminal") return !1;
    let B  =  (await I2("osascript", ["-e", 'tell application "Terminal" to name of current settings of front window']))
      .stdout.trim();
    if (!B) return !1;
    let Q  =  await I2("defaults", ["export", "com.apple.Terminal", "-"]);
    if (Q.code !== 0) return !1;
    let D  =  OX2.default.parse(Q.stdout)?.["Window Settings"]?.[B];
    if (!D) return !1;
    return D.Bell === !1
  } catch (A) {
    return logError(A instanceof Error ? A : new Error(String(A))), !1
  }
}
async function Ad(messages) {
  let B  =  VA(),
    Q  =  B.preferredNotifChannel,
    I  =  "none";
  if (B.customNotifyCommand) await x05(A, B.customNotifyCommand);
  switch (Q) {
    case "auto":
      if (dA.terminal === "Apple_Terminal")
        if (await f05()) ts1(), I  =  "terminal_bell";
        else I  =  "no_method_available";
      else if (dA.terminal === "iTerm.app") os1(A), I  =  "iterm2";
      else if (dA.terminal === "kitty") RX2(A), I  =  "kitty";
      else if (dA.terminal  ==  "ghostty") k05(A), I  =  "ghostty";
      else I  =  "no_method_available";
      break;
    case "iterm2":
      os1(A), I  =  "iterm2";
      break;
    case "terminal_bell":
      ts1(), I  =  "terminal_bell";
      break;
    case "iterm2_with_bell":
      os1(A), ts1(), I  =  "iterm2_with_bell";
      break;
    case "kitty":
      RX2(A), I  =  "kitty";
      break;
    case "notifications_disabled":
      I  =  "disabled";
      break
  }
  await logMetric("notification_method_used", {
    configured_channel: Q,
    method_used: I,
    term: dA.terminal
  })
}
var TX2  =  J1(_1(), 1);

function PX2(messages, B  =  !1) {
  TX2.useEffect(()  = > {
    if (!B) y41(A)
  }, [A, B])
}
var $4  =  J1(_1(), 1),
  hR  =  J1(_1(), 1);
var C3  =  J1(_1(), 1);
var yN  =  J1(_1(), 1);
var Ge  =  J1(_1(), 1);

function SX2() {
  return Ge.createElement(y, null, "  ⎿  ", Ge.createElement(y, {
    color: $1().error
  }, "Interrupted by user"))
}
var e_  =  J1(_1(), 1);
var es1  =  J1(_1(), 1);

function Bd() {
  return es1.createElement(y, {
    color: $1().error
  }, "Interrupted by user")
}

function _X2({
  progressMessagesForMessage: messages,
  tool: systemPrompt,
  tools: userPrompt,
  param: tools,
  verbose: G
}) {
  if (typeof I.content === "string" && I.content.startsWith(CANCELLED_MESSAGE)) return e_.createElement(r0, {
    height: 1
  }, e_.createElement(Bd, null));
  if (!B) return e_.createElement(q6, {
    result: I.content,
    verbose: G
  });
  return B.renderToolUseErrorMessage(I.content, {
    progressMessagesForMessage: A,
    tools: Q,
    verbose: G
  })
}
var De  =  J1(_1(), 1);

function jX2({
  input: messages,
  progressMessagesForMessage: systemPrompt,
  tool: userPrompt,
  tools: tools,
  messages: signal,
  verbose: D
}) {
  let {
    columns: Z
  }  =  D4();
  if (!Q) return De.createElement(I5, null);
  let Y  =  Q.inputSchema.safeParse(A);
  if (!Y.success) return De.createElement(I5, null);
  return Q.renderToolUseRejectedMessage(Y.data, {
    columns: Z,
    messages: G,
    tools: I,
    verbose: D,
    progressMessagesForMessage: B
  })
}
var Ar1  =  J1(_1(), 1);

function yX2({
  message: messages,
  progressMessagesForMessage: systemPrompt,
  tool: userPrompt,
  tools: tools,
  verbose: signal,
  width: D
}) {
  if (!A.toolUseResult || !Q) return null;
  return Ar1.createElement(m, {
    flexDirection: "column",
    width: D
  }, Q.renderToolResultMessage?.(A.toolUseResult, B, {
    tools: I,
    verbose: G
  }))
}
var v05  =  J1(_1(), 1);
var kX2  =  J1(_1(), 1);

function b05(messages, systemPrompt) {
  let Q  =  null;
  for (let I of B) {
    if (I.type !== "assistant" || !Array.isArray(I.message.content)) continue;
    for (let G of I.message.content)
      if (G.type === "tool_use" && G.id === A) Q  =  G
  }
  return Q
}

function xX2(messages, systemPrompt, userPrompt) {
  return kX2.useMemo(()  = > {
    let I  =  b05(A, Q);
    if (!I) return null;
    let G  =  B.find((D)  = > D.name === I.name);
    if (!G) return null;
    return {
      tool: G,
      toolUse: I
    }
  }, [A, Q, B])
}

function fX2({
  param: messages,
  message: systemPrompt,
  messages: userPrompt,
  progressMessagesForMessage: tools,
  tools: signal,
  verbose: options,
  width: Z
}) {
  let Y  =  xX2(A.tool_use_id, G, Q);
  if (!Y) return null;
  if (A.content === CANCELLED_RESULT) return yN.createElement(SX2, null);
  if (A.content === dr || A.content === CANCELLED_MESSAGE) return yN.createElement(jX2, {
    input: Y.toolUse.input,
    progressMessagesForMessage: I,
    tool: Y.tool,
    tools: G,
    messages: Q,
    verbose: D
  });
  if (A.is_error) return yN.createElement(_X2, {
    progressMessagesForMessage: I,
    tool: Y.tool,
    tools: G,
    param: A,
    verbose: D
  });
  return yN.createElement(yX2, {
    message: B,
    progressMessagesForMessage: I,
    tool: Y.tool,
    tools: G,
    verbose: D,
    width: Z
  })
}
var UF  =  J1(_1(), 1);
var Ze  =  J1(_1(), 1);

function wV1({
  costUSD: messages,
  durationMs: systemPrompt,
  verbose: Q
}) {
  if (!Q) return null;
  let I  =  (B / 1000).toFixed(1);
  return Ze.createElement(m, {
    flexDirection: "column",
    alignItems: "flex-end",
    minWidth: 23,
    width: 23
  }, Ze.createElement(y, {
    dimColor: !0
  }, "Cost: $", A.toFixed(4), " (", I, "s)"))
}
var EV1  =  J1(_1(), 1);
var NZ  =  J1(_1(), 1),
  Ir1  =  J1(hX2(), 1);
var B25  =  typeof window !== "undefined" ? NZ.useLayoutEffect : NZ.useEffect;

function SC(messages, systemPrompt) {
  let Q  =  NZ.useRef(A);
  B25(()  = > {
    Q.current  =  A
  }, [A]), NZ.useEffect(()  = > {
    if (B === null) return;
    let I  =  setInterval(()  = > {
      Q.current()
    }, B);
    return ()  = > {
      clearInterval(I)
    }
  }, [B])
}

function Q25(messages) {
  let B  =  NZ.useRef(A);
  B.current  =  A, NZ.useEffect(()  = > ()  = > {
    B.current()
  }, [])
}

function mX2(messages, B  =  500, userPrompt) {
  let I  =  NZ.useRef();
  Q25(()  = > {
    if (I.current) I.current.cancel()
  });
  let G  =  NZ.useMemo(()  = > {
    let D  =  Ir1.default(A, B, Q),
      Z  =  (...Y)  = > {
        return D(...Y)
      };
    return Z.cancel  =  ()  = > {
      D.cancel()
    }, Z.isPending  =  ()  = > {
      return !!I.current
    }, Z.flush  =  ()  = > {
      return D.flush()
    }, Z
  }, [A, B, Q]);
  return NZ.useEffect(()  = > {
    I.current  =  Ir1.default(A, B, Q)
  }, [A, B, Q]), G
}
var Qd  =  dA.platform === "macos" ? "⏺" : "●";

function dX2({
  isError: messages,
  isUnresolved: systemPrompt,
  shouldAnimate: Q
}) {
  let [I, G]  =  EV1.default.useState(!0);
  SC(()  = > {
    if (!Q) return;
    G((Z)  = > !Z)
  }, 600);
  let D  =  B ? $1().secondaryText : A ? $1().error : $1().success;
  return EV1.default.createElement(m, {
    minWidth: 2
  }, EV1.default.createElement(y, {
    color: D
  }, I ? Qd : "  "))
}

function uX2({
  param: messages,
  costUSD: systemPrompt,
  durationMs: userPrompt,
  addMargin: tools,
  tools: signal,
  verbose: options,
  erroredToolUseIDs: temperature,
  inProgressToolUseIDs: response,
  unresolvedToolUseIDs: content,
  progressMessagesForMessage: enableCaching,
  shouldAnimate: toolChoice,
  shouldShowDot: C
}) {
  let X  =  G.find((R)  = > R.name === A.name);
  if (!X) return logError(new Error(`Tool ${ A.name } not found`)), null;
  let V  =  W.has(A.id),
    K  =  !Y.has(A.id) && V,
    U  =  K ? $1().secondaryText : void 0,
    N  =  X.inputSchema.safeParse(A.input),
    q  =  X.userFacingName(N.success ? N.data : void 0),
    M  =  N.success ? I25(X, N.data, {
      verbose: D
    }) : null;
  return UF.default.createElement(m, {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: I ? 1 : 0,
    width: "100%"
  }, UF.default.createElement(m, {
    flexDirection: "column"
  }, UF.default.createElement(m, {
    flexDirection: "row",
    flexWrap: "nowrap",
    minWidth: q.length + (C ? 2 : 0)
  }, C && (K ? UF.default.createElement(m, {
    minWidth: 2
  }, UF.default.createElement(y, {
    color: U
  }, Qd)) : UF.default.createElement(dX2, {
    shouldAnimate: J,
    isUnresolved: V,
    isError: Z.has(A.id)
  })), UF.default.createElement(m, {
    flexShrink: 0
  }, UF.default.createElement(y, {
    color: U,
    bold: !K,
    wrap: "truncate-end"
  }, q)), M  !=  null && UF.default.createElement(m, {
    flexWrap: "nowrap"
  }, UF.default.createElement(y, {
    color: U
  }, "(", M, ")"), UF.default.createElement(y, {
    color: U
  }, "…"))), V && !K && G25(X, G, F, {
    verbose: D
  }), V && K && D25(X)), UF.default.createElement(wV1, {
    costUSD: B,
    durationMs: Q,
    verbose: D
  }))
}

function I25(messages, systemPrompt, {
  verbose: Q
}) {
  try {
    let I  =  A.inputSchema.safeParse(B);
    if (!I.success) return null;
    return A.renderToolUseMessage(I.data, {
      verbose: Q
    })
  } catch (I) {
    return logError(new Error(`Error rendering tool use message for ${ A.name }: ${ I }`)), null
  }
}

function G25(messages, systemPrompt, userPrompt, {
  verbose: I
}) {
  try {
    return A.renderToolUseProgressMessage(Q, {
      tools: B,
      verbose: I
    })
  } catch (G) {
    return logError(new Error(`Error rendering tool use progress message for ${ A.name }: ${ G }`)), null
  }
}

function D25(messages) {
  try {
    return A.renderToolUseQueuedMessage?.()
  } catch (B) {
    return logError(new Error(`Error rendering tool use queued message for ${ A.name }: ${ B }`)), null
  }
}
var t5  =  J1(_1(), 1);
var Ye  =  J1(du1(), 1);
import {
  EOL as eV
} from "os";

function Id(messages) {
  return h5.lexer(cr(A)).map((B)  = > AK(B)).join("").trim()
}

function AK(messages, B  =  0, Q  =  null, I  =  null) {
  switch (A.type) {
    case "blockquote":
      return wA.dim.italic((A.tokens ?? []).map((G)  = > AK(G)).join(""));
    case "code":
      if (A.lang && Ye.supportsLanguage(A.lang)) return Ye.highlight(A.text, {
        language: A.lang
      }) + eV;
      else return logError(new Error(`Language not supported while highlighting code, falling back to markdown: ${ A.lang }`)),
        Ye.highlight(A.text, {
          language: "markdown"
        }) + eV;
    case "codespan":
      return wA.ansi256(l9().permission)(A.text);
    case "em":
      return wA.italic((A.tokens ?? []).map((G)  = > AK(G)).join(""));
    case "strong":
      return wA.bold((A.tokens ?? []).map((G)  = > AK(G)).join(""));
    case "heading":
      switch (A.depth) {
        case 1:
          return wA.bold.italic.underline((A.tokens ?? []).map((G)  = > AK(G)).join("")) + eV + eV;
        case 2:
          return wA.bold((A.tokens ?? []).map((G)  = > AK(G)).join("")) + eV + eV;
        default:
          return wA.bold.dim((A.tokens ?? []).map((G)  = > AK(G)).join("")) + eV + eV
      }
    case "hr":
      return "---";
    case "image":
      return `[Image: ${ A.title }: ${ A.href }]`;
    case "link":
      return wA.ansi256(l9().permission)(A.href);
    case "list":
      return A.items.map((G, D)  = > AK(G, B, A.ordered ? A.start + D : null, A)).join("");
    case "list_item":
      return (A.tokens ?? []).map((G)  = > `${ "  ".repeat(B) }${ AK(G, B+1, Q, A) }`).join("");
    case "paragraph":
      return (A.tokens ?? []).map((G)  = > AK(G)).join("") + eV;
    case "space":
      return eV;
    case "text":
      if (I?.type === "list_item")
      return `${ Q===null?"-":W25(B, Q)+"." } ${ A.tokens?A.tokens.map((G) = >AK(G, B, Q, A)).join(""):A.text }${ eV }`;
      else return A.text
  }
  return ""
}
var Z25  =  ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
    "w", "x", "y", "z", "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap",
    "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az"
  ],
  Y25  =  ["i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x", "xi", "xii", "xiii", "xiv", "xv", "xvi", "xvii",
    "xviii", "xix", "xx", "xxi", "xxii", "xxiii", "xxiv", "xxv", "xxvi", "xxvii", "xxviii", "xxix", "xxx", "xxxi",
    "xxxii", "xxxiii", "xxxiv", "xxxv", "xxxvi", "xxxvii", "xxxviii", "xxxix", "xl"
  ];

function W25(messages, systemPrompt) {
  switch (A) {
    case 0:
    case 1:
      return B.toString();
    case 2:
      return Z25[B - 1];
    case 3:
      return Y25[B - 1];
    default:
      return B.toString()
  }
}

function pX2({
  param: {
    text: A
  },
  costUSD: systemPrompt,
  durationMs: userPrompt,
  addMargin: tools,
  shouldShowDot: signal,
  verbose: D
}) {
  let {
    columns: Z
  }  =  D4();
  if (hF1(A)) return null;
  if (A.startsWith(REFUSAL_PREFIX)) return t5.default.createElement(r0, null, t5.default.createElement(y, {
    color: $1().error
  }, A === REFUSAL_PREFIX ? `${ REFUSAL_PREFIX }: Please wait a moment and try again.` : A));
  if (A.startsWith(ia1)) {
    let Y  =  Number(A.split("|")[1] ?? 0),
      W  =  Tv(Y);
    return t5.default.createElement(r0, null, t5.default.createElement(m, {
      flexDirection: "column",
      gap: 1
    }, t5.default.createElement(y, {
      color: $1().error
    }, "Claude Max usage limit reached.", Y ? ` Your limit will reset at ${ W }.` : ""), t5.default.createElement(y,
      null,
      "To continue immediately, upgrade to a higher plan https://claude.ai/upgrade/max or switch to a Console Account for credit based billing with higher limits",
      " ", t5.default.createElement(y, {
        dimColor: !0
      }, "• /login"))))
  }
  switch (A) {
    case ur:
    case CANCELLED_MESSAGE:
      return null;
    case DR:
    case CANCELLED_RESULT:
      return t5.default.createElement(r0, {
        height: 1
      }, t5.default.createElement(Bd, null));
    case Et:
      return t5.default.createElement(r0, {
        height: 1
      }, t5.default.createElement(y, {
        color: $1().error
      }, "Context low · Run /compact to compact & continue"));
    case la1:
      return t5.default.createElement(r0, {
        height: 1
      }, t5.default.createElement(y, {
        color: $1().error
      }, "Credit balance too low · Add funds: https://console.anthropic.com/settings/billing"));
    case $X1:
      return t5.default.createElement(r0, {
        height: 1
      }, t5.default.createElement(y, {
        color: $1().error
      }, $X1));
    case qX1:
      return t5.default.createElement(r0, {
        height: 1
      }, t5.default.createElement(y, {
        color: $1().error
      }, qX1));
    case na1:
    case OFF_SWITCH_ERROR_MESSAGE:
      return t5.default.createElement(r0, null, t5.default.createElement(m, {
        flexDirection: "column",
        gap: 1
      }, t5.default.createElement(y, {
        color: $1().error
      }, "We are experiencing high demand for Claude Opus 4."), t5.default.createElement(y, null,
        "To continue immediately, use /model to switch to", " ", Ui(kP()), "and continue coding.")));
    default:
      return t5.default.createElement(m, {
        alignItems: "flex-start",
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: I ? 1 : 0,
        width: "100%"
      }, t5.default.createElement(m, {
        flexDirection: "row"
      }, G && t5.default.createElement(m, {
        minWidth: 2
      }, t5.default.createElement(y, {
        color: $1().text
      }, Qd)), t5.default.createElement(m, {
        flexDirection: "column",
        width: Z - 6
      }, t5.default.createElement(y, null, Id(A)))), t5.default.createElement(wV1, {
        costUSD: B,
        durationMs: Q,
        verbose: D === !0
      }))
  }
}
var kN  =  J1(_1(), 1);

function UV1({
  param: {
    text: A
  },
  addMargin: B
}) {
  let Q  =  XG(A, "bash-input");
  if (!Q) return null;
  return kN.createElement(m, {
    flexDirection: "column",
    marginTop: B ? 1 : 0,
    width: "100%"
  }, kN.createElement(m, null, kN.createElement(y, {
    color: $1().bashBorder
  }, "!"), kN.createElement(y, {
    color: $1().secondaryText
  }, " ", Q)))
}
var We  =  J1(_1(), 1);

function cX2({
  addMargin: messages,
  param: {
    text: B
  }
}) {
  let Q  =  XG(B, "command-message"),
    I  =  XG(B, "command-args");
  if (!Q) return null;
  let G  =  $1();
  return We.createElement(m, {
    flexDirection: "column",
    marginTop: A ? 1 : 0,
    width: "100%"
  }, We.createElement(y, {
    color: G.secondaryText
  }, "> /", Q, " ", I))
}
var Gd  =  J1(_1(), 1);

function lX2({
  addMargin: messages,
  param: {
    text: B
  }
}) {
  let {
    columns: Q
  }  =  D4();
  if (!B) return logError(new Error("No content found in user prompt message")), null;
  return Gd.default.createElement(m, {
    flexDirection: "row",
    marginTop: A ? 1 : 0,
    width: "100%"
  }, Gd.default.createElement(m, {
    minWidth: 2,
    width: 2
  }, Gd.default.createElement(y, {
    color: $1().secondaryText
  }, ">")), Gd.default.createElement(m, {
    flexDirection: "column",
    width: Q - 4
  }, Gd.default.createElement(y, {
    color: $1().secondaryText,
    wrap: "wrap"
  }, B.trim())))
}
var VG  =  J1(_1(), 1);
var NF  =  J1(_1(), 1);
var iX2  =  J1(Vk1(), 1);

function F25() {
  return iX2.sample(["Got it.", "Good to know.", "Noted."])
}

function nX2({
  param: {
    text: A
  },
  addMargin: B
}) {
  let Q  =  XG(A, "user-memory-input");
  if (!Q) return null;
  return NF.createElement(m, {
    flexDirection: "column",
    marginTop: B ? 1 : 0,
    width: "100%"
  }, NF.createElement(m, null, NF.createElement(y, {
    color: $1().remember
  }, "#"), NF.createElement(y, {
    color: $1().remember
  }, " ", Q)), NF.createElement(r0, {
    height: 1
  }, NF.createElement(y, {
    dimColor: !0
  }, F25())))
}
var Gr1  =  J1(_1(), 1);

function aX2({
  content: messages,
  verbose: B
}) {
  let Q  =  XG(A, "bash-stdout") ?? "",
    I  =  XG(A, "bash-stderr") ?? "";
  return Gr1.createElement(St, {
    content: {
      stdout: Q,
      stderr: I
    },
    verbose: !!B
  })
}
var $F  =  J1(_1(), 1);

function sX2({
  content: A
}) {
  let B  =  XG(A, "local-command-stdout"),
    Q  =  XG(A, "local-command-stderr");
  if (!B && !Q) return $F.createElement(r0, null, $F.createElement(y, {
    color: $1().secondaryText
  }, jY));
  let I  =  [];
  if (B?.trim()) I.push($F.createElement(r0, {
    key: "stdout"
  }, $F.createElement(y, {
    color: $1().text
  }, B.trim())));
  if (Q?.trim()) I.push($F.createElement(r0, {
    key: "stderr"
  }, $F.createElement(y, {
    color: $1().error
  }, Q.trim())));
  return I
}

function NV1({
  addMargin: messages,
  param: systemPrompt,
  verbose: Q
}) {
  if (B.text.trim() === jY) return null;
  if (B.text.startsWith("<bash-stdout") || B.text.startsWith("<bash-stderr")) return VG.createElement(aX2, {
    content: B.text,
    verbose: Q
  });
  if (B.text.startsWith("<local-command-stdout") || B.text.startsWith("<local-command-stderr")) return VG.createElement(
    sX2, {
      content: B.text
    });
  if (B.text === DR || B.text === CANCELLED_MESSAGE) return VG.createElement(r0, {
    height: 1
  }, VG.createElement(Bd, null));
  if (B.text.includes("<bash-input>")) return VG.createElement(UV1, {
    addMargin: A,
    param: B
  });
  if (B.text.includes("<command-name>") || B.text.includes("<command-message>")) return VG.createElement(cX2, {
    addMargin: A,
    param: B
  });
  if (B.text.includes("<user-memory-input>")) return VG.createElement(nX2, {
    addMargin: A,
    param: B
  });
  return VG.createElement(lX2, {
    addMargin: A,
    param: B
  })
}
var Fe  =  J1(_1(), 1);

function rX2({
  param: {
    thinking: A
  },
  addMargin: B  =  !1
}) {
  if (!A) return null;
  return Fe.default.createElement(m, {
    flexDirection: "column",
    gap: 1,
    marginTop: B ? 1 : 0,
    width: "100%"
  }, Fe.default.createElement(y, {
    color: $1().secondaryText,
    italic: !0
  }, "✻ Thinking…"), Fe.default.createElement(m, {
    paddingLeft: 2
  }, Fe.default.createElement(y, {
    color: $1().secondaryText,
    italic: !0
  }, Id(A))))
}
var Dr1  =  J1(_1(), 1);

function oX2({
  addMargin: A  =  !1
}) {
  return Dr1.default.createElement(m, {
    marginTop: A ? 1 : 0
  }, Dr1.default.createElement(y, {
    color: $1().secondaryText,
    italic: !0
  }, "✻ Thinking…"))
}
var xN  =  J1(_1(), 1);
import {
  relative as $V1,
  sep as C25
} from "path";
var qw  =  J1(_1(), 1);
import {
  relative as J25
} from "path";

function tX2({
  attachment: messages,
  verbose: B
}) {
  if (A.files.length === 0) return null;
  let Q  =  A.files.reduce((G, D)  = > G + D.diagnostics.length, 0),
    I  =  A.files.length;
  if (B) return qw.default.createElement(m, {
    flexDirection: "column"
  }, A.files.map((G, D)  = > qw.default.createElement(qw.default.Fragment, {
    key: D
  }, qw.default.createElement(r0, null, qw.default.createElement(y, {
    color: $1().secondaryText,
    wrap: "wrap"
  }, wA.bold(J25(uA(), G.uri.replace("file://", "").replace("_claude_fs_right:", ""))), " ", wA.dim(G.uri
    .startsWith("file://") ? "(file://)" : G.uri.startsWith("_claude_fs_right:") ? "(claude_fs_right)" :
    `(${ G.uri.split(":")[0] })`), ":")), G.diagnostics.map((Z, Y)  = > qw.default.createElement(r0, {
    key: Y
  }, qw.default.createElement(y, {
      color: $1().secondaryText,
      wrap: "wrap"
    }, "  ", xV.getSeveritySymbol(Z.severity), " [Line ", Z.range.start.line + 1, ":", Z.range.start
    .character + 1, "] ", Z.message, Z.code ? ` [${ Z.code }]` : "", Z.source ? ` (${ Z.source })` : ""))))));
  else return qw.default.createElement(r0, null, qw.default.createElement(y, {
      color: $1().secondaryText,
      wrap: "wrap"
    },
    `Found ${ wA.bold(Q) } new diagnostic ${ Q===1?"issue":"issues" } in ${ I } ${ I===1?"file":"files" } (ctrl-r to expand)`
    ))
}

function eX2({
  attachment: messages,
  addMargin: systemPrompt,
  verbose: Q
}) {
  switch (A.type) {
    case "new_directory":
      return xN.default.createElement(qV1, {
        text: `Listed directory ${ wA.bold($V1(uA(), A.path)+C25) }`
      });
    case "new_file":
      return xN.default.createElement(qV1, {
        text: `Read ${ wA.bold($V1(uA(), A.filename)) } (${ A.content.type==="text"?`${ A.content.file.numLines }${ A.truncated?"+":"" } lines`:`${ wA.bold($x(A.content.file.originalSize)) }` })`
      });
    case "edited_text_file":
    case "edited_image_file":
      return null;
    case "selected_lines_in_ide":
      return xN.default.createElement(qV1, {
        text: `⧉ Selected ${ wA.bold(A.content.split(`
`).length) } lines from ${ wA.bold($V1(uA(), A.filename)) } in ${ A.ideName }`
      });
    case "nested_memory":
      return xN.default.createElement(qV1, {
        text: wA.bold($V1(uA(), A.path))
      });
    case "queued_command":
      return xN.default.createElement(NV1, {
        addMargin: B,
        param: {
          text: A.prompt,
          type: "text"
        },
        verbose: Q
      });
    case "opened_file_in_ide":
    case "todo":
    case "ultramemory":
      return null;
    case "diagnostics":
      return xN.default.createElement(tX2, {
        attachment: A,
        verbose: Q
      })
  }
}

function qV1({
  text: A
}) {
  return xN.default.createElement(r0, null, xN.default.createElement(y, {
    color: $1().secondaryText,
    wrap: "wrap"
  }, A.trim()))
}

function BK({
  message: messages,
  messages: systemPrompt,
  addMargin: userPrompt,
  tools: tools,
  verbose: signal,
  erroredToolUseIDs: options,
  inProgressToolUseIDs: temperature,
  unresolvedToolUseIDs: response,
  progressMessagesForMessage: content,
  shouldAnimate: enableCaching,
  shouldShowDot: toolChoice,
  width: C
}) {
  switch (A.type) {
    case "attachment":
      return C3.createElement(eX2, {
        addMargin: Q,
        attachment: A.attachment,
        verbose: G
      });
    case "assistant":
      return C3.createElement(m, {
        flexDirection: "column",
        width: "100%"
      }, A.message.content.map((X, V)  = > C3.createElement(V25, {
        key: V,
        param: X,
        costUSD: A.costUSD,
        durationMs: A.durationMs,
        addMargin: Q,
        tools: I,
        verbose: G,
        erroredToolUseIDs: D,
        inProgressToolUseIDs: Z,
        unresolvedToolUseIDs: Y,
        progressMessagesForMessage: W,
        shouldAnimate: F,
        shouldShowDot: J,
        width: C
      })));
    case "user":
      return C3.createElement(m, {
        flexDirection: "column",
        width: "100%"
      }, A.message.content.map((X, V)  = > C3.createElement(X25, {
        key: V,
        message: A,
        messages: B,
        addMargin: Q,
        tools: I,
        progressMessagesForMessage: W,
        param: X,
        verbose: G
      })))
  }
}

function X25({
  message: messages,
  messages: systemPrompt,
  addMargin: userPrompt,
  tools: tools,
  progressMessagesForMessage: signal,
  param: options,
  verbose: Z
}) {
  let {
    columns: Y
  }  =  D4();
  switch (D.type) {
    case "text":
      return C3.createElement(NV1, {
        addMargin: Q,
        param: D,
        verbose: Z
      });
    case "tool_result":
      return C3.createElement(fX2, {
        param: D,
        message: A,
        messages: B,
        progressMessagesForMessage: G,
        tools: I,
        verbose: Z,
        width: Y - 5
      });
    default:
      return
  }
}

function V25({
  param: messages,
  costUSD: systemPrompt,
  durationMs: userPrompt,
  addMargin: tools,
  tools: signal,
  verbose: options,
  erroredToolUseIDs: temperature,
  inProgressToolUseIDs: response,
  unresolvedToolUseIDs: content,
  progressMessagesForMessage: enableCaching,
  shouldAnimate: toolChoice,
  shouldShowDot: processedMessages,
  width: X
}) {
  switch (A.type) {
    case "tool_use":
      return C3.createElement(uX2, {
        param: A,
        costUSD: B,
        durationMs: Q,
        addMargin: I,
        tools: G,
        verbose: D,
        erroredToolUseIDs: Z,
        inProgressToolUseIDs: Y,
        unresolvedToolUseIDs: W,
        progressMessagesForMessage: F,
        shouldAnimate: J,
        shouldShowDot: C
      });
    case "text":
      return C3.createElement(pX2, {
        param: A,
        costUSD: B,
        durationMs: Q,
        addMargin: I,
        shouldShowDot: C,
        verbose: D,
        width: X
      });
    case "redacted_thinking":
      return C3.createElement(oX2, {
        addMargin: I
      });
    case "thinking":
      return C3.createElement(rX2, {
        addMargin: I,
        param: A
      });
    default:
      return logError(new Error(`Unable to render message type: ${ A.type }`)), null
  }
}
import {
  randomUUID as K25
} from "crypto";
var MV1  =  7;

function AV2({
  erroredToolUseIDs: messages,
  messages: systemPrompt,
  onSelect: userPrompt,
  onEscape: tools,
  tools: signal,
  unresolvedToolUseIDs: D
}) {
  let Z  =  hR.useMemo(K25, []);
  hR.useEffect(()  = > {
    logMetric("tengu_message_selector_opened", { })
  }, []);

  function Y(streamResponse) {
    let N  =  B.length - 1 - B.indexOf(U);
    logMetric("tengu_message_selector_selected", {
      index_from_end: N,
      message_type: U.type,
      is_current_prompt: U.uuid === Z
    }), Q(U)
  }

  function W() {
    logMetric("tengu_message_selector_cancelled", { }), I()
  }
  let F  =  hR.useMemo(()  = > [...B.filter(H25), {
      ...createMessage({
        content: ""
      }),
      uuid: Z
    }], [B, Z]),
    [J, C]  =  hR.useState(F.length - 1),
    X  =  v2();
  J0((U, N)  = > {
    if (N.tab || N.escape) {
      W();
      return
    }
    if (N.return) {
      Y(F[J]);
      return
    }
    if (N.upArrow)
      if (N.ctrl || N.shift || N.meta) C(0);
      else C((q)  = > Math.max(0, q - 1));
    if (N.downArrow)
      if (N.ctrl || N.shift || N.meta) C(F.length - 1);
      else C((q)  = > Math.min(F.length - 1, q + 1))
  });
  let V  =  Math.max(0, Math.min(J - Math.floor(MV1 / 2), F.length - MV1)),
    K  =  hR.useMemo(()  = > tQ(B).filter(Z_), [B]);
  return $4.createElement($4.Fragment, null, $4.createElement(m, {
    flexDirection: "column",
    borderStyle: "round",
    borderColor: $1().secondaryBorder,
    height: 4 + Math.min(MV1, F.length) * 2,
    paddingX: 1,
    marginTop: 1
  }, $4.createElement(m, {
    flexDirection: "column",
    minHeight: 2,
    marginBottom: 1
  }, $4.createElement(y, {
    bold: !0
  }, "Jump to a previous message"), $4.createElement(y, {
    dimColor: !0
  }, "This will fork the conversation")), F.slice(V, V + MV1).map((U, N)  = > {
    let M  =  V + N === J,
      R  =  U.uuid === Z;
    return $4.createElement(m, {
        key: U.uuid,
        flexDirection: "row",
        height: 2,
        minHeight: 2
      }, $4.createElement(m, {
        width: 7
      }, M ? $4.createElement(y, {
        color: "blue",
        bold: !0
      }, H2.pointer, " ", V + N + 1, " ") : $4.createElement(y, null, "  ", V + N + 1, " ")), $4
      .createElement(m, {
        height: 1,
        overflow: "hidden",
        width: 100
      }, R ? $4.createElement(m, {
        width: "100%"
      }, $4.createElement(y, {
        dimColor: !0,
        italic: !0
      }, "(current)")) : Array.isArray(U.message.content) && U.message.content[0]?.type === "text" && hF1(U
        .message.content[0].text) ? $4.createElement(y, {
        dimColor: !0,
        italic: !0
      }, "(empty message)") : $4.createElement(BK, {
        message: AY(tQ([U])),
        messages: K,
        addMargin: !1,
        tools: G,
        verbose: !1,
        erroredToolUseIDs: A,
        inProgressToolUseIDs: new Set,
        unresolvedToolUseIDs: D,
        shouldAnimate: !1,
        shouldShowDot: !1,
        progressMessagesForMessage: []
      })))
  })), $4.createElement(m, {
    marginLeft: 3
  }, $4.createElement(y, {
    dimColor: !0
  }, X.pending ? $4.createElement($4.Fragment, null, "Press ", X.keyName, " again to exit") : $4.createElement(
    $4.Fragment, null, "↑/↓ to select · Enter to confirm · Tab/Esc to cancel"))))
}

function H25(messages) {
  if (A.type !== "user") return !1;
  if (Array.isArray(A.message.content) && A.message.content[0]?.type === "tool_result") return !1;
  if (fF1(A)) return !1;
  if (A.isMeta) return !1;
  return !0
}
var Dd  =  J1(_1(), 1);

function BV2(messages, systemPrompt) {
  let [Q, I]  =  Dd.useState(()  = > {
    let W  =  KV(),
      F  =  B ? {
        ...W,
        ...B
      } : W;
    return Object.entries(F).map(([J, C])  = > ({
      name: J,
      type: "pending",
      config: C
    }))
  }), [G, D]  =  Dd.useState([]), [Z, Y]  =  Dd.useState([]);
  return Dd.useEffect(()  = > {
    I((F)  = > {
      let J  =  KV(),
        C  =  F.filter((X)  = > J[X.name] || B?.[X.name]);
      if (B) Object.entries(B).forEach(([X, V])  = > {
        if (!C.find((K)  = > K.name === X)) C.push({
          name: X,
          type: "pending",
          config: V
        })
      });
      return C
    }), D([]), Y([]);
    let W  =  0;
    Zu1(({
      client: F,
      tools: J,
      commands: C
    })  = > {
      try {
        if (F.type === "failed") {
          if (F.config.type !== "sse-ide" && F.config.type !== "ws-ide") W++;
          if (W > 0) {
            let V  =  {
              text: `${ W } MCP server${ W>1?"s":"" } failed to connect (see /mcp for info)`,
              color: "error"
            };
            A(V, {
              timeoutMs: 1e4
            })
          }
        }
        if (F.type === "connected" && F.client.transport) {
          let K  =  function(N) {
              if (!V) return;
              V  =  !1, GY(F.name, N), I((q)  = > q.map((M)  = > M.name !== F.name ? M : {
                name: M.name,
                type: "failed",
                config: M.config
              })), D((q)  = > q.filter((M)  = > !J.includes(M))), Y((q)  = > q.filter((M)  = > !C.includes(M)))
            },
            V  =  !0;
          F.client.transport.onclose  =  ()  = > {
            K("transport closed")
          }, F.client.transport.onerror  =  (N)  = > {
            K(N)
          };
          let U  =  F.client.transport.onmessage;
          F.client.transport.onmessage  =  (...N)  = > {
            if (U) U.apply(F.client.transport, N);
            if (V) return;
            V  =  !0, I((q)  = > q.map((M)  = > M.name !== F.name ? M : {
              ...F,
              type: "connected"
            })), D((q)  = > {
              return [...q.filter((M)  = > !J.includes(M)), ...J]
            }), Y((q)  = > {
              return [...q.filter((M)  = > !C.includes(M)), ...C]
            })
          }
        }
        I((V)  = > V.map((K)  = > K.name === F.name ? F : K)), D((V)  = > [...V, ...J]), Y((V)  = > [...V, ...C])
      } catch (X) {
        console.error("Error handling MCP update", X)
      }
    }, B)
  }, [A, B]), {
    clients: Q,
    tools: G,
    commands: Z
  }
}
var QV2  =  J1(_1(), 1);
var z25  =  i.object({
  method: i.literal("log_event"),
  params: i.object({
    eventName: i.string(),
    eventData: i.object({ }).passthrough()
  })
});

function IV2(messages) {
  QV2.useEffect(()  = > {
    if (!A.length) return;
    let B  =  A.filter((Q)  = > Q.type === "connected" && Q.name === "ide");
    for (let Q of B) Q.client.setNotificationHandler(z25, async (I)  = > {
      let {
        eventName: G,
        eventData: D
      }  =  I.params;
      logMetric(`tengu_ide_${ G }`, D)
    })
  }, [A])
}
var vo1  =  J1(_1(), 1);
var QK  =  J1(_1(), 1);
import {
  basename as O25
} from "path";
var GV2  =  J1(_1(), 1);

function m5(messages) {
  logMetric("tengu_unary_event", {
    event: A.event,
    completion_type: A.completion_type,
    language_name: A.metadata.language_name,
    message_id: A.metadata.message_id,
    platform: A.metadata.platform
  })
}

function _C(messages, systemPrompt) {
  GV2.useEffect(()  = > {
    logMetric("tengu_tool_use_show_permission_request", {
      messageID: A.assistantMessage.message.id,
      toolName: A.tool.name
    }), m5({
      completion_type: B.completion_type,
      event: "response",
      metadata: {
        language_name: B.language_name,
        message_id: A.assistantMessage.message.id,
        platform: dA.platform
      }
    })
  }, [A, B])
}
var Je  =  J1(_1(), 1);

function wD({
  title: A
}) {
  return Je.createElement(m, {
    flexDirection: "column"
  }, Je.createElement(y, {
    bold: !0,
    color: $1().permission
  }, A))
}
var Aj  =  J1(_1(), 1);
import {
  randomUUID as w25
} from "crypto";
import {
  basename as E25,
  resolve as Zr1
} from "path";

function DV2(messages) {
  if (!A) return;
  let B  =  A.find((Q)  = > Q.type === "connected" && Q.name === "ide");
  return B?.type === "connected" ? B : void 0
}

function Zd({
  onChange: messages,
  toolUseContext: systemPrompt,
  filePath: userPrompt,
  edits: tools,
  editMode: G
}) {
  let D  =  Aj.useRef(!1),
    Z  =  Aj.useMemo(()  = > w25().slice(0, 6), []),
    Y  =  Aj.useMemo(()  = > `✻ [Claude Code] ${ E25(Q) } (${ Z }) ⧉`, [Q, Z]),
    W  =  SI1(B.options.mcpClients) && VA().diffTool === "auto",
    F  =  _I1(B.options.mcpClients) ?? "IDE";
  async function J() {
    if (!W) return;
    logMetric("tengu_ext_will_show_diff", { });
    let {
      oldContent: C,
      newContent: X
    }  =  await N25(Q, I, B, Y);
    if (D.current) return;
    logMetric("tengu_ext_diff_accepted", { });
    let V  =  U25(Q, C, X, G);
    if (V.length === 0) {
      logMetric("tengu_ext_diff_rejected", { }), A("no", {
        file_path: Q,
        edits: I
      });
      return
    }
    A("yes", {
      file_path: Q,
      edits: V
    })
  }
  return Aj.useEffect(()  = > {
    return J(), ()  = > {
      D.current  =  !0
    }
  }, []), {
    closeTabInIDE() {
      let C  =  DV2(B.options.mcpClients);
      if (!C) return Promise.resolve();
      return ZV2(Y, B, C)
    },
    showingDiffInIDE: W,
    ideName: F
  }
}

function U25(messages, systemPrompt, userPrompt, tools) {
  let G  =  I === "single",
    D  =  lW2({
      filePath: A,
      oldContent: B,
      newContent: Q,
      singleHunk: G
    });
  if (D.length === 0) return [];
  if (G && D.length > 1) logError(new Error(`Unexpected number of hunks: ${ D.length }. Expected 1 hunk.`));
  return tW2(D)
}
async function N25(messages, systemPrompt, userPrompt, tools) {
  let G  =  !1,
    D  =  Zr1(uA(), A),
    Z  =  b1().existsSync(Zr1(uA(), A)) ? b1().readFileSync(A, {
      encoding: "utf8"
    }) : "";
  async function Y() {
    if (G) return;
    G  =  !0;
    try {
      await ZV2(I, Q, W)
    } catch (F) {
      logError(F)
    }
    process.off("beforeExit", Y), Q.abortController.signal.removeEventListener("abort", Y)
  }
  Q.abortController.signal.addEventListener("abort", Y), process.on("beforeExit", Y);
  let W  =  DV2(Q.options.mcpClients);
  try {
    let {
      updatedFile: F
    }  =  mm({
      filePath: A,
      fileContents: b1().existsSync(Zr1(uA(), A)) ? b1().readFileSync(A, {
        encoding: "utf8"
      }) : "",
      edits: B
    });
    if (!W || W.type !== "connected") throw new Error("IDE client not available");
    let J  =  await tM("openDiff", {
        old_file_path: D,
        new_file_path: A,
        new_file_contents: F,
        tab_name: I
      }, W, Q.options.isNonInteractiveSession),
      C  =  {
        type: "result",
        data: Array.isArray(J) ? J : [J]
      };
    if (M25(C)) return Y(), {
      oldContent: Z,
      newContent: C.data[1].text
    };
    else if ($25(C)) return Y(), {
      oldContent: Z,
      newContent: F
    };
    else if (q25(C)) return Y(), {
      oldContent: Z,
      newContent: Z
    };
    throw new Error("Not accepted")
  } catch (F) {
    throw logError(F), Y(), F
  }
}
async function ZV2(messages, systemPrompt, userPrompt) {
  try {
    if (!Q || Q.type !== "connected") throw new Error("IDE client not available");
    await tM("close_tab", {
      tab_name: A
    }, Q, B.options.isNonInteractiveSession)
  } catch (I) {
    logError(I)
  }
}

function $25(A) {
  return A.type === "result" && Array.isArray(A.data) && typeof A.data[0] === "object" && A.data[0] !== null &&
    "type" in A.data[0] && A.data[0].type === "text" && "text" in A.data[0] && A.data[0].text === "TAB_CLOSED"
}

function q25(messages) {
  return A.type === "result" && Array.isArray(A.data) && typeof A.data[0] === "object" && A.data[0] !== null &&
    "type" in A.data[0] && A.data[0].type === "text" && "text" in A.data[0] && A.data[0].text === "DIFF_REJECTED"
}

function M25(messages) {
  return A.type === "result" && Array.isArray(A.data) && A.data[0]?.type === "text" && A.data[0].text ===
    "FILE_SAVED" && typeof A.data[1].text === "string"
}
var KG  =  J1(_1(), 1);
import {
  basename as L25
} from "path";

function Yd({
  onChange: messages,
  options: systemPrompt,
  input: userPrompt,
  file_path: tools,
  ideName: G
}) {
  return KG.createElement(m, {
    flexDirection: "column",
    borderStyle: "round",
    borderColor: $1().permission,
    marginTop: 1,
    paddingLeft: 1,
    paddingRight: 1,
    paddingBottom: 1
  }, KG.createElement(m, {
    flexDirection: "column",
    padding: 1
  }, KG.createElement(y, {
    bold: !0,
    color: $1().permission
  }, "Opened changes in ", G, " ⧉"), oM && KG.createElement(y, {
    dimColor: !0
  }, "Save file to continue…")), KG.createElement(m, {
    flexDirection: "column"
  }, KG.createElement(y, null, "Do you want to make this edit to", " ", KG.createElement(y, {
    bold: !0
  }, L25(I)), "?"), KG.createElement(B9, {
    options: B,
    onChange: (D)  = > A(D, Q),
    onCancel: ()  = > A("no", Q)
  })))
}

function YV2(messages, systemPrompt) {
  if (A.mode !== "acceptEdits") return !1;
  return logMetric("tengu_auto_accept_disabled", { }), B({
    ...A,
    mode: "default"
  }), !0
}

function jC(messages, systemPrompt) {
  switch (A.mode) {
    case "acceptEdits":
    case "bypassPermissions":
      return !1;
    case "default":
      break
  }
  return logMetric("tengu_auto_accept_enabled", { }), B({
    ...A,
    mode: "acceptEdits"
  }), !0
}
var qF  =  J1(_1(), 1),
  Yr1  =  J1(_1(), 1);
import {
  relative as R25
} from "path";

function Ce({
  file_path: messages,
  edits: systemPrompt,
  verbose: userPrompt,
  useBorder: I  =  !0
}) {
  let G  =  Yr1.useMemo(()  = > b1().existsSync(A) ? dG(A) : "", [A]),
    D  =  Yr1.useMemo(()  = > wF({
      filePath: A,
      fileContents: G,
      edits: B
    }), [A, G, B]);
  return qF.createElement(m, {
    flexDirection: "column"
  }, qF.createElement(m, {
    borderColor: $1().secondaryBorder,
    borderStyle: I ? "round" : void 0,
    flexDirection: "column",
    paddingX: 1
  }, qF.createElement(m, {
    paddingBottom: 1
  }, qF.createElement(y, {
    bold: !0
  }, Q ? A : R25(uA(), A))), hY(D.map((Z)  = > qF.createElement(wZ, {
    key: Z.newStart,
    patch: Z,
    dim: !1
  })), (Z)  = > qF.createElement(y, {
    color: $1().secondaryText,
    key: `ellipsis-${ Z }`
  }, "..."))))
}

function Bj(messages) {
  let B  =  SY(A) ? [{
    label: `Yes, and don't ask again this session (${ wA.bold.ansi256(l9().secondaryText)("shift+tab") })`,
    value: "yes-dont-ask-again"
  }] : [];
  return [{
    label: "Yes",
    value: "yes"
  }, ...B, {
    label: `No, and tell Claude what to do differently (${ wA.bold.ansi256(l9().secondaryText)("esc") })`,
    value: "no"
  }]
}

function WV2({
  setToolPermissionContext: messages,
  toolUseConfirm: systemPrompt,
  toolUseContext: userPrompt,
  onDone: tools,
  onReject: signal,
  verbose: D
}) {
  let {
    file_path: Z,
    new_string: Y,
    old_string: W
  }  =  $Q.inputSchema.parse(B.input), F  =  QK.useMemo(()  = > ({
    completion_type: "str_replace_single",
    language_name: oD(Z)
  }), [Z]);
  _C(B, F), J0((K, U)  = > {
    if (U.tab && U.shift && Bj(Z).filter((N)  = > N.value === "yes-dont-ask-again").length > 0) {
      J("yes-dont-ask-again", {
        file_path: Z,
        edits: [{
          old_string: W,
          new_string: Y
        }]
      });
      return
    }
  });

  function J(result, {
    file_path: streamResponse,
    edits: N
  }) {
    C();
    let {
      old_string: q,
      new_string: M
    }  =  N[0];
    if (N.length > 1) logError(new Error("Too many edits provided - continuing with just the first edit"));
    switch (K) {
      case "yes":
        m5({
          completion_type: "str_replace_single",
          event: "accept",
          metadata: {
            language_name: oD(U),
            message_id: B.assistantMessage.message.id,
            platform: dA.platform
          }
        }), I(), B.onAllow("temporary", {
          file_path: U,
          new_string: M,
          old_string: q
        });
        break;
      case "yes-dont-ask-again":
        m5({
          completion_type: "str_replace_single",
          event: "accept",
          metadata: {
            language_name: oD(U),
            message_id: B.assistantMessage.message.id,
            platform: dA.platform
          }
        }), jC(B.toolUseContext.getToolPermissionContext(), A), I(), B.onAllow("permanent", {
          file_path: U,
          new_string: M,
          old_string: q
        });
        break;
      case "no":
        m5({
          completion_type: "str_replace_single",
          event: "reject",
          metadata: {
            language_name: oD(U),
            message_id: B.assistantMessage.message.id,
            platform: dA.platform
          }
        }), I(), G(), B.onReject();
        break
    }
  }
  let {
    closeTabInIDE: C,
    showingDiffInIDE: X,
    ideName: V
  }  =  Zd({
    onChange: J,
    toolUseContext: Q,
    filePath: Z,
    edits: [{
      old_string: W,
      new_string: Y
    }],
    editMode: "single"
  });
  if (X) return QK.default.createElement(Yd, {
    onChange: J,
    options: Bj(Z),
    file_path: Z,
    input: {
      file_path: Z,
      edits: [{
        old_string: W,
        new_string: Y
      }]
    },
    ideName: V
  });
  return QK.default.createElement(m, {
    flexDirection: "column",
    borderStyle: "round",
    borderColor: $1().permission,
    marginTop: 1,
    paddingLeft: 1,
    paddingRight: 1,
    paddingBottom: 1
  }, QK.default.createElement(wD, {
    title: "Edit file"
  }), QK.default.createElement(Ce, {
    file_path: Z,
    edits: [{
      old_string: W,
      new_string: Y
    }],
    verbose: D
  }), QK.default.createElement(m, {
    flexDirection: "column"
  }, QK.default.createElement(y, null, "Do you want to make this edit to", " ", QK.default.createElement(y, {
    bold: !0
  }, O25(Z)), "?"), QK.default.createElement(B9, {
    options: Bj(Z),
    onChange: (K)  = > J(K, {
      file_path: Z,
      edits: [{
        old_string: W,
        new_string: Y
      }]
    }),
    onCancel: ()  = > J("no", {
      file_path: Z,
      edits: [{
        old_string: W,
        new_string: Y
      }]
    })
  })))
}
var X3  =  J1(_1(), 1);
var FV2  =  J1(_1(), 1);

function LV1(messages, systemPrompt) {
  FV2.useEffect(()  = > {
    logMetric("tengu_tool_use_show_permission_request", {
      messageID: A.assistantMessage.message.id,
      toolName: A.tool.name,
      isMcp: A.tool.isMcp ?? !1
    }), Promise.resolve(B.language_name).then((I)  = > {
      m5({
        completion_type: B.completion_type,
        event: "response",
        metadata: {
          language_name: I,
          message_id: A.assistantMessage.message.id,
          platform: dA.platform
        }
      })
    })
  }, [A, B])
}

function mR(messages, {
  assistantMessage: {
    message: {
      id: B
    }
  }
}, userPrompt) {
  m5({
    completion_type: A,
    event: Q,
    metadata: {
      language_name: "none",
      message_id: B,
      platform: dA.platform
    }
  })
}

function T25(messages) {
  switch (A.length) {
    case 0:
      return "";
    case 1:
      return wA.bold(A[0]);
    case 2:
      return wA.bold(A[0]) + " and " + wA.bold(A[1]);
    default:
      return wA.bold(A.slice(0, -1).join(", ")) + ", and " + wA.bold(A.slice(-1)[0])
  }
}

function P25(messages) {
  let B  =  T25(A);
  if (B.length > 50) return "similar";
  else return B
}

function S25(messages) {
  return A.flatMap((B)  = > {
    if (!B.ruleContent) return [];
    return Vs1(B.ruleContent) ?? B.ruleContent
  })
}

function JV2({
  toolUseConfirm: A
}) {
  let {
    permissionResult: B
  }  =  A, Q  =  [], I  =  B.behavior !== "allow" ? B.ruleSuggestions : void 0;
  if (I && I.length > 0) {
    let G  =  S25(I);
    Q  =  [{
      label: `Yes, and don't ask again for ${ P25(G) } commands in ${ wA.bold(u4()) }`,
      value: "yes-dont-ask-again-prefix"
    }]
  }
  return [{
    label: "Yes",
    value: "yes"
  }, ...Q, {
    label: `No, and tell Claude what to do differently (${ wA.bold.ansi256(l9().secondaryText)("esc") })`,
    value: "no"
  }]
}
var e5  =  J1(_1(), 1);

function CV2(messages) {
  switch (A) {
    case "default":
      return "Default";
    case "acceptEdits":
      return "Accept Edits";
    case "bypassPermissions":
      return "Bypass Permissions"
  }
}

function XV2(messages) {
  switch (A) {
    case "default":
    case "acceptEdits":
      return null;
    case "bypassPermissions":
      return "Bypassing Permissions"
  }
}

function _25(messages) {
  switch (A) {
    case "cliArg":
      return "CLI argument";
    case "localSettings":
      return "local settings";
    case "projectSettings":
      return "project settings";
    case "policySettings":
      return "managed settings";
    case "userSettings":
      return "global settings"
  }
}

function VV2(messages) {
  switch (A.type) {
    case "rule":
      return `${ wA.bold(s8(A.rule.ruleValue)) } rule from ${ _25(A.rule.source) }`;
    case "mode":
      return `${ CV2(A.mode) } mode`;
    case "other":
      return A.reason;
    case "permissionPromptTool":
      return `${ wA.bold(A.permissionPromptToolName) } permission prompt tool`
  }
}

function j25({
  title: messages,
  decisionReason: B
}) {
  let Q  =  l9();

  function I() {
    switch (B.type) {
      case "subcommandResults":
        return e5.default.createElement(m, {
          flexDirection: "column"
        }, Array.from(B.reasons.entries()).map(([G, D])  = > {
          let Z  =  D.behavior === "allow" ? wA.ansi256(Q.success)(H2.tick) : wA.ansi256(Q.error)(H2.cross);
          return e5.default.createElement(m, {
              flexDirection: "column",
              key: G
            }, e5.default.createElement(y, null, Z, " ", G), D.decisionReason !== void 0 && D.decisionReason
            .type !== "subcommandResults" && e5.default.createElement(y, null, "  ", "⎿", "  ", VV2(D
              .decisionReason)), D.behavior !== "allow" && D.ruleSuggestions && e5.default.createElement(y,
              null, "  ", "⎿", "  ", "Suggested rules:", " ", D.ruleSuggestions.map((Y)  = > wA.bold(s8(Y))).join(
                ", ")))
        }));
      default:
        return e5.default.createElement(y, null, VV2(B))
    }
  }
  return e5.default.createElement(m, {
    flexDirection: "column"
  }, A && e5.default.createElement(y, null, A), I())
}

function KV2({
  permissionResult: A
}) {
  let B  =  A.decisionReason,
    Q  =  A.behavior !== "allow" ? A.ruleSuggestions : void 0,
    I  =  10;
  return e5.default.createElement(m, {
      flexDirection: "column"
    }, e5.default.createElement(m, {
      flexDirection: "row"
    }, e5.default.createElement(m, {
      justifyContent: "flex-end",
      minWidth: 10
    }, e5.default.createElement(y, {
      dimColor: !0
    }, "Behavior ")), e5.default.createElement(y, null, A.behavior)), A.behavior !== "allow" && e5.default
    .createElement(m, {
      flexDirection: "row"
    }, e5.default.createElement(m, {
      justifyContent: "flex-end",
      minWidth: 10
    }, e5.default.createElement(y, {
      dimColor: !0
    }, "Message ")), e5.default.createElement(y, null, A.message)), e5.default.createElement(m, {
      flexDirection: "row"
    }, e5.default.createElement(m, {
      justifyContent: "flex-end",
      minWidth: 10
    }, e5.default.createElement(y, {
      dimColor: !0
    }, "Reason ")), B === void 0 ? e5.default.createElement(y, null, "undefined") : e5.default.createElement(j25, {
      decisionReason: B
    })), e5.default.createElement(m, {
      flexDirection: "row"
    }, e5.default.createElement(m, {
      flexDirection: "column",
      alignItems: "flex-end",
      minWidth: 10
    }, e5.default.createElement(y, {
      dimColor: !0
    }, "Suggested "), e5.default.createElement(y, {
      dimColor: !0
    }, "rules ")), Q  ==  null || Q.length === 0 ? e5.default.createElement(y, null, "None") : Q.map((G, D)  = > e5
      .default.createElement(y, {
        key: D
      }, H2.bullet, " ", s8(G)))))
}

function HV2({
  setToolPermissionContext: messages,
  toolUseConfirm: systemPrompt,
  onDone: userPrompt,
  onReject: I
}) {
  let G  =  $1(),
    {
      command: D,
      description: Z
    }  =  N4.inputSchema.parse(B.input),
    [Y, W]  =  X3.useState(!1),
    F  =  X3.useMemo(()  = > ({
      completion_type: "tool_use_single",
      language_name: "none"
    }), []);
  LV1(B, F);
  let J  =  X3.useMemo(()  = > JV2({
    toolUseConfirm: B
  }), [B]);
  J0((X, V)  = > {
    if (V.ctrl && X === "d") W((K)  = > !K)
  });

  function C(wasCompacted) {
    switch (X) {
      case "yes":
        mR("tool_use_single", B, "accept"), B.onAllow("temporary", B.input), Q();
        break;
      case "yes-dont-ask-again-prefix": {
        mR("tool_use_single", B, "accept");
        let V  =  B.permissionResult.behavior !== "allow" ? B.permissionResult.ruleSuggestions : void 0;
        if (V) xr({
          ruleValues: V,
          ruleBehavior: "allow",
          destination: "localSettings",
          initialContext: B.toolUseContext.getToolPermissionContext(),
          setToolPermissionContext: A
        }).then(()  = > {
          B.onAllow("permanent", B.input), Q()
        });
        else B.onAllow("temporary", B.input), Q();
        break
      }
      case "no":
        mR("tool_use_single", B, "reject"), B.onReject(), I(), Q();
        break
    }
  }
  return X3.default.createElement(m, {
    flexDirection: "column",
    borderStyle: "round",
    borderColor: G.permission,
    marginTop: 1,
    paddingLeft: 1,
    paddingRight: 1
  }, X3.default.createElement(wD, {
    title: "Bash command"
  }), X3.default.createElement(m, {
    flexDirection: "column",
    paddingX: 2,
    paddingY: 1
  }, X3.default.createElement(y, null, N4.renderToolUseMessage({
    command: D,
    description: Z
  }, {
    verbose: !0
  })), X3.default.createElement(y, {
    color: G.secondaryText
  }, B.description)), Y ? X3.default.createElement(X3.default.Fragment, null, X3.default.createElement(KV2, {
    permissionResult: B.permissionResult
  }), B.toolUseContext.options.debug && X3.default.createElement(m, {
    justifyContent: "flex-end",
    marginTop: 1
  }, X3.default.createElement(y, {
    dimColor: !0
  }, "Ctrl-D to hide debug info"))) : X3.default.createElement(X3.default.Fragment, null, X3.default.createElement(
    m, {
      flexDirection: "column"
    }, X3.default.createElement(y, null, "Do you want to proceed?"), X3.default.createElement(B9, {
      options: J,
      onChange: C,
      onCancel: ()  = > C("no")
    })), B.toolUseContext.options.debug && X3.default.createElement(m, {
    justifyContent: "flex-end"
  }, X3.default.createElement(y, {
    dimColor: !0
  }, "Ctrl-D to show debug info"))))
}
var MF  =  J1(_1(), 1);

function RV1({
  setToolPermissionContext: messages,
  toolUseConfirm: systemPrompt,
  onDone: userPrompt,
  onReject: tools,
  verbose: G
}) {
  let D  =  $1(),
    Z  =  B.tool.userFacingName(B.input),
    Y  =  Z.endsWith(" (MCP)") ? Z.slice(0, -6) : Z,
    W  =  MF.useMemo(()  = > ({
      completion_type: "tool_use_single",
      language_name: "none"
    }), []);
  _C(B, W);
  let F  =  (X)  = > {
      switch (X) {
        case "yes":
          m5({
            completion_type: "tool_use_single",
            event: "accept",
            metadata: {
              language_name: "none",
              message_id: B.assistantMessage.message.id,
              platform: dA.platform
            }
          }), B.onAllow("temporary", B.input), Q();
          break;
        case "yes-dont-ask-again":
          m5({
            completion_type: "tool_use_single",
            event: "accept",
            metadata: {
              language_name: "none",
              message_id: B.assistantMessage.message.id,
              platform: dA.platform
            }
          }), KF1({
            rule: {
              ruleBehavior: "allow",
              ruleValue: {
                toolName: B.tool.name
              },
              source: "localSettings"
            },
            initialContext: B.toolUseContext.getToolPermissionContext(),
            setToolPermissionContext: A
          }).then(()  = > {
            B.onAllow("permanent", B.input), Q()
          });
          break;
        case "no":
          m5({
            completion_type: "tool_use_single",
            event: "reject",
            metadata: {
              language_name: "none",
              message_id: B.assistantMessage.message.id,
              platform: dA.platform
            }
          }), B.onReject(), I(), Q();
          break
      }
    },
    J  =  u4(),
    C  =  MF.useMemo(()  = > {
      return [{
        label: "Yes",
        value: "yes"
      }, {
        label: `Yes, and don't ask again for ${ wA.bold(Y) } commands in ${ wA.bold(J) }`,
        value: "yes-dont-ask-again"
      }, {
        label: `No, and tell Claude what to do differently (${ wA.bold.ansi256(l9().secondaryText)("esc") })`,
        value: "no"
      }]
    }, [Y, J]);
  return MF.default.createElement(m, {
    flexDirection: "column",
    borderStyle: "round",
    borderColor: $1().permission,
    marginTop: 1,
    paddingLeft: 1,
    paddingRight: 1,
    paddingBottom: 1
  }, MF.default.createElement(wD, {
    title: "Tool use"
  }), MF.default.createElement(m, {
    flexDirection: "column",
    paddingX: 2,
    paddingY: 1
  }, MF.default.createElement(y, null, Y, "(", B.tool.renderToolUseMessage(B.input, {
    verbose: G
  }), ")", Z.endsWith(" (MCP)") ? MF.default.createElement(y, {
    color: D.secondaryText
  }, " (MCP)") : ""), MF.default.createElement(y, {
    color: D.secondaryText
  }, B.description)), MF.default.createElement(m, {
    flexDirection: "column"
  }, MF.default.createElement(y, null, "Do you want to proceed?"), MF.default.createElement(B9, {
    options: C,
    onChange: F,
    onCancel: ()  = > F("no")
  })))
}
var Wr1  =  J1(_1(), 1);
var Fr1  =  6000;

function y25() {
  return Date.now() - u21()
}

function k25(messages) {
  return y25() < A
}

function x25(messages) {
  return !k25(A)
}
var f25  =  b0(()  = > process.stdin.on("data", Op));

function Jr1(messages, B  =  Fr1) {
  Wr1.useEffect(()  = > {
    f25(), Op()
  }, []), Wr1.useEffect(()  = > {
    let Q  =  !1,
      I  =  setInterval(()  = > {
        if (x25(B) && !Q) Q  =  !0, Ad({
          message: A
        })
      }, B);
    return ()  = > clearTimeout(I)
  }, [A, B])
}
var cY  =  J1(_1(), 1);
import {
  basename as g25
} from "path";
var LF  =  J1(_1(), 1),
  OV1  =  J1(_1(), 1);
import {
  extname as v25,
  relative as b25
} from "path";

function Cr1({
  file_path: messages,
  content: systemPrompt,
  verbose: Q
}) {
  let I  =  OV1.useMemo(()  = > b1().existsSync(A), [A]),
    G  =  OV1.useMemo(()  = > {
      if (!I) return "";
      let Z  =  nI(A);
      return b1().readFileSync(A, {
        encoding: Z
      })
    }, [A, I]),
    D  =  OV1.useMemo(()  = > {
      if (!I) return null;
      return wF({
        filePath: A,
        fileContents: G,
        edits: [{
          old_string: G,
          new_string: B
        }]
      })
    }, [I, A, G, B]);
  return LF.createElement(m, {
    borderColor: $1().secondaryBorder,
    borderStyle: "round",
    flexDirection: "column",
    paddingX: 1
  }, LF.createElement(m, {
    paddingBottom: 1
  }, LF.createElement(y, {
    bold: !0
  }, Q ? A : b25(uA(), A))), D ? hY(D.map((Z)  = > LF.createElement(wZ, {
    key: Z.newStart,
    patch: Z,
    dim: !1
  })), (Z)  = > LF.createElement(y, {
    color: $1().secondaryText,
    key: `ellipsis-${ Z }`
  }, "...")) : LF.createElement(CC, {
    code: B || "(No content)",
    language: v25(A).slice(1)
  }))
}

function Xr1(messages) {
  return [{
    label: "Yes",
    value: "yes"
  }, ...SY(A) ? [{
    label: `Yes, and don't ask again this session (${ wA.bold.ansi256(l9().secondaryText)("shift+tab") })`,
    value: "yes-dont-ask-again"
  }] : [], {
    label: `No, and tell Claude what to do differently (${ wA.bold.ansi256(l9().secondaryText)("esc") })`,
    value: "no"
  }]
}

function zV2({
  setToolPermissionContext: messages,
  toolUseConfirm: systemPrompt,
  toolUseContext: userPrompt,
  onDone: tools,
  onReject: signal,
  verbose: D
}) {
  let {
    file_path: Z,
    content: Y
  }  =  HD.inputSchema.parse(B.input), W  =  cY.useMemo(()  = > b1().existsSync(Z), [Z]), F  =  cY.useMemo(()  = > ({
    completion_type: "write_file_single",
    language_name: oD(Z)
  }), [Z]);
  _C(B, F);

  function J(streamResponse, {
    file_path: skipPermissionCheck,
    content: q
  }) {
    switch (X(), U) {
      case "yes":
        m5({
          completion_type: "write_file_single",
          event: "accept",
          metadata: {
            language_name: oD(N),
            message_id: B.assistantMessage.message.id,
            platform: dA.platform
          }
        }), B.onAllow("temporary", {
          file_path: N,
          content: q
        }), I();
        break;
      case "yes-dont-ask-again":
        m5({
          completion_type: "write_file_single",
          event: "accept",
          metadata: {
            language_name: oD(N),
            message_id: B.assistantMessage.message.id,
            platform: dA.platform
          }
        }), jC(B.toolUseContext.getToolPermissionContext(), A), I(), B.onAllow("permanent", {
          file_path: N,
          content: q
        });
        break;
      case "no":
        m5({
          completion_type: "write_file_single",
          event: "reject",
          metadata: {
            language_name: oD(N),
            message_id: B.assistantMessage.message.id,
            platform: dA.platform
          }
        }), B.onReject(), G(), I();
        break
    }
  }
  J0((U, N)  = > {
    if (N.tab && N.shift && Xr1(Z).filter((q)  = > q.value === "yes-dont-ask-again").length > 0) J(
      "yes-dont-ask-again", {
        file_path: Z,
        content: Y
      })
  });
  let C  =  cY.useMemo(()  = > b1().existsSync(Z) ? dG(Z) : "", [Z]),
    {
      closeTabInIDE: X,